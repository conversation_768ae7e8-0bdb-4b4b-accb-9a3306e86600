//
//  ContentView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var supabaseService = SupabaseService.shared
    @EnvironmentObject var trackingManager: TrackingManager
    @State private var showSplash = true
    @State private var isInitialized = false

    @AppStorage("hasSeenOnboarding") private var hasSeenOnboarding = false

    var body: some View {
        ZStack {
            if showSplash {
                SplashView()
                    .transition(.opacity)
            } else {
                Group {
                    if !hasSeenOnboarding {
                        OnboardingView()
                    } else if supabaseService.isAuthenticated {
                        MainTabView()
                    } else {
                        AuthView()
                    }
                }
                .transition(.opacity)

            }
        }
        .onAppear {
            initializeApp()

            // Uygulama açılır açılmaz ATT iznini sor
            if trackingManager.shouldShowTrackingRequest() {
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    Task {
                        await trackingManager.showTrackingDialog()
                    }
                }
            }
        }
        .animation(.easeInOut(duration: 0.5), value: showSplash)
    }

    private func initializeApp() {
        // Simulate app initialization processes
        Task {
            // Wait for animations to complete and show the app
            try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds

            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.8)) {
                    showSplash = false
                    isInitialized = true
                }
            }
        }
    }
}

#Preview {
    ContentView()
}
