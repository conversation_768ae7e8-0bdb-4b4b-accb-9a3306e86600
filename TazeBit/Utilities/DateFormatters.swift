//
//  DateFormatters.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 28.05.2025.
//

import Foundation

extension DateFormatter {
    static let turkishShort: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "tr_TR")
        return formatter
    }()
    
    static let turkishMedium: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "tr_TR")
        return formatter
    }()
    
    static let turkishLong: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .long
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "tr_TR")
        return formatter
    }()
    
    static let turkishRelative: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "tr_TR")
        formatter.doesRelativeDateFormatting = true
        return formatter
    }()
    
    static let turkishCustom: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d MMMM yyyy"
        formatter.locale = Locale(identifier: "tr_TR")
        return formatter
    }()
}

// MARK: - Date Helper Functions
extension Date {
    func turkishShortString() -> String {
        return DateFormatter.turkishShort.string(from: self)
    }
    
    func turkishMediumString() -> String {
        return DateFormatter.turkishMedium.string(from: self)
    }
    
    func turkishLongString() -> String {
        return DateFormatter.turkishLong.string(from: self)
    }
    
    func turkishRelativeString() -> String {
        return DateFormatter.turkishRelative.string(from: self)
    }
    
    func turkishCustomString() -> String {
        return DateFormatter.turkishCustom.string(from: self)
    }
}
