//
//  ProductViewModel.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> Ko<PERSON>ma<PERSON> on 26.05.2025.
//

import Foundation
import SwiftUI

@MainActor
class ProductViewModel: ObservableObject {
    @Published var products: [Product] = []
    @Published var isLoading = false
    @Published var errorMessage = ""
    @Published var showingAddProduct = false
    @Published var editingProduct: Product?
    @Published var showingDeleteConfirmation = false
    @Published var productToDelete: Product?

    // Add/Edit product form
    @Published var productName = ""
    @Published var selectedCategory: ProductCategory = .other
    @Published var selectedStorageLocation: StorageLocation = .refrigerator
    @Published var expiryDate = Date()
    @Published var purchaseDate = Date()
    @Published var quantity = 1
    @Published var selectedUnit: ProductUnit = .piece
    @Published var notes = ""
    @Published var caloriesPer100g = ""
    @Published var includePurchaseDate = false

    private let supabaseService = SupabaseService.shared
    private let notificationManager = NotificationManager.shared

    init() {
        // Alışveriş listesi öğesi "alınmadı" olarak işaretlendiğinde dinle
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("ShoppingItemUnpurchased"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let itemId = notification.userInfo?["itemId"] as? UUID {
                Task {
                    await self?.handleShoppingItemUnpurchased(itemId: itemId)
                }
            }
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    var expiredProducts: [Product] {
        products.filter { $0.currentQuantity > 0 && $0.expiryStatus == .expired }
    }

    var expiringSoonProducts: [Product] {
        products.filter { $0.currentQuantity > 0 && $0.expiryStatus == .expiringSoon }
    }

    var expiringThisWeekProducts: [Product] {
        products.filter { $0.currentQuantity > 0 && $0.expiryStatus == .expiringThisWeek }
    }

    var freshProducts: [Product] {
        products.filter { $0.currentQuantity > 0 && $0.expiryStatus == .fresh }
    }

    func fetchProducts(for householdId: UUID) async {
        isLoading = true
        errorMessage = ""

        do {
            products = try await supabaseService.fetchProducts(for: householdId)

            // Ürünler yüklendikten sonra bildirimleri güncelle
            await scheduleNotificationsForProducts()
        } catch {
            errorMessage = "Ürünler yüklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func addProduct(to householdId: UUID) async {
        guard !productName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = "Ürün adı boş olamaz"
            return
        }

        guard let userId = supabaseService.client.auth.currentUser?.id else {
            errorMessage = "Kullanıcı oturumu bulunamadı"
            return
        }

        isLoading = true
        errorMessage = ""

        do {
            let product = Product(
                id: UUID(),
                householdId: householdId,
                name: productName.trimmingCharacters(in: .whitespacesAndNewlines),
                category: selectedCategory.rawValue,
                storageLocation: selectedStorageLocation.rawValue,
                expiryDate: expiryDate,
                purchaseDate: includePurchaseDate ? purchaseDate : nil,
                originalQuantity: quantity,
                currentQuantity: quantity,
                unit: selectedUnit.rawValue,
                notes: notes.isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines),
                caloriesPer100g: caloriesPer100g.isEmpty ? nil : Int(caloriesPer100g),
                addedBy: userId,
                shoppingListItemId: nil, // Normal ürün ekleme
                createdAt: Date(),
                updatedAt: Date()
            )

            // Add product to database
            _ = try await supabaseService.addProduct(product)

            // Refresh the products list from database
            await fetchProducts(for: householdId)

            clearForm()
            showingAddProduct = false

        } catch {
            errorMessage = "Ürün eklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func addProductFromShoppingList(to householdId: UUID, shoppingListItemId: UUID) async {
        guard let userId = supabaseService.client.auth.currentUser?.id else {
            errorMessage = "Kullanıcı oturumu bulunamadı"
            return
        }

        isLoading = true
        errorMessage = ""

        do {
            let product = Product(
                id: UUID(),
                householdId: householdId,
                name: productName.trimmingCharacters(in: .whitespacesAndNewlines),
                category: selectedCategory.rawValue,
                storageLocation: selectedStorageLocation.rawValue,
                expiryDate: expiryDate,
                purchaseDate: includePurchaseDate ? purchaseDate : nil,
                originalQuantity: quantity,
                currentQuantity: quantity,
                unit: selectedUnit.rawValue,
                notes: notes.isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines),
                caloriesPer100g: caloriesPer100g.isEmpty ? nil : Int(caloriesPer100g),
                addedBy: userId,
                shoppingListItemId: shoppingListItemId, // Alışveriş listesi ilişkisi
                createdAt: Date(),
                updatedAt: Date()
            )

            // Add product to database
            _ = try await supabaseService.addProduct(product)

            // Refresh the products list from database
            await fetchProducts(for: householdId)

            clearForm()
            showingAddProduct = false
        } catch {
            errorMessage = "Ürün eklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func updateProduct() async {
        guard let product = editingProduct else { return }

        guard !productName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            await MainActor.run {
                errorMessage = "Ürün adı boş olamaz"
            }
            return
        }

        await MainActor.run {
            isLoading = true
            errorMessage = ""
        }

        do {
            // Calculate the difference in original quantity
            let quantityDifference = quantity - product.originalQuantity
            let newCurrentQuantity = max(0, product.currentQuantity + quantityDifference)

            let updatedProduct = Product(
                id: product.id,
                householdId: product.householdId,
                name: productName.trimmingCharacters(in: .whitespacesAndNewlines),
                category: selectedCategory.rawValue,
                storageLocation: selectedStorageLocation.rawValue,
                expiryDate: expiryDate,
                purchaseDate: includePurchaseDate ? purchaseDate : nil,
                originalQuantity: quantity,
                currentQuantity: newCurrentQuantity, // Adjust current quantity based on difference
                unit: selectedUnit.rawValue,
                notes: notes.isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines),
                caloriesPer100g: caloriesPer100g.isEmpty ? nil : Int(caloriesPer100g),
                addedBy: product.addedBy,
                shoppingListItemId: product.shoppingListItemId, // Preserve existing relationship
                createdAt: product.createdAt,
                updatedAt: Date()
            )

            // Update in database first
            try await supabaseService.updateProduct(updatedProduct)

            // Clear form and state on main thread
            await MainActor.run {
                clearForm()
                editingProduct = nil
                print("✅ Product updated successfully: \(updatedProduct.name)")
            }

            // Refresh the entire products list to ensure consistency
            await fetchProducts(for: updatedProduct.householdId)

            // Bildirimleri güncelle
            await scheduleNotificationsForProducts()

        } catch {
            await MainActor.run {
                errorMessage = "Ürün güncellenemedi: \(error.localizedDescription)"
            }
            print("❌ Error updating product: \(error)")
        }

        await MainActor.run {
            isLoading = false
        }
    }

    func showDeleteConfirmation(for product: Product) {
        productToDelete = product
        showingDeleteConfirmation = true
    }

    func deleteProduct(_ product: Product) async {
        await MainActor.run {
            isLoading = true
            errorMessage = ""
        }

        do {
            try await supabaseService.deleteProduct(id: product.id)

            await MainActor.run {
                // Remove from local array immediately on main thread
                products.removeAll { $0.id == product.id }

                // Clear delete confirmation state
                productToDelete = nil
                showingDeleteConfirmation = false
            }

            // Silinen ürün için bildirimleri iptal et
            notificationManager.cancelNotification(for: product.id)

            print("✅ Product deleted successfully: \(product.name)")
        } catch {
            await MainActor.run {
                errorMessage = "Ürün silinemedi: \(error.localizedDescription)"
            }
            print("❌ Error deleting product: \(error)")
        }

        await MainActor.run {
            isLoading = false
        }
    }

    func confirmDelete() async {
        guard let product = productToDelete else { return }
        let householdId = product.householdId

        // Clear any previous error messages
        errorMessage = ""

        await deleteProduct(product)

        // Only refresh if deletion was successful (no error message)
        if errorMessage.isEmpty {
            // Refresh the products list to ensure UI is updated
            await fetchProducts(for: householdId)
            print("✅ Product list refreshed after deletion")
        }
    }

    func showAddProduct() {
        clearForm()
        showingAddProduct = true
    }

    func addDetectedProduct(_ detectedProduct: DetectedProduct, to householdId: UUID) async {
        guard let userId = supabaseService.client.auth.currentUser?.id else {
            errorMessage = "Kullanıcı oturumu bulunamadı"
            return
        }

        // DetectedProduct'ı Product'a çevir
        let product = Product(
            id: UUID(),
            householdId: householdId,
            name: detectedProduct.name,
            category: detectedProduct.productCategory.rawValue,
            storageLocation: detectedProduct.productLocation.rawValue, // AI'dan gelen saklama yeri
            expiryDate: detectedProduct.expiryDate, // AI'dan gelen akıllı son kullanma tarihi
            purchaseDate: Date(),
            originalQuantity: detectedProduct.estimatedQuantity,
            currentQuantity: detectedProduct.estimatedQuantity,
            unit: detectedProduct.productUnit.rawValue,
            notes: detectedProduct.description,
            caloriesPer100g: nil, // AI'dan kalori bilgisi henüz gelmiyor
            addedBy: userId,
            shoppingListItemId: nil,
            createdAt: Date(),
            updatedAt: Date()
        )

        do {
            // Add product to database
            _ = try await supabaseService.addProduct(product)
            print("✅ AI detected product added: \(detectedProduct.name)")
        } catch {
            errorMessage = "AI ürünü eklenemedi: \(error.localizedDescription)"
            print("❌ Error adding AI detected product: \(error)")
        }
    }

    func editProduct(_ product: Product) {
        editingProduct = product
        productName = product.name
        selectedCategory = ProductCategory(rawValue: product.category ?? "other") ?? .other
        selectedStorageLocation = StorageLocation(rawValue: product.storageLocation ?? "refrigerator") ?? .refrigerator
        expiryDate = product.expiryDate
        purchaseDate = product.purchaseDate ?? Date()
        includePurchaseDate = product.purchaseDate != nil
        quantity = product.originalQuantity
        selectedUnit = ProductUnit(rawValue: product.unit ?? "piece") ?? .piece
        notes = product.notes ?? ""
        caloriesPer100g = product.caloriesPer100g != nil ? String(product.caloriesPer100g!) : ""
    }

    private func clearForm() {
        productName = ""
        selectedCategory = .other
        selectedStorageLocation = .refrigerator
        expiryDate = Date()
        purchaseDate = Date()
        quantity = 1
        selectedUnit = .piece
        notes = ""
        caloriesPer100g = ""
        includePurchaseDate = false
        errorMessage = ""
    }

    // MARK: - Notification Management

    func scheduleNotificationsForProducts() async {
        // Sadece aktif (miktarı 0'dan büyük) ürünler için bildirim planla
        let activeProducts = products.filter { $0.currentQuantity > 0 }
        notificationManager.scheduleExpiryNotifications(for: activeProducts)
    }

    func requestNotificationPermission() async {
        await notificationManager.requestAuthorization()
    }

    // MARK: - Shopping List Integration

    private func handleShoppingItemUnpurchased(itemId: UUID) async {
        // Alışveriş listesi öğesi ile ilişkili ürünleri products listesinden kaldır
        products.removeAll { product in
            product.shoppingListItemId == itemId
        }

        // Bildirimleri güncelle
        await scheduleNotificationsForProducts()
    }
}
