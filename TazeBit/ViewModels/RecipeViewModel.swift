//
//  RecipeViewModel.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 29.05.2025.
//

import Foundation

@MainActor
class RecipeViewModel: ObservableObject {
    @Published var suggestedRecipes: [Recipe] = []
    @Published var favoriteRecipes: [Recipe] = []
    @Published var isLoading = false
    @Published var errorMessage = ""
    @Published var lastGeneratedAt: Date?
    @Published var missingIngredients: [String] = []
    @Published var suggestions: [String] = []

    // Recipe preferences
    @Published var selectedDifficulty: RecipeDifficulty?
    @Published var maxCookingTime: Int = 60 // minutes
    @Published var selectedCuisine: CuisineType?
    @Published var excludedIngredients: [String] = []

    private let openAIService = OpenAIService.shared
    private let userDefaults = UserDefaults.standard
    private let supabaseService = SupabaseService.shared
    private var currentUserId: UUID?

    init() {
        loadPreferences()
        Task {
            await loadCurrentUser()
            await loadFavoriteRecipes()
        }
    }

    // MARK: - Recipe Generation

    func generateRecipesForExpiringProducts(_ products: [Product]) async {
        isLoading = true
        errorMessage = ""

        do {
            let expiringIngredients = getExpiringIngredients(from: products)
            let allIngredients = getAllAvailableIngredients(from: products)

            guard !allIngredients.isEmpty else {
                errorMessage = "Tarif önerisi için en az bir ürün gerekli"
                isLoading = false
                return
            }

            let request = RecipeRequest(
                availableIngredients: allIngredients,
                expiringIngredients: expiringIngredients,
                preferences: createPreferences(),
                maxRecipes: Config.maxRecipeSuggestions
            )

            let response = try await openAIService.generateRecipes(for: request)

            suggestedRecipes = response.recipes
            missingIngredients = response.missingIngredients
            suggestions = response.suggestions
            lastGeneratedAt = response.generatedAt

        } catch {
            errorMessage = "Tarif önerileri alınamadı: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func generateQuickRecipe(for ingredients: [String]) async {
        isLoading = true
        errorMessage = ""

        do {
            if let recipe = try await openAIService.generateQuickRecipe(ingredients: ingredients) {
                suggestedRecipes = [recipe]
                lastGeneratedAt = Date()
            } else {
                errorMessage = "Hızlı tarif oluşturulamadı"
            }
        } catch {
            errorMessage = "Hızlı tarif alınamadı: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func refreshRecipes(for products: [Product]) async {
        await generateRecipesForExpiringProducts(products)
    }



    // MARK: - Favorites Management

    func toggleFavorite(_ recipe: Recipe) {
        Task {
            await toggleFavoriteAsync(recipe)
        }
    }

    @MainActor
    private func toggleFavoriteAsync(_ recipe: Recipe) async {
        guard let userId = currentUserId else {
            print("❌ No current user ID available")
            return
        }

        do {
            if favoriteRecipes.contains(where: { $0.name == recipe.name }) {
                // Remove from favorites
                try await supabaseService.removeFavoriteRecipe(userId: userId, recipeName: recipe.name)
                favoriteRecipes.removeAll { $0.name == recipe.name }
                print("✅ Removed '\(recipe.name)' from favorites")
            } else {
                // Add to favorites
                let favoriteRecipe = try FavoriteRecipe(from: recipe, userId: userId)
                _ = try await supabaseService.addFavoriteRecipe(favoriteRecipe)

                var updatedRecipe = recipe
                updatedRecipe = Recipe(
                    id: updatedRecipe.id,
                    name: updatedRecipe.name,
                    difficulty: updatedRecipe.difficulty,
                    preparationTime: updatedRecipe.preparationTime,
                    cookingTime: updatedRecipe.cookingTime,
                    servings: updatedRecipe.servings,
                    ingredients: updatedRecipe.ingredients,
                    instructions: updatedRecipe.instructions,
                    tips: updatedRecipe.tips,
                    cuisine: updatedRecipe.cuisine,
                    tags: updatedRecipe.tags,
                    createdAt: updatedRecipe.createdAt,
                    isFavorite: true
                )
                favoriteRecipes.append(updatedRecipe)
                print("✅ Added '\(recipe.name)' to favorites")
            }
        } catch {
            print("❌ Error toggling favorite: \(error)")
        }
    }

    func isFavorite(_ recipe: Recipe) -> Bool {
        return favoriteRecipes.contains { $0.name == recipe.name }
    }

    // MARK: - Preferences

    func updatePreferences(
        difficulty: RecipeDifficulty? = nil,
        maxTime: Int? = nil,
        cuisine: CuisineType? = nil,
        excludedIngredients: [String]? = nil
    ) {
        if let difficulty = difficulty {
            selectedDifficulty = difficulty
        }
        if let maxTime = maxTime {
            maxCookingTime = maxTime
        }
        if let cuisine = cuisine {
            selectedCuisine = cuisine
        }
        if let excluded = excludedIngredients {
            self.excludedIngredients = excluded
        }

        savePreferences()
    }

    func resetPreferences() {
        selectedDifficulty = nil
        maxCookingTime = 60
        selectedCuisine = nil
        excludedIngredients = []
        savePreferences()
    }

    // MARK: - Helper Methods

    func getExpiringProducts(from products: [Product]) -> [Product] {
        let calendar = Calendar.current
        let today = Date()

        return products.filter { product in
            // Sadece aktif (miktarı 0'dan büyük) ürünleri kontrol et
            guard product.currentQuantity > 0 else { return false }

            let daysUntilExpiry = calendar.dateComponents([.day], from: today, to: product.expiryDate).day ?? 0
            return daysUntilExpiry <= Config.nearExpiryDays && daysUntilExpiry >= 0
        }
    }

    func getUrgentProducts(from products: [Product]) -> [Product] {
        let calendar = Calendar.current
        let today = Date()

        return products.filter { product in
            // Sadece aktif (miktarı 0'dan büyük) ürünleri kontrol et
            guard product.currentQuantity > 0 else { return false }

            let daysUntilExpiry = calendar.dateComponents([.day], from: today, to: product.expiryDate).day ?? 0
            return daysUntilExpiry <= Config.urgentExpiryDays && daysUntilExpiry >= 0
        }
    }

    private func getExpiringIngredients(from products: [Product]) -> [String] {
        return getExpiringProducts(from: products).map { $0.name }
    }

    private func getAllAvailableIngredients(from products: [Product]) -> [String] {
        return products.filter { $0.currentQuantity > 0 }.map { $0.name }
    }

    private func createPreferences() -> RecipePreferences {
        return RecipePreferences(
            difficulty: selectedDifficulty,
            maxTime: maxCookingTime,
            cuisine: selectedCuisine,
            excludeIngredients: excludedIngredients,
            dietaryRestrictions: []
        )
    }

    // MARK: - Persistence

    private func loadCurrentUser() async {
        do {
            let session = try await supabaseService.client.auth.session
            currentUserId = session.user.id
            print("✅ Current user loaded: \(session.user.id)")
        } catch {
            print("❌ Error loading current user: \(error)")
        }
    }

    private func loadFavoriteRecipes() async {
        guard let userId = currentUserId else {
            print("❌ No user ID available for loading favorites")
            return
        }

        do {
            let favoriteRecipeData = try await supabaseService.getFavoriteRecipes(for: userId)
            let recipes = try favoriteRecipeData.compactMap { try $0.toRecipe() }

            await MainActor.run {
                self.favoriteRecipes = recipes
                print("✅ Loaded \(recipes.count) favorite recipes for user")
            }
        } catch {
            print("❌ Error loading favorite recipes: \(error)")
        }
    }

    private func savePreferences() {
        userDefaults.set(selectedDifficulty?.rawValue, forKey: "recipeDifficulty")
        userDefaults.set(maxCookingTime, forKey: "maxCookingTime")
        userDefaults.set(selectedCuisine?.rawValue, forKey: "recipeCuisine")
        userDefaults.set(excludedIngredients, forKey: "excludedIngredients")
    }

    private func loadPreferences() {
        if let difficultyRaw = userDefaults.string(forKey: "recipeDifficulty") {
            selectedDifficulty = RecipeDifficulty(rawValue: difficultyRaw)
        }
        maxCookingTime = userDefaults.integer(forKey: "maxCookingTime")
        if maxCookingTime == 0 { maxCookingTime = 60 }

        if let cuisineRaw = userDefaults.string(forKey: "recipeCuisine") {
            selectedCuisine = CuisineType(rawValue: cuisineRaw)
        }
        excludedIngredients = userDefaults.stringArray(forKey: "excludedIngredients") ?? []
    }

    // MARK: - Shopping List Integration

    func createShoppingListForRecipe(_ recipe: Recipe, householdId: UUID) async -> Bool {
        guard !recipe.missingIngredients.isEmpty else { return false }

        do {
            let supabaseService = SupabaseService.shared

            // Get current user ID
            guard let currentUser = try? await supabaseService.client.auth.session.user else {
                print("❌ No authenticated user found")
                return false
            }

            // Create shopping list with recipe name
            let shoppingList = ShoppingList(
                id: UUID(),
                name: recipe.name,
                description: "AI tarafından '\(recipe.name)' tarifi için oluşturuldu",
                householdId: householdId,
                createdBy: currentUser.id,
                isCompleted: false,
                createdAt: Date(),
                updatedAt: Date()
            )

            // Create shopping list via Supabase
            let createdList = try await supabaseService.createShoppingList(shoppingList)

            // Add missing ingredients as shopping list items
            for ingredient in recipe.missingIngredients {
                // Parse quantity from amount string (e.g., "500" from "500 gr")
                let quantityString = ingredient.amount.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                let quantity = Double(quantityString) ?? 1.0

                let shoppingItem = ShoppingListItem(
                    id: UUID(),
                    shoppingListId: createdList.id,
                    name: ingredient.name,
                    quantity: quantity,
                    unit: ingredient.unit,
                    category: nil, // We don't have category info from recipe ingredients
                    notes: "Tarif: \(recipe.name)",
                    isPurchased: false,
                    isAddedToHouse: false,
                    addedToHouseAt: nil,
                    createdAt: Date(),
                    updatedAt: Date()
                )

                _ = try await supabaseService.createShoppingListItem(shoppingItem)
            }

            print("🛒 Successfully created shopping list '\(recipe.name)' with \(recipe.missingIngredientsCount) items")

            return true
        } catch {
            print("❌ Error creating shopping list for recipe: \(error)")
            return false
        }
    }

    func addMissingIngredientsToShoppingList() -> [String] {
        // Bu fonksiyon eksik malzemeleri döndürür
        // ShoppingListViewModel ile entegre edilecek
        return missingIngredients
    }

    // MARK: - Helper Functions

    private func updateMockRecipeAvailability(recipes: [Recipe], availableIngredients: [String]) -> [Recipe] {
        let availableIngredientsLower = Set(availableIngredients.map { $0.lowercased().trimmingCharacters(in: .whitespacesAndNewlines) })

        return recipes.map { recipe in
            let updatedIngredients = recipe.ingredients.map { ingredient in
                let ingredientNameLower = ingredient.name.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

                // Check if ingredient is available (exact match or contains)
                let isAvailable = availableIngredientsLower.contains(ingredientNameLower) ||
                                availableIngredientsLower.contains { available in
                                    available.contains(ingredientNameLower) || ingredientNameLower.contains(available)
                                }

                return RecipeIngredient(
                    id: ingredient.id,
                    name: ingredient.name,
                    amount: ingredient.amount,
                    unit: ingredient.unit,
                    isOptional: ingredient.isOptional,
                    isAvailable: isAvailable
                )
            }

            return Recipe(
                id: recipe.id,
                name: recipe.name,
                difficulty: recipe.difficulty,
                preparationTime: recipe.preparationTime,
                cookingTime: recipe.cookingTime,
                servings: recipe.servings,
                ingredients: updatedIngredients,
                instructions: recipe.instructions,
                tips: recipe.tips,
                cuisine: recipe.cuisine,
                tags: recipe.tags,
                createdAt: recipe.createdAt,
                isFavorite: recipe.isFavorite
            )
        }
    }

    func hasExpiringProducts(_ products: [Product]) -> Bool {
        return !getExpiringProducts(from: products).isEmpty
    }

    func getRecipeSuggestionText(for products: [Product]) -> String {
        let expiringCount = getExpiringProducts(from: products).count
        let urgentCount = getUrgentProducts(from: products).count

        if urgentCount > 0 {
            return "🚨 \(urgentCount) ürün yarın bozulacak! Hemen tarif önerisi al"
        } else if expiringCount > 0 {
            return "⏰ \(expiringCount) ürün yakında bozulacak. Tarif önerisi alalım?"
        } else {
            return "🍳 Bugün ne pişirsem? AI'dan tarif önerisi al"
        }
    }
}
