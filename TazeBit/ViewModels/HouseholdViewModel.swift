//
//  HouseholdViewModel.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> Ko<PERSON>ma<PERSON> on 26.05.2025.
//

import Foundation
import SwiftUI

@MainActor
class HouseholdViewModel: ObservableObject {
    @Published var households: [Household] = []
    @Published var selectedHousehold: Household?
    @Published var isLoading = false
    @Published var errorMessage = ""
    @Published var showingCreateHousehold = false
    @Published var showingJoinHousehold = false
    @Published var showingInviteCode = false
    @Published var showingLeaveConfirmation = false
    @Published var showingHouseholdMembers = false
    @Published var showingEditHousehold = false

    // Create household form
    @Published var newHouseholdName = ""
    @Published var newHouseholdDescription = ""

    // Edit household form
    @Published var editHouseholdName = ""
    @Published var editHouseholdDescription = ""

    // Join household form
    @Published var inviteCodeInput = ""

    // Household members
    @Published var householdMembers: [HouseholdMember] = []
    @Published var isLoadingMembers = false

    private let supabaseService = SupabaseService.shared

    func fetchHouseholds() async {
        isLoading = true
        errorMessage = ""

        do {
            households = try await supabaseService.fetchUserHouseholds()

            // Select first household if none selected
            if selectedHousehold == nil && !households.isEmpty {
                selectedHousehold = households.first
            }
        } catch {
            errorMessage = "Evler yüklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func createHousehold() async {
        guard !newHouseholdName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = "Ev adı boş olamaz"
            return
        }

        isLoading = true
        errorMessage = ""

        do {
            let household = try await supabaseService.createHousehold(
                name: newHouseholdName.trimmingCharacters(in: .whitespacesAndNewlines),
                description: newHouseholdDescription.isEmpty ? nil : newHouseholdDescription.trimmingCharacters(in: .whitespacesAndNewlines)
            )

            households.append(household)
            selectedHousehold = household

            // Clear form
            newHouseholdName = ""
            newHouseholdDescription = ""
            showingCreateHousehold = false

        } catch {
            errorMessage = "Ev oluşturulamadı: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func selectHousehold(_ household: Household) {
        selectedHousehold = household
    }

    func showCreateHousehold() {
        newHouseholdName = ""
        newHouseholdDescription = ""
        errorMessage = ""
        showingCreateHousehold = true
    }

    func showJoinHousehold() {
        inviteCodeInput = ""
        errorMessage = ""
        showingJoinHousehold = true
    }

    func showInviteCode() {
        showingInviteCode = true
    }

    func joinHouseholdWithCode() async {
        guard !inviteCodeInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = "Davet kodu boş olamaz"
            return
        }

        isLoading = true
        errorMessage = ""

        do {
            let household = try await supabaseService.joinHouseholdWithInviteCode(
                inviteCodeInput.trimmingCharacters(in: .whitespacesAndNewlines)
            )

            households.append(household)
            selectedHousehold = household

            // Clear form
            inviteCodeInput = ""
            showingJoinHousehold = false

        } catch {
            errorMessage = "Eve katılamadı: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func showLeaveConfirmation() {
        showingLeaveConfirmation = true
    }

    func leaveCurrentHousehold() async {
        guard let household = selectedHousehold else {
            errorMessage = "Ayrılacak ev seçilmedi"
            return
        }

        isLoading = true
        errorMessage = ""

        do {
            try await supabaseService.leaveHousehold(household.id)

            // Remove household from local list
            households.removeAll { $0.id == household.id }

            // Select another household if available
            if !households.isEmpty {
                selectedHousehold = households.first
            } else {
                selectedHousehold = nil
            }

            showingLeaveConfirmation = false

        } catch {
            errorMessage = "Evden ayrılamadı: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func showHouseholdMembers() {
        showingHouseholdMembers = true
        Task {
            await fetchHouseholdMembers()
        }
    }

    func fetchHouseholdMembers() async {
        guard let household = selectedHousehold else {
            errorMessage = "Ev seçilmedi"
            return
        }

        isLoadingMembers = true
        errorMessage = ""

        do {
            householdMembers = try await supabaseService.fetchHouseholdMembers(household.id)
        } catch {
            errorMessage = "Üyeler yüklenemedi: \(error.localizedDescription)"
        }

        isLoadingMembers = false
    }

    func showEditHousehold() {
        guard let household = selectedHousehold else {
            errorMessage = "Düzenlenecek ev seçilmedi"
            return
        }

        editHouseholdName = household.name
        editHouseholdDescription = household.description ?? ""
        errorMessage = ""
        showingEditHousehold = true
    }

    func updateHousehold() async {
        guard let household = selectedHousehold else {
            errorMessage = "Düzenlenecek ev seçilmedi"
            return
        }

        guard !editHouseholdName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = "Ev adı boş olamaz"
            return
        }

        isLoading = true
        errorMessage = ""

        do {
            let updatedHousehold = try await supabaseService.updateHousehold(
                household,
                name: editHouseholdName.trimmingCharacters(in: .whitespacesAndNewlines),
                description: editHouseholdDescription.isEmpty ? nil : editHouseholdDescription.trimmingCharacters(in: .whitespacesAndNewlines)
            )

            // Update local household list
            if let index = households.firstIndex(where: { $0.id == household.id }) {
                households[index] = updatedHousehold
            }

            // Update selected household
            selectedHousehold = updatedHousehold

            // Clear form and close modal
            editHouseholdName = ""
            editHouseholdDescription = ""
            showingEditHousehold = false

        } catch {
            errorMessage = "Ev güncellenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }
}
