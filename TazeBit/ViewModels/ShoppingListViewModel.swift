import Foundation
import SwiftUI

@MainActor
class ShoppingListViewModel: ObservableObject {
    @Published var shoppingLists: [ShoppingList] = []
    @Published var currentItems: [ShoppingListItem] = []
    @Published var listItems: [UUID: [ShoppingListItem]] = [:] // Her liste için items
    @Published var isLoading = false
    @Published var errorMessage: String?

    // Sheet states
    @Published var showingAddList = false
    @Published var showingAddItem = false
    @Published var showingEditList = false
    @Published var showingEditItem = false

    // Selected items for editing
    @Published var selectedList: ShoppingList?
    @Published var selectedItem: ShoppingListItem?

    private let shoppingListService = ShoppingListService()
    private let supabaseService = SupabaseService.shared

    // MARK: - Shopping Lists

    func loadShoppingLists(for householdId: UUID) async {
        isLoading = true
        errorMessage = nil

        do {
            let lists = try await shoppingListService.getShoppingLists(for: householdId)
            shoppingLists = lists

            // Her liste için items'ları yükle
            await loadItemsForAllLists()
        } catch {
            errorMessage = "Alışveriş listeleri yüklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    private func loadItemsForAllLists() async {
        for list in shoppingLists {
            do {
                let items = try await shoppingListService.getShoppingListItems(for: list.id)
                listItems[list.id] = items
            } catch {
                print("Liste items yüklenemedi: \(list.name) - \(error.localizedDescription)")
                listItems[list.id] = []
            }
        }
    }

    func createShoppingList(name: String, description: String?, householdId: UUID) async {
        guard let userId = supabaseService.currentUser?.id else { return }

        isLoading = true
        errorMessage = nil

        do {
            let request = CreateShoppingListRequest(
                name: name,
                description: description,
                householdId: householdId,
                createdBy: userId
            )

            let newList = try await shoppingListService.createShoppingList(request)
            shoppingLists.insert(newList, at: 0)
            showingAddList = false
        } catch {
            errorMessage = "Alışveriş listesi oluşturulamadı: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func updateShoppingList(_ list: ShoppingList, name: String, description: String?, isCompleted: Bool? = nil) async {
        isLoading = true
        errorMessage = nil

        do {
            let request = UpdateShoppingListRequest(
                name: name,
                description: description,
                isCompleted: isCompleted
            )

            let updatedList = try await shoppingListService.updateShoppingList(id: list.id, request: request)

            if let index = shoppingLists.firstIndex(where: { $0.id == list.id }) {
                shoppingLists[index] = updatedList
            }

            showingEditList = false
            selectedList = nil
        } catch {
            errorMessage = "Alışveriş listesi güncellenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func deleteShoppingList(_ list: ShoppingList) async {
        isLoading = true
        errorMessage = nil

        do {
            try await shoppingListService.deleteShoppingList(id: list.id)
            shoppingLists.removeAll { $0.id == list.id }
        } catch {
            errorMessage = "Alışveriş listesi silinemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func toggleListCompleted(_ list: ShoppingList) async {
        await updateShoppingList(list, name: list.name, description: list.description, isCompleted: !list.isCompleted)
    }

    // MARK: - Shopping List Items

    func loadShoppingListItems(for shoppingListId: UUID) async {
        isLoading = true
        errorMessage = nil

        do {
            let items = try await shoppingListService.getShoppingListItems(for: shoppingListId)
            currentItems = items
        } catch {
            errorMessage = "Alışveriş listesi öğeleri yüklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func createShoppingListItem(
        shoppingListId: UUID,
        name: String,
        quantity: Double,
        unit: String?,
        category: String?,
        notes: String?
    ) async {
        isLoading = true
        errorMessage = nil

        do {
            let request = CreateShoppingListItemRequest(
                shoppingListId: shoppingListId,
                name: name,
                quantity: quantity,
                unit: unit,
                category: category,
                notes: notes
            )

            let newItem = try await shoppingListService.createShoppingListItem(request)
            currentItems.append(newItem)
            showingAddItem = false
        } catch {
            errorMessage = "Ürün eklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func updateShoppingListItem(
        _ item: ShoppingListItem,
        name: String,
        quantity: Double,
        unit: String?,
        category: String?,
        notes: String?
    ) async {
        isLoading = true
        errorMessage = nil

        do {
            let request = UpdateShoppingListItemRequest(
                name: name,
                quantity: quantity,
                unit: unit,
                category: category,
                notes: notes,
                isPurchased: nil,
                isAddedToHouse: nil,
                addedToHouseAt: nil
            )

            let updatedItem = try await shoppingListService.updateShoppingListItem(id: item.id, request: request)

            if let index = currentItems.firstIndex(where: { $0.id == item.id }) {
                currentItems[index] = updatedItem
            }

            showingEditItem = false
            selectedItem = nil
        } catch {
            errorMessage = "Ürün güncellenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func deleteShoppingListItem(_ item: ShoppingListItem) async {
        isLoading = true
        errorMessage = nil

        do {
            try await shoppingListService.deleteShoppingListItem(id: item.id)
            currentItems.removeAll { $0.id == item.id }
        } catch {
            errorMessage = "Ürün silinemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func toggleItemPurchased(_ item: ShoppingListItem) async {
        do {
            let updatedItem = try await shoppingListService.toggleItemPurchased(id: item.id, isPurchased: !item.isPurchased)

            // currentItems'ı güncelle
            if let index = currentItems.firstIndex(where: { $0.id == item.id }) {
                currentItems[index] = updatedItem
            }

            // listItems dictionary'sini de güncelle
            if var items = listItems[item.shoppingListId] {
                if let index = items.firstIndex(where: { $0.id == item.id }) {
                    items[index] = updatedItem
                    listItems[item.shoppingListId] = items
                }
            }

            // Eğer "alınmadı" olarak işaretlendiyse, ProductViewModel'e bildir
            if !updatedItem.isPurchased {
                NotificationCenter.default.post(
                    name: NSNotification.Name("ShoppingItemUnpurchased"),
                    object: nil,
                    userInfo: ["itemId": item.id]
                )
            }
        } catch {
            errorMessage = "Ürün durumu güncellenemedi: \(error.localizedDescription)"
        }
    }

    func markItemAsAddedToHouse(_ item: ShoppingListItem) async {
        do {
            let updatedItem = try await shoppingListService.markItemAsAddedToHouse(id: item.id)

            if let index = currentItems.firstIndex(where: { $0.id == item.id }) {
                currentItems[index] = updatedItem
            }
        } catch {
            errorMessage = "Ürün eve eklenemedi: \(error.localizedDescription)"
        }
    }

    // MARK: - Helper Methods

    func getItems(for shoppingListId: UUID) -> [ShoppingListItem] {
        return listItems[shoppingListId] ?? []
    }

    func showAddList() {
        showingAddList = true
    }

    func showAddItem() {
        showingAddItem = true
    }

    func editList(_ list: ShoppingList) {
        selectedList = list
        showingEditList = true
    }

    func editItem(_ item: ShoppingListItem) {
        selectedItem = item
        showingEditItem = true
    }

    // MARK: - Computed Properties

    var pendingItems: [ShoppingListItem] {
        currentItems.filter { !$0.isPurchased }
    }

    var purchasedItems: [ShoppingListItem] {
        currentItems.filter { $0.isPurchased }
    }

    var purchasedNotAddedToHouseItems: [ShoppingListItem] {
        currentItems.filter { $0.isPurchased && !$0.isAddedToHouse }
    }

    var addedToHouseItems: [ShoppingListItem] {
        currentItems.filter { $0.isAddedToHouse }
    }

    var completionPercentage: Double {
        guard !currentItems.isEmpty else { return 0 }
        let purchasedCount = purchasedItems.count
        return Double(purchasedCount) / Double(currentItems.count) * 100
    }
}
