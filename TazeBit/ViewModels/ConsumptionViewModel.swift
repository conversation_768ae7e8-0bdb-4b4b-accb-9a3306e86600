//
//  ConsumptionViewModel.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 28.05.2025.
//

import Foundation
import SwiftUI

@MainActor
class ConsumptionViewModel: ObservableObject {
    @Published var consumptions: [Consumption] = []
    @Published var isLoading = false
    @Published var errorMessage = ""

    private let supabaseService = SupabaseService.shared

    // MARK: - Consumption Management

    func loadConsumptions(for householdId: UUID) async {
        isLoading = true
        errorMessage = ""

        do {
            consumptions = try await supabaseService.getConsumptions(for: householdId)
        } catch {
            errorMessage = "Tüketim verileri yüklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func loadConsumptionsForProduct(_ productId: UUID) async {
        isLoading = true
        errorMessage = ""

        do {
            consumptions = try await supabaseService.getConsumptionsForProduct(productId)
        } catch {
            errorMessage = "Ürün tüketim verileri yüklenemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func addConsumption(
        productId: UUID,
        householdId: UUID,
        consumedQuantity: Int,
        wastedQuantity: Int,
        notes: String?
    ) async -> Bool {
        isLoading = true
        errorMessage = ""

        do {
            _ = try await supabaseService.createConsumption(
                productId: productId,
                householdId: householdId,
                consumedQuantity: consumedQuantity,
                wastedQuantity: wastedQuantity,
                notes: notes
            )

            // Don't add to local array - ProductDetailView will refresh
            // This avoids potential parsing issues with the returned consumption object

            isLoading = false
            return true
        } catch {
            errorMessage = "Tüketim kaydedilemedi: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }

    func updateConsumption(
        _ consumption: Consumption,
        consumedQuantity: Int,
        wastedQuantity: Int,
        notes: String?
    ) async -> Bool {
        isLoading = true
        errorMessage = ""

        do {
            let updatedConsumption = try await supabaseService.updateConsumption(
                id: consumption.id,
                productId: consumption.productId,
                consumedQuantity: consumedQuantity,
                wastedQuantity: wastedQuantity,
                notes: notes
            )

            // Update local array
            if let index = consumptions.firstIndex(where: { $0.id == consumption.id }) {
                consumptions[index] = updatedConsumption
            }

            isLoading = false
            return true
        } catch {
            errorMessage = "Tüketim güncellenemedi: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }

    func deleteConsumption(_ consumption: Consumption) async -> Bool {
        isLoading = true
        errorMessage = ""

        do {
            try await supabaseService.deleteConsumption(id: consumption.id)

            // Remove from local array
            consumptions.removeAll { $0.id == consumption.id }

            isLoading = false
            return true
        } catch {
            errorMessage = "Tüketim silinemedi: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }

    // MARK: - Analytics

    func getConsumptionSummary(for productId: UUID) -> (consumed: Int, wasted: Int, total: Int) {
        let productConsumptions = consumptions.filter { $0.productId == productId }

        let totalConsumed = productConsumptions.reduce(0) { $0 + $1.consumedQuantity }
        let totalWasted = productConsumptions.reduce(0) { $0 + $1.wastedQuantity }
        let total = totalConsumed + totalWasted

        return (consumed: totalConsumed, wasted: totalWasted, total: total)
    }

    func getConsumptionHistory(for productId: UUID) -> [Consumption] {
        return consumptions
            .filter { $0.productId == productId }
            .sorted { $0.consumedAt > $1.consumedAt }
    }

    func getUserConsumptions(for userId: UUID) -> [Consumption] {
        return consumptions
            .filter { $0.userId == userId }
            .sorted { $0.consumedAt > $1.consumedAt }
    }

    func getConsumptionEfficiency(for productId: UUID) -> Double {
        let summary = getConsumptionSummary(for: productId)
        if summary.total == 0 { return 0.0 }
        return Double(summary.consumed) / Double(summary.total) * 100.0
    }

    func getWastePercentage(for productId: UUID) -> Double {
        let summary = getConsumptionSummary(for: productId)
        if summary.total == 0 { return 0.0 }
        return Double(summary.wasted) / Double(summary.total) * 100.0
    }

    // MARK: - Date Range Analytics

    func getConsumptionsInDateRange(from startDate: Date, to endDate: Date) -> [Consumption] {
        return consumptions.filter { consumption in
            consumption.consumedAt >= startDate && consumption.consumedAt <= endDate
        }
    }

    func getDailyConsumptionStats(for date: Date) -> (consumed: Int, wasted: Int) {
        let calendar = Calendar.current
        let dayConsumptions = consumptions.filter { consumption in
            calendar.isDate(consumption.consumedAt, inSameDayAs: date)
        }

        let totalConsumed = dayConsumptions.reduce(0) { $0 + $1.consumedQuantity }
        let totalWasted = dayConsumptions.reduce(0) { $0 + $1.wastedQuantity }

        return (consumed: totalConsumed, wasted: totalWasted)
    }

    func getWeeklyConsumptionStats() -> [(date: Date, consumed: Int, wasted: Int)] {
        let calendar = Calendar.current
        let today = Date()
        var weeklyStats: [(date: Date, consumed: Int, wasted: Int)] = []

        for i in 0..<7 {
            if let date = calendar.date(byAdding: .day, value: -i, to: today) {
                let stats = getDailyConsumptionStats(for: date)
                weeklyStats.append((date: date, consumed: stats.consumed, wasted: stats.wasted))
            }
        }

        return weeklyStats.reversed()
    }

    func getMonthlyConsumptionStats() -> (consumed: Int, wasted: Int, efficiency: Double) {
        let calendar = Calendar.current
        let today = Date()

        guard let startOfMonth = calendar.dateInterval(of: .month, for: today)?.start else {
            return (consumed: 0, wasted: 0, efficiency: 0.0)
        }

        let monthlyConsumptions = getConsumptionsInDateRange(from: startOfMonth, to: today)

        let totalConsumed = monthlyConsumptions.reduce(0) { $0 + $1.consumedQuantity }
        let totalWasted = monthlyConsumptions.reduce(0) { $0 + $1.wastedQuantity }
        let total = totalConsumed + totalWasted

        let efficiency = total > 0 ? Double(totalConsumed) / Double(total) * 100.0 : 0.0

        return (consumed: totalConsumed, wasted: totalWasted, efficiency: efficiency)
    }

    // MARK: - Helper Methods

    func clearError() {
        errorMessage = ""
    }

    func refreshConsumptions(for householdId: UUID) async {
        await loadConsumptions(for: householdId)
    }
}
