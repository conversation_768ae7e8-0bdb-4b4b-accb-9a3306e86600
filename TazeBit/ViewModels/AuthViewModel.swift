//
//  AuthViewModel.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import Foundation
import SwiftUI

@MainActor
class AuthViewModel: ObservableObject {
    @Published var email = ""
    @Published var password = ""
    @Published var fullName = ""
    @Published var isLoading = false
    @Published var errorMessage = ""
    @Published var isSignUpMode = false

    private let supabaseService = SupabaseService.shared

    var isAuthenticated: Bool {
        supabaseService.isAuthenticated
    }

    var currentUser: User? {
        supabaseService.currentUser
    }

    func signIn() async {
        guard !email.isEmpty && !password.isEmpty else {
            errorMessage = "Lütfen email ve şifre alanlarını doldurun"
            return
        }

        isLoading = true
        errorMessage = ""

        do {
            try await supabaseService.signIn(email: email, password: password)
            clearFields()
        } catch {
            print("Sign in error: \(error)")
            if let urlError = error as? URLError {
                switch urlError.code {
                case .networkConnectionLost:
                    errorMessage = "Ağ bağlantısı kesildi. İnternet bağlantınızı kontrol edin."
                case .notConnectedToInternet:
                    errorMessage = "İnternet bağlantısı yok."
                case .timedOut:
                    errorMessage = "Bağlantı zaman aşımına uğradı."
                default:
                    errorMessage = "Ağ hatası: \(urlError.localizedDescription)"
                }
            } else {
                errorMessage = "Giriş yapılamadı: \(error.localizedDescription)"
            }
        }

        isLoading = false
    }

    func signUp() async {
        guard !email.isEmpty && !password.isEmpty && !fullName.isEmpty else {
            errorMessage = "Lütfen tüm alanları doldurun"
            return
        }

        guard password.count >= 6 else {
            errorMessage = "Şifre en az 6 karakter olmalıdır"
            return
        }

        isLoading = true
        errorMessage = ""

        do {
            try await supabaseService.signUp(email: email, password: password, fullName: fullName)
            clearFields()
        } catch {
            errorMessage = "Kayıt olunamadı: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func signOut() async {
        isLoading = true

        do {
            try await supabaseService.signOut()
            clearFields()
        } catch {
            errorMessage = "Çıkış yapılamadı: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func deleteAccount() async {
        isLoading = true

        do {
            try await supabaseService.deleteAccount()
            clearFields()
        } catch {
            errorMessage = "Hesap silinemedi: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func toggleMode() {
        isSignUpMode.toggle()
        errorMessage = ""
    }

    private func clearFields() {
        email = ""
        password = ""
        fullName = ""
        errorMessage = ""
    }
}
