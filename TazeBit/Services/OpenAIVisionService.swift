import Foundation
import UIKit

// MARK: - Models
struct DetectedProduct: Identifiable, Codable {
    let id = UUID()
    let name: String
    let category: String
    let estimatedQuantity: Int
    let unit: String
    let confidence: Double
    let description: String?
    let storageLocation: String
    let shelfLifeDays: Int
    let caloriesPer100g: Int?

    enum CodingKeys: String, CodingKey {
        case name, category, estimatedQuantity, unit, confidence, description, storageLocation, shelfLifeDays, caloriesPer100g
    }
}

struct VisionResponse: Codable {
    let products: [DetectedProduct]
}

enum VisionError: Error, LocalizedError {
    case imageProcessingFailed
    case apiKeyMissing
    case networkError(String)
    case parsingError(String)
    case noProductsDetected

    var errorDescription: String? {
        switch self {
        case .imageProcessingFailed:
            return "Fotoğraf işlenemedi"
        case .apiKeyMissing:
            return "OpenAI API anahtarı eksik"
        case .networkError(let message):
            return "Ağ hatası: \(message)"
        case .parsingError(let message):
            return "Veri işleme hatası: \(message)"
        case .noProductsDetected:
            return "Fotoğrafta ürün tespit edilemedi"
        }
    }
}

// MARK: - OpenAI Vision Service
class OpenAIVisionService {
    static let shared = OpenAIVisionService()

    private let apiKey: String
    private let baseURL = "https://api.openai.com/v1/chat/completions"

    private init() {
        // API key'i Config'den al
        self.apiKey = Config.openAIAPIKey
    }

    func analyzeImage(_ image: UIImage) async throws -> [DetectedProduct] {
        guard !apiKey.isEmpty && apiKey.hasPrefix("sk-") else {
            throw VisionError.apiKeyMissing
        }

        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw VisionError.imageProcessingFailed
        }

        let base64Image = imageData.base64EncodedString()

        let payload: [String: Any] = [
            "model": "gpt-4o",
            "messages": [
                [
                    "role": "user",
                    "content": [
                        [
                            "type": "text",
                            "text": createVisionPrompt()
                        ],
                        [
                            "type": "image_url",
                            "image_url": [
                                "url": "data:image/jpeg;base64,\(base64Image)"
                            ]
                        ]
                    ]
                ]
            ],
            "max_tokens": 1500,
            "temperature": 0.1
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: payload)

            var request = URLRequest(url: URL(string: baseURL)!)
            request.httpMethod = "POST"
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw VisionError.networkError("Geçersiz yanıt")
            }

            guard httpResponse.statusCode == 200 else {
                throw VisionError.networkError("HTTP \(httpResponse.statusCode)")
            }

            let products = try parseResponse(data)

            if products.isEmpty {
                throw VisionError.noProductsDetected
            }

            return products

        } catch let error as VisionError {
            throw error
        } catch {
            throw VisionError.networkError(error.localizedDescription)
        }
    }

    private func createVisionPrompt() -> String {
        return """
        Analyze this refrigerator/kitchen/grocery photo and detect all visible food products.

        Return ONLY a valid JSON response in this exact format:
        {
            "products": [
                {
                    "name": "product name in Turkish",
                    "category": "fruits",
                    "estimatedQuantity": 5,
                    "unit": "adet",
                    "confidence": 0.95,
                    "description": "optional description",
                    "storageLocation": "Buzdolabı",
                    "shelfLifeDays": 7,
                    "caloriesPer100g": 52
                }
            ]
        }

        Rules:
        - Only include clearly visible and identifiable food items
        - Use Turkish product names (e.g., "elma", "süt", "ekmek", "domates")
        - Categories: fruits, vegetables, dairy, meat, beverages, snacks, frozen, canned, grains, other
        - Units: adet, kg, gr, litre, ml, şişe, kutu, paket
        - Storage locations: "Buzdolabı", "Dondurucu", "Dolap", "Tezgah"
          * Buzdolabı: Fresh fruits, vegetables, dairy, meat, leftovers
          * Dondurucu: Frozen foods, meat for long storage, ice cream
          * Dolap: Canned goods, dry goods, spices, non-perishables
          * Tezgah: Bananas, bread, onions, potatoes
        - Shelf life days: Realistic expiry based on product type:
          * Fresh fruits/vegetables: 3-14 days
          * Dairy products: 3-10 days
          * Meat/fish: 1-3 days
          * Bread/bakery: 2-5 days
          * Canned goods: 365-1095 days
          * Frozen items: 30-365 days
          * Dry goods: 30-365 days
        - Calories per 100g: Realistic nutritional values:
          * Fruits: 30-80 kcal (apple: 52, banana: 89, orange: 47)
          * Vegetables: 15-50 kcal (tomato: 18, carrot: 41, potato: 77)
          * Dairy: 50-400 kcal (milk: 64, cheese: 350, yogurt: 59)
          * Meat: 100-300 kcal (chicken: 165, beef: 250, fish: 120)
          * Grains: 300-400 kcal (bread: 265, rice: 365, pasta: 371)
          * Beverages: 0-50 kcal (water: 0, juice: 45, soda: 42)
          * Use null for non-food items or when uncertain
        - Confidence: 0.0-1.0 (minimum 0.6)
        - Maximum 20 products
        - Estimate realistic quantities

        IMPORTANT: Return ONLY the JSON object, no explanations, no markdown formatting, no code blocks.
        """
    }

    private func parseResponse(_ data: Data) throws -> [DetectedProduct] {
        do {
            let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]

            guard let choices = json?["choices"] as? [[String: Any]],
                  let firstChoice = choices.first,
                  let message = firstChoice["message"] as? [String: Any],
                  let content = message["content"] as? String else {
                throw VisionError.parsingError("OpenAI yanıtı parse edilemedi")
            }

            print("🔍 OpenAI Raw Response: \(content)")

            // JSON content'i temizle ve parse et
            let cleanedContent = cleanJSONContent(content)
            print("🧹 Cleaned Content: \(cleanedContent)")

            guard let contentData = cleanedContent.data(using: .utf8) else {
                throw VisionError.parsingError("Content UTF8'e çevrilemedi")
            }

            let visionResponse = try JSONDecoder().decode(VisionResponse.self, from: contentData)

            // Güven skoru filtreleme
            let filteredProducts = visionResponse.products.filter { $0.confidence >= 0.6 }

            print("✅ Vision API: \(filteredProducts.count) ürün tespit edildi")

            return filteredProducts

        } catch let decodingError as DecodingError {
            print("❌ JSON Decode Error: \(decodingError)")
            throw VisionError.parsingError("JSON decode hatası: \(decodingError.localizedDescription)")
        } catch {
            print("❌ Parse Error: \(error)")
            throw VisionError.parsingError(error.localizedDescription)
        }
    }

    private func cleanJSONContent(_ content: String) -> String {
        // Markdown code block'larını temizle
        var cleaned = content

        // ```json ve ``` etiketlerini kaldır
        cleaned = cleaned.replacingOccurrences(of: "```json", with: "")
        cleaned = cleaned.replacingOccurrences(of: "```", with: "")

        // Başındaki ve sonundaki boşlukları temizle
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)

        // Eğer JSON ile başlamıyorsa, JSON kısmını bul
        if !cleaned.hasPrefix("{") {
            if let jsonStart = cleaned.range(of: "{") {
                cleaned = String(cleaned[jsonStart.lowerBound...])
            }
        }

        // Eğer JSON ile bitmiyorsa, JSON kısmını bul
        if !cleaned.hasSuffix("}") {
            if let jsonEnd = cleaned.lastIndex(of: "}") {
                cleaned = String(cleaned[...jsonEnd])
            }
        }

        return cleaned
    }
}

// MARK: - Helper Extensions
extension DetectedProduct {
    var productCategory: ProductCategory {
        switch category.lowercased() {
        case "fruits":
            return .fruits
        case "vegetables":
            return .vegetables
        case "dairy":
            return .dairy
        case "meat":
            return .meat
        case "beverages":
            return .beverages
        case "snacks":
            return .snacks
        case "frozen":
            return .frozen
        case "canned":
            return .canned
        case "bakery", "grains":
            return .grains
        default:
            return .other
        }
    }

    var productUnit: ProductUnit {
        switch unit.lowercased() {
        case "adet", "piece":
            return .piece
        case "kg":
            return .kg
        case "gr", "gram":
            return .gram
        case "litre", "liter":
            return .liter
        case "ml":
            return .ml
        case "şişe", "bottle":
            return .bottle
        case "kutu", "can":
            return .can
        case "paket", "package":
            return .pack
        default:
            return .piece
        }
    }

    var productLocation: StorageLocation {
        switch storageLocation.lowercased() {
        case "buzdolabı", "refrigerator", "fridge":
            return .refrigerator
        case "dondurucu", "freezer":
            return .freezer
        case "dolap", "pantry", "cabinet":
            return .pantry
        case "tezgah", "counter", "countertop":
            return .countertop
        default:
            return .refrigerator
        }
    }

    var expiryDate: Date {
        return Calendar.current.date(byAdding: .day, value: shelfLifeDays, to: Date()) ?? Date()
    }
}
