import Foundation
import Supabase

class ShoppingListService {
    private let supabase = SupabaseService.shared.client

    // MARK: - Shopping Lists

    func getShoppingLists(for householdId: UUID) async throws -> [ShoppingList] {
        let response: [ShoppingList] = try await supabase
            .from("shopping_lists")
            .select()
            .eq("household_id", value: householdId)
            .order("created_at", ascending: false)
            .execute()
            .value

        return response
    }

    func createShoppingList(_ request: CreateShoppingListRequest) async throws -> ShoppingList {
        let response: [ShoppingList] = try await supabase
            .from("shopping_lists")
            .insert(request)
            .select()
            .execute()
            .value

        guard let shoppingList = response.first else {
            throw NSError(domain: "ShoppingListService", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to create shopping list"])
        }

        return shoppingList
    }

    func updateShoppingList(id: UUID, request: UpdateShoppingListRequest) async throws -> ShoppingList {
        let response: [ShoppingList] = try await supabase
            .from("shopping_lists")
            .update(request)
            .eq("id", value: id)
            .select()
            .execute()
            .value

        guard let shoppingList = response.first else {
            throw NSError(domain: "ShoppingListService", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to update shopping list"])
        }

        return shoppingList
    }

    func deleteShoppingList(id: UUID) async throws {
        try await supabase
            .from("shopping_lists")
            .delete()
            .eq("id", value: id)
            .execute()
    }

    // MARK: - Shopping List Items

    func getShoppingListItems(for shoppingListId: UUID) async throws -> [ShoppingListItem] {
        let response: [ShoppingListItem] = try await supabase
            .from("shopping_list_items")
            .select()
            .eq("shopping_list_id", value: shoppingListId)
            .order("created_at", ascending: true)
            .execute()
            .value

        return response
    }

    func createShoppingListItem(_ request: CreateShoppingListItemRequest) async throws -> ShoppingListItem {
        let response: [ShoppingListItem] = try await supabase
            .from("shopping_list_items")
            .insert(request)
            .select()
            .execute()
            .value

        guard let item = response.first else {
            throw NSError(domain: "ShoppingListService", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to create shopping list item"])
        }

        return item
    }

    func updateShoppingListItem(id: UUID, request: UpdateShoppingListItemRequest) async throws -> ShoppingListItem {
        let response: [ShoppingListItem] = try await supabase
            .from("shopping_list_items")
            .update(request)
            .eq("id", value: id)
            .select()
            .execute()
            .value

        guard let item = response.first else {
            throw NSError(domain: "ShoppingListService", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to update shopping list item"])
        }

        return item
    }

    func deleteShoppingListItem(id: UUID) async throws {
        try await supabase
            .from("shopping_list_items")
            .delete()
            .eq("id", value: id)
            .execute()
    }

    func toggleItemPurchased(id: UUID, isPurchased: Bool) async throws -> ShoppingListItem {
        // Eğer alınmadı olarak işaretleniyorsa, önce ilişkili ürünleri sil
        if !isPurchased {
            try await supabase
                .from("products")
                .delete()
                .eq("shopping_list_item_id", value: id)
                .execute()
        }

        let request = UpdateShoppingListItemRequest(
            name: nil,
            quantity: nil,
            unit: nil,
            category: nil,
            notes: nil,
            isPurchased: isPurchased,
            // Eğer alınmadı olarak işaretleniyorsa, evden de çıkar
            isAddedToHouse: isPurchased ? nil : false,
            addedToHouseAt: isPurchased ? nil : nil
        )

        return try await updateShoppingListItem(id: id, request: request)
    }

    func markItemAsAddedToHouse(id: UUID) async throws -> ShoppingListItem {
        let request = UpdateShoppingListItemRequest(
            name: nil,
            quantity: nil,
            unit: nil,
            category: nil,
            notes: nil,
            isPurchased: nil,
            isAddedToHouse: true,
            addedToHouseAt: Date()
        )

        return try await updateShoppingListItem(id: id, request: request)
    }

    func removeItemFromHouse(id: UUID) async throws -> ShoppingListItem {
        // Önce ilişkili ürünleri sil
        try await supabase
            .from("products")
            .delete()
            .eq("shopping_list_item_id", value: id)
            .execute()

        // Sonra shopping list item'ı güncelle
        let request = UpdateShoppingListItemRequest(
            name: nil,
            quantity: nil,
            unit: nil,
            category: nil,
            notes: nil,
            isPurchased: nil,
            isAddedToHouse: false,
            addedToHouseAt: nil
        )

        return try await updateShoppingListItem(id: id, request: request)
    }
}
