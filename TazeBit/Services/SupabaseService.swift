//
//  SupabaseService.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import Foundation
import Supabase

class SupabaseService: ObservableObject {
    static let shared = SupabaseService()

    let client: SupabaseClient

    @Published var currentUser: User?
    @Published var isAuthenticated = false

    private init() {
        self.client = SupabaseClient(
            supabaseURL: URL(string: "https://cevbwbktkwzcqrjxkjfm.supabase.co")!,
            supabaseKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNldmJ3Ymt0a3d6Y3FyanhramZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyODY5OTMsImV4cCI6MjA2Mzg2Mjk5M30.rff7mkREwAUQ4knzP9P9cWyqDlgSXjCtD-qRRwtCUtQ"
        )

        // Listen for auth state changes
        Task {
            for await state in client.auth.authStateChanges {
                await MainActor.run {
                    self.isAuthenticated = state.session != nil
                    if let user = state.session?.user {
                        Task {
                            await self.fetchUserProfile(userId: user.id)
                        }
                    } else {
                        self.currentUser = nil
                    }
                }
            }
        }
    }

    // MARK: - Authentication

    func signUp(email: String, password: String, fullName: String) async throws {
        let response = try await client.auth.signUp(
            email: email,
            password: password
        )

        // Create user profile
        let userProfile = UserProfile(
            id: response.user.id,
            email: email,
            fullName: fullName,
            avatarUrl: nil,
            createdAt: Date(),
            updatedAt: Date()
        )

        try await client
            .from("users")
            .insert(userProfile)
            .execute()
    }

    func signIn(email: String, password: String) async throws {
        var lastError: Error?

        // Retry up to 3 times
        for attempt in 1...3 {
            do {
                try await client.auth.signIn(
                    email: email,
                    password: password
                )
                return // Success, exit the function
            } catch {
                lastError = error
                print("Sign in attempt \(attempt) failed: \(error)")

                // If it's a network error, wait before retrying
                if let urlError = error as? URLError,
                   urlError.code == .networkConnectionLost || urlError.code == .timedOut {
                    if attempt < 3 {
                        try await Task.sleep(nanoseconds: UInt64(attempt * 1_000_000_000)) // Wait 1, 2 seconds
                        continue
                    }
                }

                // If it's not a network error, don't retry
                throw error
            }
        }

        // If all retries failed, throw the last error
        throw lastError ?? SupabaseError.invalidData
    }

    func signOut() async throws {
        try await client.auth.signOut()
    }

    func deleteAccount() async throws {
        guard let user = client.auth.currentUser else {
            throw SupabaseError.userNotFound
        }

        // First delete all user data from our tables
        try await deleteAllUserData(userId: user.id)

        // Then delete the user account from Supabase Auth
        // Note: This requires admin privileges or RLS policies that allow users to delete themselves
        // For now, we'll sign out the user and mark their data as deleted
        try await client.auth.signOut()
    }

    private func deleteAllUserData(userId: UUID) async throws {
        // Delete user's consumption records
        try await client
            .from("consumption")
            .delete()
            .eq("user_id", value: userId)
            .execute()

        // Delete user's products
        try await client
            .from("products")
            .delete()
            .eq("created_by", value: userId)
            .execute()

        // Delete user's shopping lists
        try await client
            .from("shopping_lists")
            .delete()
            .eq("created_by", value: userId)
            .execute()

        // Remove user from households (but don't delete households)
        try await client
            .from("household_members")
            .delete()
            .eq("user_id", value: userId)
            .execute()

        // Delete user profile
        try await client
            .from("users")
            .delete()
            .eq("id", value: userId)
            .execute()
    }

    func fetchUserProfile(userId: UUID) async {
        do {
            let user: User = try await client
                .from("users")
                .select()
                .eq("id", value: userId)
                .single()
                .execute()
                .value

            await MainActor.run {
                self.currentUser = user
            }
        } catch {
            print("Error fetching user profile: \(error)")
        }
    }

    // MARK: - Households

    func createHousehold(name: String, description: String?) async throws -> Household {
        guard let userId = client.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let household = Household(
            id: UUID(),
            name: name,
            description: description,
            inviteCode: generateInviteCode(),
            createdAt: Date(),
            createdBy: userId
        )

        let createdHousehold: Household = try await client
            .from("households")
            .insert(household)
            .select()
            .single()
            .execute()
            .value

        // Add user as owner
        let userHousehold = UserHousehold(
            id: UUID(),
            userId: userId,
            householdId: createdHousehold.id,
            role: .owner,
            joinedAt: Date()
        )

        try await client
            .from("user_households")
            .insert(userHousehold)
            .execute()

        return createdHousehold
    }

    func fetchUserHouseholds() async throws -> [Household] {
        guard let userId = client.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let households: [Household] = try await client
            .from("households")
            .select("""
                *,
                user_households!inner(user_id)
            """)
            .eq("user_households.user_id", value: userId)
            .execute()
            .value

        return households
    }

    func joinHouseholdWithInviteCode(_ inviteCode: String) async throws -> Household {
        guard let userId = client.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        // Find household by invite code
        let household: Household = try await client
            .from("households")
            .select()
            .eq("invite_code", value: inviteCode.uppercased())
            .single()
            .execute()
            .value

        // Check if user is already a member
        let existingMembership: [UserHousehold] = try await client
            .from("user_households")
            .select()
            .eq("user_id", value: userId)
            .eq("household_id", value: household.id)
            .execute()
            .value

        if !existingMembership.isEmpty {
            throw SupabaseError.alreadyMember
        }

        // Add user as member
        let userHousehold = UserHousehold(
            id: UUID(),
            userId: userId,
            householdId: household.id,
            role: .member,
            joinedAt: Date()
        )

        try await client
            .from("user_households")
            .insert(userHousehold)
            .execute()

        return household
    }

    func leaveHousehold(_ householdId: UUID) async throws {
        guard let userId = client.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        // Check if user is the owner
        let userHousehold: [UserHousehold] = try await client
            .from("user_households")
            .select()
            .eq("user_id", value: userId)
            .eq("household_id", value: householdId)
            .execute()
            .value

        guard let membership = userHousehold.first else {
            throw SupabaseError.notMember
        }

        // Don't allow owner to leave if there are other members
        if membership.role == .owner {
            let allMembers: [UserHousehold] = try await client
                .from("user_households")
                .select()
                .eq("household_id", value: householdId)
                .execute()
                .value

            if allMembers.count > 1 {
                throw SupabaseError.ownerCannotLeave
            }

            // If owner is the only member, delete the household and all related data
            try await deleteHouseholdCompletely(householdId)
        } else {
            // Regular member can leave directly
            try await client
                .from("user_households")
                .delete()
                .eq("user_id", value: userId)
                .eq("household_id", value: householdId)
                .execute()
        }
    }

    private func deleteHouseholdCompletely(_ householdId: UUID) async throws {
        // Delete all products in the household
        try await client
            .from("products")
            .delete()
            .eq("household_id", value: householdId)
            .execute()

        // Delete all user-household relationships
        try await client
            .from("user_households")
            .delete()
            .eq("household_id", value: householdId)
            .execute()

        // Delete the household itself
        try await client
            .from("households")
            .delete()
            .eq("id", value: householdId)
            .execute()
    }

    func updateHousehold(_ household: Household, name: String, description: String?) async throws -> Household {
        guard let userId = client.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        // Check if user is the owner
        let userHousehold: [UserHousehold] = try await client
            .from("user_households")
            .select()
            .eq("user_id", value: userId)
            .eq("household_id", value: household.id)
            .eq("role", value: HouseholdRole.owner.rawValue)
            .execute()
            .value

        if userHousehold.isEmpty {
            throw SupabaseError.notOwner
        }

        // Update household
        let updatedHousehold = Household(
            id: household.id,
            name: name,
            description: description,
            inviteCode: household.inviteCode,
            createdAt: household.createdAt,
            createdBy: household.createdBy
        )

        let result: Household = try await client
            .from("households")
            .update(updatedHousehold)
            .eq("id", value: household.id)
            .select()
            .single()
            .execute()
            .value

        return result
    }

    func fetchHouseholdMembers(_ householdId: UUID) async throws -> [HouseholdMember] {
        let members: [HouseholdMember] = try await client
            .from("user_households")
            .select("""
                *,
                users!inner(id, email, full_name, created_at)
            """)
            .eq("household_id", value: householdId)
            .order("role", ascending: false) // owner first, then members
            .order("joined_at", ascending: true) // oldest first
            .execute()
            .value

        return members
    }

    private func generateInviteCode() -> String {
        let characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return String((0..<6).map { _ in characters.randomElement()! })
    }

    // MARK: - Products

    func addProduct(_ product: Product) async throws -> Product {
        // Insert the product without trying to read it back
        try await client
            .from("products")
            .insert(product)
            .execute()

        // Return the original product since insert was successful
        return product
    }

    func fetchProducts(for householdId: UUID) async throws -> [Product] {
        do {
            let products: [Product] = try await client
                .from("products")
                .select()
                .eq("household_id", value: householdId)
                .order("expiry_date", ascending: true)
                .execute()
                .value

            // Süresi geçen ürünleri otomatik israf et
            await autoWasteExpiredProducts(products, householdId: householdId)

            return products
        } catch {
            print("Error fetching products: \(error)")
            // Return empty array if there's a parsing error
            return []
        }
    }

    func updateProduct(_ product: Product) async throws {
        try await client
            .from("products")
            .update(product)
            .eq("id", value: product.id)
            .execute()
    }

    func deleteProduct(id: UUID) async throws {
        try await client
            .from("products")
            .delete()
            .eq("id", value: id)
            .execute()
    }

    // MARK: - Auto Waste Expired Products

    private func autoWasteExpiredProducts(_ products: [Product], householdId: UUID) async {
        let today = Date()

        for product in products {
            // Sadece süresi geçmiş ve kalan miktarı olan ürünleri kontrol et
            if product.expiryDate < today && product.currentQuantity > 0 {
                do {
                    // Bu ürün için zaten israf kaydı var mı kontrol et
                    let existingWaste = try await checkExistingWasteRecord(productId: product.id)

                    if !existingWaste {
                        // Kalan miktarı israf olarak kaydet
                        _ = try await createConsumption(
                            productId: product.id,
                            householdId: householdId,
                            consumedQuantity: 0,
                            wastedQuantity: product.currentQuantity,
                            notes: "Otomatik israf - son kullanma tarihi geçti"
                        )

                        print("🗑️ Auto-wasted expired product: \(product.name) - \(product.currentQuantity) units")
                    }
                } catch {
                    print("❌ Error auto-wasting product \(product.name): \(error)")
                }
            }
        }
    }

    private func checkExistingWasteRecord(productId: UUID) async throws -> Bool {
        let consumptions: [Consumption] = try await client
            .from("consumptions")
            .select()
            .eq("product_id", value: productId)
            .eq("consumption_type", value: "wasted")
            .execute()
            .value

        return !consumptions.isEmpty
    }

    // MARK: - Consumption Management

    func getConsumptions(for householdId: UUID) async throws -> [Consumption] {
        let consumptions: [Consumption] = try await client
            .from("consumptions")
            .select()
            .eq("household_id", value: householdId)
            .order("consumed_at", ascending: false)
            .execute()
            .value

        return consumptions
    }

    func getConsumptionsForProduct(_ productId: UUID) async throws -> [Consumption] {
        let consumptions: [Consumption] = try await client
            .from("consumptions")
            .select()
            .eq("product_id", value: productId)
            .order("consumed_at", ascending: false)
            .execute()
            .value

        return consumptions
    }

    func createConsumption(
        productId: UUID,
        householdId: UUID,
        consumedQuantity: Int,
        wastedQuantity: Int,
        notes: String?
    ) async throws -> Consumption {
        let session = try await client.auth.session
        let currentUser = session.user

        // Determine consumption type
        let consumptionType: String
        if consumedQuantity > 0 && wastedQuantity > 0 {
            consumptionType = "mixed"
        } else if consumedQuantity > 0 {
            consumptionType = "consumed"
        } else {
            consumptionType = "wasted"
        }

        struct ConsumptionCreate: Codable {
            let product_id: UUID
            let user_id: UUID
            let household_id: UUID
            let consumed_quantity: Int
            let wasted_quantity: Int
            let consumption_type: String
            let notes: String?
        }

        let createData = ConsumptionCreate(
            product_id: productId,
            user_id: currentUser.id,
            household_id: householdId,
            consumed_quantity: consumedQuantity,
            wasted_quantity: wastedQuantity,
            consumption_type: consumptionType,
            notes: notes
        )

        // Insert without returning data to avoid parsing issues
        try await client
            .from("consumptions")
            .insert(createData)
            .execute()

        // Update product's current quantity first
        try await updateProductCurrentQuantity(productId: productId)

        // Create a local consumption object to return
        let consumption = Consumption(
            id: UUID(), // This will be different from DB, but it's just for local state
            productId: productId,
            userId: currentUser.id,
            householdId: householdId,
            consumedQuantity: consumedQuantity,
            wastedQuantity: wastedQuantity,
            consumptionType: consumptionType,
            notes: notes,
            consumedAt: Date(),
            createdAt: Date()
        )

        return consumption
    }

    func updateConsumption(
        id: UUID,
        productId: UUID,
        consumedQuantity: Int,
        wastedQuantity: Int,
        notes: String?
    ) async throws -> Consumption {
        // Determine consumption type
        let consumptionType: String
        if consumedQuantity > 0 && wastedQuantity > 0 {
            consumptionType = "mixed"
        } else if consumedQuantity > 0 {
            consumptionType = "consumed"
        } else {
            consumptionType = "wasted"
        }

        struct ConsumptionUpdate: Codable {
            let consumed_quantity: Int
            let wasted_quantity: Int
            let consumption_type: String
            let notes: String?
        }

        let updateData = ConsumptionUpdate(
            consumed_quantity: consumedQuantity,
            wasted_quantity: wastedQuantity,
            consumption_type: consumptionType,
            notes: notes
        )

        let consumptions: [Consumption] = try await client
            .from("consumptions")
            .update(updateData)
            .eq("id", value: id)
            .select()
            .execute()
            .value

        guard let consumption = consumptions.first else {
            throw SupabaseError.invalidData
        }

        // Update product's current quantity
        try await updateProductCurrentQuantity(productId: productId)

        return consumption
    }

    func deleteConsumption(id: UUID) async throws {
        // Get the consumption to find the product ID
        let consumptions: [Consumption] = try await client
            .from("consumptions")
            .select()
            .eq("id", value: id)
            .execute()
            .value

        guard let consumption = consumptions.first else {
            throw SupabaseError.invalidData
        }

        try await client
            .from("consumptions")
            .delete()
            .eq("id", value: id)
            .execute()

        // Update product's current quantity after deletion
        try await updateProductCurrentQuantity(productId: consumption.productId)
    }

    private func updateProductCurrentQuantity(productId: UUID) async throws {
        // Get the product's original quantity using a simple struct
        struct ProductQuantity: Codable {
            let original_quantity: Int
        }

        let productQuantities: [ProductQuantity] = try await client
            .from("products")
            .select("original_quantity")
            .eq("id", value: productId)
            .execute()
            .value

        guard let productQuantity = productQuantities.first else {
            throw SupabaseError.invalidData
        }

        // Get all consumptions for this product
        let consumptions: [Consumption] = try await client
            .from("consumptions")
            .select()
            .eq("product_id", value: productId)
            .execute()
            .value

        // Calculate total consumed and wasted
        let totalConsumed = consumptions.reduce(0) { $0 + $1.consumedQuantity }
        let totalWasted = consumptions.reduce(0) { $0 + $1.wastedQuantity }

        // Calculate new current quantity
        let newCurrentQuantity = max(0, productQuantity.original_quantity - totalConsumed - totalWasted)

        // Update the product's current quantity
        struct ProductQuantityUpdate: Codable {
            let current_quantity: Int
        }

        let updateData = ProductQuantityUpdate(current_quantity: newCurrentQuantity)

        try await client
            .from("products")
            .update(updateData)
            .eq("id", value: productId)
            .execute()
    }

    // MARK: - User Methods
    func getUserProfile(userId: UUID) async throws -> UserProfile? {
        let users: [User] = try await client
            .from("users")
            .select()
            .eq("id", value: userId)
            .execute()
            .value

        guard let user = users.first else {
            return nil
        }

        // Convert User to UserProfile
        return UserProfile(
            id: user.id,
            email: user.email,
            fullName: user.fullName,
            avatarUrl: nil,
            createdAt: user.createdAt,
            updatedAt: user.createdAt
        )
    }

    // MARK: - Shopping List Operations

    func createShoppingList(_ shoppingList: ShoppingList) async throws -> ShoppingList {
        let response: ShoppingList = try await client
            .from("shopping_lists")
            .insert(shoppingList)
            .select()
            .single()
            .execute()
            .value

        return response
    }

    func createShoppingListItem(_ item: ShoppingListItem) async throws -> ShoppingListItem {
        let response: ShoppingListItem = try await client
            .from("shopping_list_items")
            .insert(item)
            .select()
            .single()
            .execute()
            .value

        return response
    }

    func getShoppingLists(for householdId: UUID) async throws -> [ShoppingList] {
        let response: [ShoppingList] = try await client
            .from("shopping_lists")
            .select()
            .eq("household_id", value: householdId)
            .order("created_at", ascending: false)
            .execute()
            .value

        return response
    }

    func getShoppingListItems(for shoppingListId: UUID) async throws -> [ShoppingListItem] {
        let response: [ShoppingListItem] = try await client
            .from("shopping_list_items")
            .select()
            .eq("shopping_list_id", value: shoppingListId)
            .order("created_at", ascending: false)
            .execute()
            .value

        return response
    }

    // MARK: - Favorite Recipe Operations

    func addFavoriteRecipe(_ favoriteRecipe: FavoriteRecipe) async throws -> FavoriteRecipe {
        let response: FavoriteRecipe = try await client
            .from("favorite_recipes")
            .insert(favoriteRecipe)
            .select()
            .single()
            .execute()
            .value

        return response
    }

    func removeFavoriteRecipe(userId: UUID, recipeName: String) async throws {
        try await client
            .from("favorite_recipes")
            .delete()
            .eq("user_id", value: userId)
            .eq("recipe_name", value: recipeName)
            .execute()
    }

    func getFavoriteRecipes(for userId: UUID) async throws -> [FavoriteRecipe] {
        let response: [FavoriteRecipe] = try await client
            .from("favorite_recipes")
            .select()
            .eq("user_id", value: userId)
            .order("created_at", ascending: false)
            .execute()
            .value

        return response
    }

    func isFavoriteRecipe(userId: UUID, recipeName: String) async throws -> Bool {
        let response: [FavoriteRecipe] = try await client
            .from("favorite_recipes")
            .select()
            .eq("user_id", value: userId)
            .eq("recipe_name", value: recipeName)
            .execute()
            .value

        return !response.isEmpty
    }
}

enum SupabaseError: LocalizedError {
    case notAuthenticated
    case alreadyMember
    case householdNotFound
    case invalidData
    case notMember
    case userNotFound
    case ownerCannotLeave
    case notOwner

    var errorDescription: String? {
        switch self {
        case .notAuthenticated:
            return "Kullanıcı oturumu bulunamadı"
        case .alreadyMember:
            return "Bu eve zaten üyesiniz"
        case .householdNotFound:
            return "Geçersiz davet kodu"
        case .invalidData:
            return "Geçersiz veri"
        case .notMember:
            return "Bu evin üyesi değilsiniz"
        case .ownerCannotLeave:
            return "Ev sahibi başka üyeler varken evden ayrılamaz. Önce diğer üyeleri çıkarın veya ev sahipliğini devredin."
        case .notOwner:
            return "Bu işlemi sadece ev sahibi yapabilir"
        case .userNotFound:
            return "Kullanıcı bulunamadı."
        }
    }
}
