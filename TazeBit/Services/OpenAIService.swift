//
//  OpenAIService.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 29.05.2025.
//

import Foundation

// MARK: - OpenAI API Models

struct OpenAIRequest: Codable {
    let model: String
    let messages: [OpenAIMessage]
    let temperature: Double
    let maxTokens: Int

    enum CodingKeys: String, CodingKey {
        case model
        case messages
        case temperature
        case maxTokens = "max_tokens"
    }
}

struct OpenAIMessage: Codable {
    let role: String
    let content: String
}

struct OpenAIResponse: Codable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [OpenAIChoice]
    let usage: OpenAIUsage?
}

struct OpenAIChoice: Codable {
    let index: Int
    let message: OpenAIMessage
    let finishReason: String?

    enum CodingKeys: String, CodingKey {
        case index
        case message
        case finishReason = "finish_reason"
    }
}

struct OpenAIUsage: Codable {
    let promptTokens: Int
    let completionTokens: Int
    let totalTokens: Int

    enum CodingKeys: String, CodingKey {
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
    }
}

// MARK: - OpenAI Service

class OpenAIService {
    static let shared = OpenAIService()

    private let baseURL = Config.openAIBaseURL
    private let apiKey = Config.openAIAPIKey
    private let model = Config.openAIModel

    private init() {}

    // MARK: - Recipe Generation

    func generateRecipes(for request: RecipeRequest) async throws -> AIRecipeResponse {
        let prompt = createRecipePrompt(from: request)

        if Config.shouldLogAPIRequests {
            print("🤖 OpenAI Request Prompt:")
            print(prompt)
        }

        let openAIRequest = OpenAIRequest(
            model: model,
            messages: [
                OpenAIMessage(role: "system", content: "Sen bir Türk mutfağı uzmanısın. Verilen malzemelerle lezzetli, pratik ve sağlıklı yemek tarifleri öneriyorsun."),
                OpenAIMessage(role: "user", content: prompt)
            ],
            temperature: 0.7,
            maxTokens: 2000
        )

        let response = try await makeAPIRequest(request: openAIRequest)
        var recipes = try parseRecipeResponse(response.choices.first?.message.content ?? "")

        // Update ingredient availability based on available ingredients
        recipes = updateIngredientAvailability(recipes: recipes, availableIngredients: request.availableIngredients)

        return AIRecipeResponse(
            recipes: recipes,
            missingIngredients: findMissingIngredients(recipes: recipes, available: request.availableIngredients),
            suggestions: generateSuggestions(for: request),
            generatedAt: Date()
        )
    }

    func generateQuickRecipe(ingredients: [String]) async throws -> Recipe? {
        let prompt = Config.quickRecipePromptTemplate.replacingOccurrences(
            of: "{ingredients}",
            with: ingredients.joined(separator: ", ")
        )

        let openAIRequest = OpenAIRequest(
            model: model,
            messages: [
                OpenAIMessage(role: "system", content: "Sen hızlı ve pratik yemek tarifleri konusunda uzmansın."),
                OpenAIMessage(role: "user", content: prompt)
            ],
            temperature: 0.8,
            maxTokens: 800
        )

        let response = try await makeAPIRequest(request: openAIRequest)
        let recipes = try parseRecipeResponse(response.choices.first?.message.content ?? "")

        return recipes.first
    }



    // MARK: - Private Methods

    private func makeAPIRequest(request: OpenAIRequest) async throws -> OpenAIResponse {
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw OpenAIError.invalidURL
        }

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.timeoutInterval = 60.0 // 60 saniye timeout

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            throw OpenAIError.encodingError(error)
        }

        do {
            // Özel URLSession configuration ile timeout ayarları
            let configuration = URLSessionConfiguration.default
            configuration.timeoutIntervalForRequest = 60.0
            configuration.timeoutIntervalForResource = 120.0
            let session = URLSession(configuration: configuration)

            let (data, response) = try await session.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw OpenAIError.invalidResponse
            }

            if Config.shouldLogAPIRequests {
                print("🌐 OpenAI Response Status: \(httpResponse.statusCode)")
            }

            guard httpResponse.statusCode == 200 else {
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let error = errorData["error"] as? [String: Any],
                   let message = error["message"] as? String {
                    throw OpenAIError.apiError(message)
                }
                throw OpenAIError.httpError(httpResponse.statusCode)
            }

            let openAIResponse = try JSONDecoder().decode(OpenAIResponse.self, from: data)

            if Config.shouldLogAPIRequests {
                print("✅ OpenAI Response received successfully")
                if let usage = openAIResponse.usage {
                    print("📊 Token Usage: \(usage.totalTokens) total (\(usage.promptTokens) prompt + \(usage.completionTokens) completion)")
                }
            }

            return openAIResponse

        } catch let error as OpenAIError {
            throw error
        } catch {
            // Timeout hatalarını özel olarak yakala
            if let urlError = error as? URLError, urlError.code == .timedOut {
                throw OpenAIError.timeout
            }
            throw OpenAIError.networkError(error)
        }
    }

    private func createRecipePrompt(from request: RecipeRequest) -> String {
        var prompt = Config.recipePromptTemplate

        // Replace ingredients
        let ingredientsList = request.availableIngredients.joined(separator: ", ")
        prompt = prompt.replacingOccurrences(of: "{ingredients}", with: ingredientsList)

        // Add expiring ingredients emphasis
        if !request.expiringIngredients.isEmpty {
            prompt += "\n\nÖNEMLİ: Şu malzemeler yakında bozulacak, öncelikle bunları kullan: \(request.expiringIngredients.joined(separator: ", "))"
        }

        // Add preferences
        if let difficulty = request.preferences.difficulty {
            prompt += "\n\nZorluk seviyesi tercihi: \(difficulty.displayName) - ama diğer seviyelerden de öner"
        } else {
            prompt += "\n\nFarklı zorluk seviyelerinde tarifler öner: kolay, orta ve zor karışık"
        }

        if let maxTime = request.preferences.maxTime {
            prompt += "\n\nMaksimum süre: \(maxTime) dakika"
        }

        if !request.preferences.excludeIngredients.isEmpty {
            prompt += "\n\nKullanma: \(request.preferences.excludeIngredients.joined(separator: ", "))"
        }

        return prompt
    }



    private func parseRecipeResponse(_ response: String) throws -> [Recipe] {
        // Bu basit bir parser. Gerçek uygulamada daha sofistike parsing gerekebilir
        var recipes: [Recipe] = []

        let recipeBlocks = response.components(separatedBy: "---").filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }

        for block in recipeBlocks.prefix(5) { // Maksimum 5 tarif
            if let recipe = parseRecipeBlock(block.trimmingCharacters(in: .whitespacesAndNewlines)) {
                recipes.append(recipe)
            }
        }

        return recipes
    }

    private func parseRecipeBlock(_ block: String) -> Recipe? {
        let lines = block.components(separatedBy: .newlines).map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }

        var name = "Bilinmeyen Tarif"
        var difficulty = RecipeDifficulty.medium // Default orta seviye
        var time = 30
        var ingredients: [RecipeIngredient] = []
        var instructions: [String] = []
        var tips: String?

        var currentSection = ""

        for line in lines {
            if line.hasPrefix("**Tarif Adı:**") {
                name = line.replacingOccurrences(of: "**Tarif Adı:**", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
            } else if line.hasPrefix("**Zorluk:**") {
                let difficultyText = line.replacingOccurrences(of: "**Zorluk:**", with: "").trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
                if difficultyText.contains("kolay") {
                    difficulty = .easy
                } else if difficultyText.contains("orta") {
                    difficulty = .medium
                } else if difficultyText.contains("zor") {
                    difficulty = .hard
                }
            } else if line.hasPrefix("**Süre:**") {
                let timeText = line.replacingOccurrences(of: "**Süre:**", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
                time = extractTimeFromText(timeText)
            } else if line.hasPrefix("**Malzemeler:**") {
                currentSection = "ingredients"
            } else if line.hasPrefix("**Tarif:**") {
                currentSection = "instructions"
            } else if line.hasPrefix("**İpucu:**") {
                tips = line.replacingOccurrences(of: "**İpucu:**", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
            } else if line.hasPrefix("- ") && currentSection == "ingredients" {
                let ingredientText = line.replacingOccurrences(of: "- ", with: "")
                let ingredient = RecipeIngredient(name: ingredientText, amount: "1", unit: "adet")
                ingredients.append(ingredient)
            } else if (line.hasPrefix("1.") || line.hasPrefix("2.") || line.hasPrefix("3.") || line.hasPrefix("4.") || line.hasPrefix("5.") || line.hasPrefix("6.") || line.hasPrefix("7.") || line.hasPrefix("8.") || line.hasPrefix("9.")) && currentSection == "instructions" {
                let instruction = String(line.dropFirst(2)).trimmingCharacters(in: .whitespacesAndNewlines)
                if !instruction.isEmpty {
                    instructions.append(instruction)
                }
            }
        }

        guard !name.isEmpty && !ingredients.isEmpty && !instructions.isEmpty else {
            return nil
        }

        return Recipe(
            name: name,
            difficulty: difficulty,
            preparationTime: time / 2,
            cookingTime: time / 2,
            ingredients: ingredients,
            instructions: instructions,
            tips: tips
        )
    }

    private func extractTimeFromText(_ text: String) -> Int {
        // "30 dakika" veya "1 saat 30 dakika" gibi metinlerden süre çıkar
        let numbers = text.components(separatedBy: CharacterSet.decimalDigits.inverted).compactMap { Int($0) }

        if text.contains("saat") && numbers.count >= 2 {
            return numbers[0] * 60 + (numbers.count > 1 ? numbers[1] : 0)
        } else if let minutes = numbers.first {
            return minutes
        }

        return 30 // Default
    }

    private func findMissingIngredients(recipes: [Recipe], available: [String]) -> [String] {
        let allNeededIngredients = Set(recipes.flatMap { $0.ingredients.map { $0.name.lowercased() } })
        let availableIngredients = Set(available.map { $0.lowercased() })

        return Array(allNeededIngredients.subtracting(availableIngredients))
    }

    private func updateIngredientAvailability(recipes: [Recipe], availableIngredients: [String]) -> [Recipe] {
        let availableIngredientsLower = Set(availableIngredients.map { $0.lowercased().trimmingCharacters(in: .whitespacesAndNewlines) })

        return recipes.map { recipe in
            let updatedIngredients = recipe.ingredients.map { ingredient in
                let ingredientNameLower = ingredient.name.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

                // Check if ingredient is available (exact match or contains)
                let isAvailable = availableIngredientsLower.contains(ingredientNameLower) ||
                                availableIngredientsLower.contains { available in
                                    available.contains(ingredientNameLower) || ingredientNameLower.contains(available)
                                }

                return RecipeIngredient(
                    id: ingredient.id,
                    name: ingredient.name,
                    amount: ingredient.amount,
                    unit: ingredient.unit,
                    isOptional: ingredient.isOptional,
                    isAvailable: isAvailable
                )
            }

            return Recipe(
                id: recipe.id,
                name: recipe.name,
                difficulty: recipe.difficulty,
                preparationTime: recipe.preparationTime,
                cookingTime: recipe.cookingTime,
                servings: recipe.servings,
                ingredients: updatedIngredients,
                instructions: recipe.instructions,
                tips: recipe.tips,
                cuisine: recipe.cuisine,
                tags: recipe.tags,
                createdAt: recipe.createdAt,
                isFavorite: recipe.isFavorite
            )
        }
    }

    private func generateSuggestions(for request: RecipeRequest) -> [String] {
        var suggestions: [String] = []

        if request.expiringIngredients.count > 0 {
            suggestions.append("Son kullanma tarihi yaklaşan ürünleri öncelikle kullanın")
        }

        if request.availableIngredients.count < 3 {
            suggestions.append("Daha fazla malzeme ekleyerek daha çeşitli tarifler alabilirsiniz")
        }

        suggestions.append("Eksik malzemeleri alışveriş listenize ekleyebilirsiniz")

        return suggestions
    }
}

// MARK: - OpenAI Errors

enum OpenAIError: LocalizedError {
    case invalidURL
    case invalidResponse
    case encodingError(Error)
    case networkError(Error)
    case apiError(String)
    case httpError(Int)
    case parsingError
    case timeout

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Geçersiz URL"
        case .invalidResponse:
            return "Geçersiz yanıt"
        case .encodingError(let error):
            return "Kodlama hatası: \(error.localizedDescription)"
        case .networkError(let error):
            return "Ağ hatası: \(error.localizedDescription)"
        case .apiError(let message):
            return "API hatası: \(message)"
        case .httpError(let code):
            return "HTTP hatası: \(code)"
        case .parsingError:
            return "Yanıt ayrıştırma hatası"
        case .timeout:
            return "İstek zaman aşımına uğradı. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin."
        }
    }
}
