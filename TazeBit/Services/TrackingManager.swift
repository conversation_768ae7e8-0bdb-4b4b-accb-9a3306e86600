//
//  TrackingManager.swift
//  TazeBit
//
//  Created for App Store Compliance
//

import Foundation
import AppTrackingTransparency
import AdSupport
import SwiftUI

@MainActor
class TrackingManager: ObservableObject {
    static let shared = TrackingManager()

    @Published var trackingStatus: ATTrackingManager.AuthorizationStatus = .notDetermined
    @Published var isTrackingAuthorized = false
    @Published var hasRequestedPermission = false

    private init() {
        updateTrackingStatus()
        hasRequestedPermission = UserDefaults.standard.bool(forKey: "hasRequestedTrackingPermission")
    }
    
    // MARK: - Tracking Authorization

    func requestTrackingPermission() async {
        // iOS 14.5+ için ATT framework kullan
        if #available(iOS 14.5, *) {
            let status = await ATTrackingManager.requestTrackingAuthorization()
            await MainActor.run {
                self.trackingStatus = status
                self.isTrackingAuthorized = (status == .authorized)
                self.hasRequestedPermission = true
                UserDefaults.standard.set(true, forKey: "hasRequestedTrackingPermission")
            }
        } else {
            // iOS 14.5 öncesi için IDFA kontrolü
            await MainActor.run {
                self.isTrackingAuthorized = ASIdentifierManager.shared().isAdvertisingTrackingEnabled
                self.hasRequestedPermission = true
                UserDefaults.standard.set(true, forKey: "hasRequestedTrackingPermission")
            }
        }
    }
    
    func updateTrackingStatus() {
        if #available(iOS 14.5, *) {
            trackingStatus = ATTrackingManager.trackingAuthorizationStatus
            isTrackingAuthorized = (trackingStatus == .authorized)
        } else {
            isTrackingAuthorized = ASIdentifierManager.shared().isAdvertisingTrackingEnabled
        }
    }
    
    // MARK: - Tracking Functions
    
    func getIDFA() -> String? {
        guard isTrackingAuthorized else { return nil }
        
        let idfa = ASIdentifierManager.shared().advertisingIdentifier
        return idfa.uuidString != "00000000-0000-0000-0000-000000000000" ? idfa.uuidString : nil
    }
    
    func canTrack() -> Bool {
        return isTrackingAuthorized
    }
    
    // MARK: - Privacy Compliance

    func shouldShowTrackingRequest() -> Bool {
        // iOS 14.5+ kontrolü
        guard #available(iOS 14.5, *) else { return false }

        // Daha önce izin istenmişse tekrar isteme
        if hasRequestedPermission { return false }

        // Tracking durumu belirlenmemişse izin iste
        return trackingStatus == .notDetermined
    }

    func showTrackingDialog() async {
        // Direkt Apple'ın native popup'ını göster
        await requestTrackingPermission()
    }
}


