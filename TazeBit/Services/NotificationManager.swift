import Foundation
import UserNotifications
import SwiftUI

@MainActor
class NotificationManager: ObservableObject {
    static let shared = NotificationManager()
    
    @Published var isAuthorized = false
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    private init() {
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    
    func requestAuthorization() async {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .badge, .sound]
            )
            
            await MainActor.run {
                self.isAuthorized = granted
                self.checkAuthorizationStatus()
            }
        } catch {
            print("Bildirim izni hatası: \(error)")
        }
    }
    
    func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.authorizationStatus = settings.authorizationStatus
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - Product Expiry Notifications
    
    func scheduleExpiryNotifications(for products: [Product]) {
        // Önce mevcut bildirimleri temizle
        cancelAllExpiryNotifications()
        
        let calendar = Calendar.current
        let now = Date()
        
        for product in products {
            // Sadece gelecekteki son kullanma tarihleri için bildirim oluştur
            guard product.expiryDate > now else { continue }
            
            // 1 gün önce bildirim
            if let oneDayBefore = calendar.date(byAdding: .day, value: -1, to: product.expiryDate),
               oneDayBefore > now {
                scheduleNotification(
                    for: product,
                    at: oneDayBefore,
                    title: "⚠️ Son Kullanma Tarihi Yaklaşıyor",
                    body: "\(product.name) yarın son kullanma tarihine ulaşacak!",
                    identifier: "expiry_1day_\(product.id)"
                )
            }
            
            // Son kullanma günü bildirim
            scheduleNotification(
                for: product,
                at: product.expiryDate,
                title: "🚨 Son Kullanma Tarihi Bugün",
                body: "\(product.name) bugün son kullanma tarihine ulaştı!",
                identifier: "expiry_today_\(product.id)"
            )
            
            // 3 gün önce bildirim (isteğe bağlı)
            if let threeDaysBefore = calendar.date(byAdding: .day, value: -3, to: product.expiryDate),
               threeDaysBefore > now {
                scheduleNotification(
                    for: product,
                    at: threeDaysBefore,
                    title: "📅 Son Kullanma Tarihi Hatırlatması",
                    body: "\(product.name) 3 gün içinde son kullanma tarihine ulaşacak.",
                    identifier: "expiry_3days_\(product.id)"
                )
            }
        }
    }
    
    private func scheduleNotification(
        for product: Product,
        at date: Date,
        title: String,
        body: String,
        identifier: String
    ) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.badge = 1
        
        // Ürün bilgilerini userInfo'ya ekle
        content.userInfo = [
            "productId": product.id.uuidString,
            "productName": product.name,
            "expiryDate": ISO8601DateFormatter().string(from: product.expiryDate),
            "type": "expiry"
        ]
        
        // Tarih bileşenlerini al
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        
        // Trigger oluştur
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        // Request oluştur
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: trigger
        )
        
        // Bildirimi planla
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Bildirim planlama hatası: \(error)")
            } else {
                print("Bildirim planlandı: \(title) - \(date)")
            }
        }
    }
    
    // MARK: - Notification Management
    
    func cancelAllExpiryNotifications() {
        UNUserNotificationCenter.current().getPendingNotificationRequests { requests in
            let expiryIdentifiers = requests
                .filter { $0.identifier.contains("expiry_") }
                .map { $0.identifier }
            
            UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: expiryIdentifiers)
            print("Mevcut son kullanma bildirimleri iptal edildi: \(expiryIdentifiers.count) adet")
        }
    }
    
    func cancelNotification(for productId: UUID) {
        let identifiers = [
            "expiry_1day_\(productId)",
            "expiry_today_\(productId)",
            "expiry_3days_\(productId)"
        ]
        
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiers)
    }
    
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await UNUserNotificationCenter.current().pendingNotificationRequests()
    }
    
    // MARK: - Badge Management
    
    func updateBadgeCount() {
        UNUserNotificationCenter.current().getDeliveredNotifications { notifications in
            DispatchQueue.main.async {
                let badgeCount = notifications.count
                UNUserNotificationCenter.current().setBadgeCount(badgeCount)
            }
        }
    }
    
    func clearBadge() {
        UNUserNotificationCenter.current().setBadgeCount(0)
    }
    
    // MARK: - Settings
    
    func openNotificationSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

// MARK: - Notification Delegate

class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    
    // Uygulama açıkken bildirim geldiğinde
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Bildirimi göster
        completionHandler([.banner, .sound, .badge])
    }
    
    // Bildirime tıklandığında
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        if let productIdString = userInfo["productId"] as? String,
           let productId = UUID(uuidString: productIdString) {
            
            // Ürün detay sayfasına yönlendir (NotificationCenter kullanarak)
            NotificationCenter.default.post(
                name: NSNotification.Name("OpenProductDetail"),
                object: nil,
                userInfo: ["productId": productId]
            )
        }
        
        completionHandler()
    }
}
