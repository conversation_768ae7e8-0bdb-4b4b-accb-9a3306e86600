//
//  DeleteAccountView.swift
//  TazeBit
//
//  Created for App Store Compliance
//

import SwiftUI

struct DeleteAccountView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Binding var isPresented: Bool
    
    @State private var confirmationText = ""
    @State private var showingFinalConfirmation = false
    @State private var isDeleting = false
    
    private let requiredText = "HESABI SIL"
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Warning Icon
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 80))
                        .foregroundColor(.red)
                        .padding(.top, 20)
                    
                    VStack(spacing: 16) {
                        Text("Hesabınızı Silmek Üzeresiniz")
                            .font(.title2)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                        
                        Text("Bu işlem geri alınamaz ve aşağıdaki verileriniz kalıcı olarak silinecek:")
                            .font(.body)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    }
                    
                    // Data that will be deleted
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Silinecek Veriler:")
                            .font(.headline)
                            .foregroundColor(.red)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            DataDeletionRow(icon: "person.circle", text: "Profil bilgileriniz")
                            DataDeletionRow(icon: "house", text: "Ev üyelikleriniz")
                            DataDeletionRow(icon: "carrot", text: "Tüm ürün kayıtlarınız")
                            DataDeletionRow(icon: "chart.bar", text: "Tüketim ve israf verileriniz")
                            DataDeletionRow(icon: "list.bullet", text: "Alışveriş listeleriniz")
                            DataDeletionRow(icon: "heart", text: "Favori tarifleriniz")
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Confirmation input
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Devam etmek için aşağıya '\(requiredText)' yazın:")
                            .font(.headline)
                        
                        TextField("Buraya yazın...", text: $confirmationText)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.allCharacters)
                            .disableAutocorrection(true)
                    }
                    
                    Spacer()
                    
                    // Action buttons
                    VStack(spacing: 12) {
                        Button("Hesabı Kalıcı Olarak Sil") {
                            showingFinalConfirmation = true
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                        .frame(maxWidth: .infinity)
                        .disabled(confirmationText != requiredText || isDeleting)
                        .tint(.red)
                        
                        Button("İptal") {
                            isPresented = false
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.large)
                        .frame(maxWidth: .infinity)
                        .disabled(isDeleting)
                    }
                    .padding(.horizontal)
                    .padding(.bottom)
                }
                .padding(.horizontal)
            }
            .navigationTitle("Hesabı Sil")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("İptal") {
                        isPresented = false
                    }
                    .disabled(isDeleting)
                }
            }
        }
        .alert("Son Uyarı", isPresented: $showingFinalConfirmation) {
            Button("İptal", role: .cancel) { }
            Button("Evet, Sil", role: .destructive) {
                Task {
                    await deleteAccount()
                }
            }
        } message: {
            Text("Bu işlem geri alınamaz. Hesabınız ve tüm verileriniz kalıcı olarak silinecek. Emin misiniz?")
        }
        .overlay {
            if isDeleting {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Hesabınız siliniyor...")
                        .font(.headline)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(radius: 10)
            }
        }
    }
    
    private func deleteAccount() async {
        isDeleting = true
        
        await authViewModel.deleteAccount()
        
        // If we reach here, deletion was successful
        await MainActor.run {
            isPresented = false
        }
    }
}

struct DataDeletionRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.red)
                .frame(width: 20)
            
            Text(text)
                .font(.body)
            
            Spacer()
        }
    }
}

#Preview {
    DeleteAccountView(isPresented: .constant(true))
        .environmentObject(AuthViewModel())
}
