//
//  ProductRowView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct ProductRowView: View {
    let product: Product
    let onEdit: (() -> Void)?
    let onDelete: () -> Void
    let onConsume: (() -> Void)?
    let onRefresh: (() -> Void)?

    @EnvironmentObject var consumptionViewModel: ConsumptionViewModel
    @State private var showingDetailSheet = false

    var body: some View {
        HStack(spacing: 12) {
            // Category Icon
            Text(categoryIcon)
                .font(.title2)

            // Product Info
            VStack(alignment: .leading, spacing: 4) {
                Text(product.name)
                    .font(.headline)
                    .lineLimit(1)

                if let category = ProductCategory(rawValue: product.category ?? "other") {
                    Text(category.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Text(expiryText)
                    .font(.caption)
                    .foregroundColor(expiryColor)
            }

            Spacer()

            // Quantity and Status
            VStack(alignment: .trailing, spacing: 4) {
                // Current quantity display
                let consumptionStatus = product.consumptionStatus(from: consumptionViewModel.consumptions)

                if product.currentQuantity < product.originalQuantity {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("Kalan: \(product.quantityWithUnit)")
                            .font(.caption2)
                            .foregroundColor(.tazeBitPrimary)
                            .fontWeight(.semibold)

                        Text("Toplam: \(product.originalQuantityWithUnit)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                } else {
                    Text(product.quantityWithUnit)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                HStack(spacing: 4) {
                    // Consumption status badge
                    if consumptionStatus != .active {
                        Text(consumptionStatus.icon)
                            .font(.caption2)
                    }

                    statusBadge
                }
            }
        }
        .padding()
        .background(Color.cardBackground)
        .cornerRadius(12)
        .onTapGesture {
            showingDetailSheet = true
        }
        .contextMenu {
            if let onConsume = onConsume {
                Button(action: onConsume) {
                    Label("Tüketim Kaydet", systemImage: "checkmark.circle")
                }
            }

            if let onEdit = onEdit {
                Button(action: onEdit) {
                    Label("Düzenle", systemImage: "pencil")
                }
            }

            Button(action: onDelete) {
                Label("Sil", systemImage: "trash")
            }
        }
        .sheet(isPresented: $showingDetailSheet) {
            ProductDetailView(product: product)
                .onDisappear {
                    onRefresh?()
                }
        }
    }

    private var categoryIcon: String {
        if let category = ProductCategory(rawValue: product.category ?? "other") {
            return category.icon
        }
        return "📦"
    }

    private var expiryText: String {
        // Tüketilmiş ürünler için özel mesaj
        if product.expiryStatus == .consumed {
            return "Tamamen tüketildi"
        }

        let days = product.daysUntilExpiry

        if days < 0 {
            return "\(-days) gün önce süresi geçti"
        } else if days == 0 {
            return "Bugün süresi bitiyor"
        } else if days == 1 {
            return "Yarın süresi bitiyor"
        } else {
            return "\(days) gün kaldı"
        }
    }

    private var expiryColor: Color {
        switch product.expiryStatus {
        case .expired:
            return .red
        case .expiringSoon:
            return .orange
        case .expiringThisWeek:
            return .yellow
        case .fresh:
            return .green
        case .consumed:
            return .blue
        }
    }

    private var statusBadge: some View {
        Text(product.expiryStatus.displayName)
            .font(.caption2)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(expiryColor.opacity(0.2))
            .foregroundColor(expiryColor)
            .cornerRadius(8)
    }
}

#Preview {
    VStack {
        ProductRowView(
            product: Product(
                id: UUID(),
                householdId: UUID(),
                name: "Süt",
                category: "dairy",
                storageLocation: "refrigerator",
                expiryDate: Calendar.current.date(byAdding: .day, value: 2, to: Date())!,
                purchaseDate: Date(),
                originalQuantity: 1,
                currentQuantity: 1,
                unit: "liter",
                notes: nil,
                addedBy: UUID(),
                shoppingListItemId: nil,
                createdAt: Date(),
                updatedAt: Date()
            ),
            onEdit: { print("Edit tapped") },
            onDelete: { print("Delete tapped") },
            onConsume: { print("Consume tapped") },
            onRefresh: { print("Refresh tapped") }
        )

        ProductRowView(
            product: Product(
                id: UUID(),
                householdId: UUID(),
                name: "Ekmek",
                category: "grains",
                storageLocation: "pantry",
                expiryDate: Calendar.current.date(byAdding: .day, value: -1, to: Date())!,
                purchaseDate: Date(),
                originalQuantity: 2,
                currentQuantity: 2,
                unit: "piece",
                notes: nil,
                addedBy: UUID(),
                shoppingListItemId: nil,
                createdAt: Date(),
                updatedAt: Date()
            ),
            onEdit: { print("Edit tapped") },
            onDelete: { print("Delete tapped") },
            onConsume: { print("Consume tapped") },
            onRefresh: { print("Refresh tapped") }
        )
    }
    .padding()
}
