//
//  JoinHouseholdView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 27.05.2025.
//

import SwiftUI

struct JoinHouseholdView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 0) {
                        // Header Section
                        headerSection
                        
                        // Content Section
                        VStack(spacing: 24) {
                            // Icon and Title
                            iconSection
                            
                            // Form Section
                            formSection
                            
                            // Action Buttons
                            actionButtons
                            
                            // Error Message
                            if !householdViewModel.errorMessage.isEmpty {
                                errorSection
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Background with gradient
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [
                            Color.tazeBitPrimary,
                            Color.tazeBitPrimary.opacity(0.8)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(height: 120)
                .overlay(
                    VStack {
                        Spacer()
                        
                        HStack {
                            // Close Button
                            Button(action: { dismiss() }) {
                                Image(systemName: "xmark")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                                    .frame(width: 44, height: 44)
                                    .background(
                                        Circle()
                                            .fill(.white.opacity(0.2))
                                    )
                            }
                            
                            Spacer()
                            
                            Text("Eve Katıl")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                            
                            Spacer()
                            
                            // Invisible button for balance
                            Color.clear
                                .frame(width: 44, height: 44)
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 16)
                    }
                )
        }
        .ignoresSafeArea(.all)
    }
    
    private var iconSection: some View {
        VStack(spacing: 16) {
            // Icon
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.tazeBitPrimary.opacity(0.1),
                                Color.tazeBitPrimary.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                
                Image(systemName: "house.and.flag")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.tazeBitPrimary)
            }
            
            // Title and Description
            VStack(spacing: 8) {
                Text("Davet Kodu ile Katıl")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)
                
                Text("Ev sahibinden aldığınız 6 haneli davet kodunu girin")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
    }
    
    private var formSection: some View {
        VStack(spacing: 20) {
            // Invite Code Input
            VStack(alignment: .leading, spacing: 8) {
                Text("Davet Kodu")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)
                
                TextField("Örnek: ABC123", text: $householdViewModel.inviteCodeInput)
                    .textFieldStyle(ModernTextFieldStyle())
                    .textInputAutocapitalization(.characters)
                    .autocorrectionDisabled()
                    .onChange(of: householdViewModel.inviteCodeInput) { _, newValue in
                        // Limit to 6 characters and uppercase
                        let filtered = String(newValue.uppercased().prefix(6))
                        if filtered != newValue {
                            householdViewModel.inviteCodeInput = filtered
                        }
                    }
            }
        }
        .padding(.horizontal, 4)
    }
    
    private var actionButtons: some View {
        VStack(spacing: 16) {
            // Join Button
            Button(action: {
                Task {
                    await householdViewModel.joinHouseholdWithCode()
                    if householdViewModel.errorMessage.isEmpty {
                        dismiss()
                    }
                }
            }) {
                HStack {
                    if householdViewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.9)
                            .tint(.white)
                    } else {
                        Image(systemName: "house.and.flag.fill")
                            .font(.system(size: 16, weight: .medium))
                    }
                    
                    Text(householdViewModel.isLoading ? "Katılıyor..." : "Eve Katıl")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.tazeBitPrimary,
                                    Color.tazeBitPrimary.opacity(0.8)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
                .scaleEffect(householdViewModel.isLoading ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: householdViewModel.isLoading)
            }
            .disabled(householdViewModel.isLoading || householdViewModel.inviteCodeInput.count < 6)
            .opacity(householdViewModel.inviteCodeInput.count < 6 ? 0.6 : 1.0)
            
            // Cancel Button
            Button(action: { dismiss() }) {
                Text("İptal")
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
            .disabled(householdViewModel.isLoading)
        }
    }
    
    private var errorSection: some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.red)
            
            Text(householdViewModel.errorMessage)
                .font(.subheadline)
                .foregroundColor(.red)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.1))
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
        .transition(.opacity.combined(with: .scale(scale: 0.95)))
    }
}

struct ModernTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .font(.system(size: 16, weight: .medium, design: .monospaced))
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.cardBackground)
                    .stroke(Color.borderColor, lineWidth: 1)
            )
            .foregroundColor(.textPrimary)
    }
}

#Preview {
    JoinHouseholdView()
        .environmentObject(HouseholdViewModel())
}
