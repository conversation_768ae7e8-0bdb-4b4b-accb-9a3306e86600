//
//  ProductListView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> Korkmaz on 26.05.2025.
//

import SwiftUI

struct ProductListView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var consumptionViewModel: ConsumptionViewModel
    @State private var searchText = ""
    @State private var selectedFilter: ExpiryStatus? = nil
    @State private var productToConsume: Product?
    @State private var showingPhotoDetection = false
    @State private var selectedTab = 0 // 0: Aktif <PERSON>, 1: <PERSON><PERSON><PERSON><PERSON>, 2: Tüketilen Ürünler
    @State private var selectedDateFilter: DateFilter? = nil
    @State private var selectedCategory: ProductCategory? = nil
    @State private var selectedStorageLocation: StorageLocation? = nil
    @State private var sortOption: ProductSortOption = .expiryDateAsc
    @State private var viewMode: ViewMode = .card
    @State private var itemsPerPage = 20
    @State private var currentPage = 0

    var activeProducts: [Product] {
        // Aktif ürünler: Kalan miktarı > 0 ve süresi geçmemiş
        var products = productViewModel.products.filter { product in
            product.currentQuantity > 0 && product.expiryStatus != .expired
        }

        // Apply all filters
        products = applyFilters(to: products)

        // Apply sorting
        products = applySorting(to: products)

        return products
    }

    var wastedProducts: [Product] {
        // İsraf edilen ürünler: Süresi geçmiş VEYA israf kaydı olan ürünler
        var products = productViewModel.products.filter { product in
            (product.currentQuantity > 0 && product.expiryStatus == .expired) ||
            hasWastedConsumption(product: product)
        }

        // Apply search filter
        if !searchText.isEmpty {
            products = products.filter { product in
                product.name.localizedCaseInsensitiveContains(searchText) ||
                (product.category?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }

        // Apply date filter
        if let dateFilter = selectedDateFilter {
            products = products.filter { dateFilter.matches(product: $0) }
        }

        return products.sorted { $0.expiryDate < $1.expiryDate } // En eski süresi geçenler üstte
    }

    var consumedProducts: [Product] {
        // Tüketilen ürünler: Tamamen tüketilmiş VEYA herhangi bir tüketim kaydı olan ürünler
        var products = productViewModel.products.filter { product in
            // Tamamen tüketilmiş ürünler
            if product.currentQuantity == 0 {
                return true
            }

            // Herhangi bir tüketim kaydı olan ürünler
            return hasConsumedConsumption(product: product)
        }

        // Apply search filter
        if !searchText.isEmpty {
            products = products.filter { product in
                product.name.localizedCaseInsensitiveContains(searchText) ||
                (product.category?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }

        // Apply date filter
        if let dateFilter = selectedDateFilter {
            products = products.filter { dateFilter.matches(product: $0) }
        }

        // Apply category filter
        if let category = selectedCategory {
            products = products.filter { $0.category == category.rawValue }
        }

        // Apply storage location filter
        if let location = selectedStorageLocation {
            products = products.filter { $0.storageLocation == location.rawValue }
        }

        return products.sorted { $0.updatedAt > $1.updatedAt } // En son tüketilenler üstte
    }

    var filteredProducts: [Product] {
        let products = switch selectedTab {
        case 0: activeProducts
        case 1: wastedProducts
        case 2: consumedProducts
        default: activeProducts
        }

        // Apply pagination
        return applyPagination(to: products)
    }

    // MARK: - Filter Functions
    private func applyFilters(to products: [Product]) -> [Product] {
        var filtered = products

        // Search filter
        if !searchText.isEmpty {
            filtered = filtered.filter { product in
                product.name.localizedCaseInsensitiveContains(searchText) ||
                (product.category?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }

        // Status filter
        if let filter = selectedFilter {
            filtered = filtered.filter { $0.expiryStatus == filter }
        }

        // Date filter
        if let dateFilter = selectedDateFilter {
            filtered = filtered.filter { dateFilter.matches(product: $0) }
        }

        // Category filter
        if let category = selectedCategory {
            filtered = filtered.filter { $0.category == category.rawValue }
        }

        // Storage location filter
        if let location = selectedStorageLocation {
            filtered = filtered.filter { $0.storageLocation == location.rawValue }
        }

        return filtered
    }

    private func applySorting(to products: [Product]) -> [Product] {
        switch sortOption {
        case .expiryDateAsc:
            return products.sorted { $0.expiryDate < $1.expiryDate }
        case .expiryDateDesc:
            return products.sorted { $0.expiryDate > $1.expiryDate }
        case .nameAsc:
            return products.sorted { $0.name.localizedCompare($1.name) == .orderedAscending }
        case .nameDesc:
            return products.sorted { $0.name.localizedCompare($1.name) == .orderedDescending }
        case .quantityAsc:
            return products.sorted { $0.currentQuantity < $1.currentQuantity }
        case .quantityDesc:
            return products.sorted { $0.currentQuantity > $1.currentQuantity }
        case .addedDateAsc:
            return products.sorted { $0.createdAt < $1.createdAt }
        case .addedDateDesc:
            return products.sorted { $0.createdAt > $1.createdAt }
        }
    }

    private func applyPagination(to products: [Product]) -> [Product] {
        let startIndex = currentPage * itemsPerPage
        let endIndex = min(startIndex + itemsPerPage, products.count)

        guard startIndex < products.count else { return [] }

        return Array(products[startIndex..<endIndex])
    }

    private func loadMoreProducts() {
        let allProducts = switch selectedTab {
        case 0: activeProducts
        case 1: wastedProducts
        case 2: consumedProducts
        default: activeProducts
        }

        let totalPages = (allProducts.count + itemsPerPage - 1) / itemsPerPage

        if currentPage < totalPages - 1 {
            currentPage += 1
        }
    }

    private func resetPagination() {
        currentPage = 0
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 0) {
                        // Header Section
                        headerSection

                        // Content sections
                        VStack(spacing: 20) {
                            if householdViewModel.selectedHousehold != nil {
                                // Tab Selector
                                tabSelector

                                // Filter and Sort Controls
                                if selectedTab == 0 || selectedTab == 1 || selectedTab == 2 {
                                    advancedFilterSection
                                }

                                // Product List
                                if filteredProducts.isEmpty {
                                    emptyStateView
                                } else {
                                    productListWithPagination
                                }
                            } else {
                                noHouseholdSelectedView
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
            .sheet(isPresented: $productViewModel.showingAddProduct) {
                AddProductView()
                    .environmentObject(householdViewModel)
                    .environmentObject(productViewModel)
            }
            .sheet(item: $productViewModel.editingProduct) { _ in
                AddProductView()
                    .environmentObject(householdViewModel)
                    .environmentObject(productViewModel)
            }
            .sheet(isPresented: $productViewModel.showingDeleteConfirmation) {
                DeleteConfirmationView()
                    .environmentObject(productViewModel)
            }
            .sheet(item: $productToConsume) { product in
                ConsumptionView(product: product)
                    .environmentObject(productViewModel)
                    .environmentObject(consumptionViewModel)
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $showingPhotoDetection) {
                if let household = householdViewModel.selectedHousehold {
                    PhotoDetectionFlow(householdId: household.id)
                        .environmentObject(productViewModel)
                }
            }
            .onAppear {
                // Sayfa her göründüğünde ürün listesini yenile
                if let household = householdViewModel.selectedHousehold {
                    Task {
                        await productViewModel.fetchProducts(for: household.id)
                        await consumptionViewModel.loadConsumptions(for: household.id)
                    }
                }
            }
            .onChange(of: productToConsume) { oldValue, newValue in
                // Tüketim sheet'i kapandığında refresh yap
                if oldValue != nil && newValue == nil {
                    if let household = householdViewModel.selectedHousehold {
                        Task {
                            await productViewModel.fetchProducts(for: household.id)
                            await consumptionViewModel.loadConsumptions(for: household.id)
                        }
                    }
                }
            }
            .onChange(of: productViewModel.products) {
                // Products array changed, UI will automatically update
            }
            .onChange(of: consumptionViewModel.consumptions) {
                // Consumptions array changed, UI will automatically update
            }
        }
    }

    // MARK: - Tab Selector
    private var tabSelector: some View {
        HStack(spacing: 0) {
            // Aktif Ürünler Tab
            Button(action: {
                selectedTab = 0
                selectedFilter = nil // Sekme değiştiğinde filtreyi sıfırla
                selectedDateFilter = nil
                selectedCategory = nil
                selectedStorageLocation = nil
                resetPagination()
            }) {
                VStack(spacing: 4) {
                    HStack {
                        Image(systemName: "tray.fill")
                            .font(.caption)
                        Text("Aktif")
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    Rectangle()
                        .fill(selectedTab == 0 ? Color.tazeBitPrimary : Color.clear)
                        .frame(height: 2)
                }
                .foregroundColor(selectedTab == 0 ? Color.tazeBitPrimary : .textSecondary)
                .frame(maxWidth: .infinity)
            }

            // İsraf Edilen Ürünler Tab
            Button(action: {
                selectedTab = 1
                selectedFilter = nil // Sekme değiştiğinde filtreyi sıfırla
                selectedDateFilter = nil
                selectedCategory = nil
                selectedStorageLocation = nil
                resetPagination()
            }) {
                VStack(spacing: 4) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.caption)
                        Text("İsraf")
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    Rectangle()
                        .fill(selectedTab == 1 ? Color.orange : Color.clear)
                        .frame(height: 2)
                }
                .foregroundColor(selectedTab == 1 ? Color.orange : .textSecondary)
                .frame(maxWidth: .infinity)
            }

            // Tüketilen Ürünler Tab
            Button(action: {
                selectedTab = 2
                selectedFilter = nil // Sekme değiştiğinde filtreyi sıfırla
                selectedDateFilter = nil
                selectedCategory = nil
                selectedStorageLocation = nil
                resetPagination()
            }) {
                VStack(spacing: 4) {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.caption)
                        Text("Tüketilen")
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    Rectangle()
                        .fill(selectedTab == 2 ? Color.tazeBitPrimary : Color.clear)
                        .frame(height: 2)
                }
                .foregroundColor(selectedTab == 2 ? Color.tazeBitPrimary : .textSecondary)
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Header Section
    private var headerSection: some View {
        ZStack {
            // Primary Color Background
            Color.tazeBitPrimary
                .frame(height: 200)
                .clipShape(
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 30,
                        bottomTrailingRadius: 30,
                        topTrailingRadius: 0
                    )
                )
                .ignoresSafeArea(.all)

            VStack(spacing: 16) {
                // Top Section
                HStack {
                    // Left AI Button
                    Button(action: {
                        showingPhotoDetection = true
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "camera.viewfinder")
                                .font(.subheadline)
                            Text("AI")
                                .font(.caption)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.white.opacity(0.2))
                        )
                    }
                    .disabled(householdViewModel.selectedHousehold == nil)
                    .opacity(householdViewModel.selectedHousehold == nil ? 0.5 : 1.0)

                    Spacer()

                    // Center Title
                    VStack(spacing: 4) {
                        Text("Ürünler 📦")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        if let household = householdViewModel.selectedHousehold {
                            let count = selectedTab == 0 ? activeProducts.count : selectedTab == 1 ? wastedProducts.count : consumedProducts.count
                            Text("\(household.name) - \(count) ürün")
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.9))
                        } else {
                            Text("Ev seçilmedi")
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.9))
                        }
                    }

                    Spacer()

                    // Right Add Button
                    Button(action: {
                        productViewModel.showAddProduct()
                    }) {
                        ZStack {
                            // Outer transparent circle
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .frame(width: 44, height: 44)

                            // Inner solid circle
                            Circle()
                                .fill(Color.white)
                                .frame(width: 32, height: 32)

                            // Icon
                            Image(systemName: "plus")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.tazeBitPrimary)
                        }
                    }
                    .disabled(householdViewModel.selectedHousehold == nil)
                    .opacity(householdViewModel.selectedHousehold == nil ? 0.5 : 1.0)
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)

                // Search Bar
                if householdViewModel.selectedHousehold != nil {
                    searchBar
                        .padding(.horizontal, 20)
                }
            }
            .padding(.bottom, 20)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Search Bar
    private var searchBar: some View {
        HStack(spacing: 12) {
            // Search Icon
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.7))

            // Text Field
            TextField("Ürün adı ile ara...", text: $searchText)
                .font(.subheadline)
                .foregroundColor(.white)
                .accentColor(.white)
                .placeholder(when: searchText.isEmpty) {
                    Text("Ürün adı ile ara...")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.6))
                }
                .onChange(of: searchText) {
                    resetPagination()
                }

            // Clear Button
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.2))
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
        )
    }

    // MARK: - Advanced Filter Section
    private var advancedFilterSection: some View {
        VStack(spacing: 12) {
            // Filter Header
            HStack {
                Image(systemName: "line.3.horizontal.decrease.circle.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Filtreler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            // Dropdown Filters Row
            HStack(spacing: 8) {
                // Category Dropdown
                categoryDropdown

                // Sort Dropdown
                sortDropdown

                // Status Dropdown (only for active products)
                if selectedTab == 0 {
                    statusDropdown
                }

                Spacer()
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .shadow(color: Color.cardShadow, radius: 2, x: 0, y: 1)
    }

    // MARK: - Dropdown Components
    private var categoryDropdown: some View {
        Menu {
            Button("Tümü") {
                selectedCategory = nil
                resetPagination()
            }

            ForEach(ProductCategory.allCases, id: \.self) { category in
                Button(action: {
                    selectedCategory = category
                    resetPagination()
                }) {
                    HStack {
                        Image(systemName: category.icon)
                        Text(category.displayName)
                        Spacer()
                        if selectedCategory == category {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: "tag.fill")
                    .font(.caption)
                Text(selectedCategory?.displayName ?? "Kategori")
                    .font(.caption)
                    .fontWeight(.medium)
                Image(systemName: "chevron.down")
                    .font(.caption2)
            }
            .foregroundColor(.textPrimary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedCategory != nil ? Color.tazeBitPrimary.opacity(0.1) : Color.gray.opacity(0.1))
                    .stroke(selectedCategory != nil ? Color.tazeBitPrimary : Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
    }

    private var sortDropdown: some View {
        Menu {
            ForEach(ProductSortOption.allCases, id: \.self) { option in
                Button(action: {
                    sortOption = option
                    resetPagination()
                }) {
                    HStack {
                        Image(systemName: option.icon)
                        Text(option.displayName)
                        Spacer()
                        if sortOption == option {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: "arrow.up.arrow.down")
                    .font(.caption)
                Text("Sırala")
                    .font(.caption)
                    .fontWeight(.medium)
                Image(systemName: "chevron.down")
                    .font(.caption2)
            }
            .foregroundColor(.textPrimary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.1))
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
    }

    private var statusDropdown: some View {
        Menu {
            Button("Tümü") {
                selectedFilter = nil
                resetPagination()
            }

            // Aktif sekmesinde süresi geçmiş ürünleri gösterme
            ForEach(ExpiryStatus.allCases.filter { $0 != .consumed && $0 != .expired }, id: \.self) { status in
                Button(action: {
                    selectedFilter = status
                    resetPagination()
                }) {
                    HStack {
                        Circle()
                            .fill(colorForStatus(status))
                            .frame(width: 8, height: 8)
                        Text(status.displayName)
                        Spacer()
                        if selectedFilter == status {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 6) {
                Image(systemName: "clock.fill")
                    .font(.caption)
                Text(selectedFilter?.displayName ?? "Durum")
                    .font(.caption)
                    .fontWeight(.medium)
                Image(systemName: "chevron.down")
                    .font(.caption2)
            }
            .foregroundColor(.textPrimary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedFilter != nil ? Color.tazeBitPrimary.opacity(0.1) : Color.gray.opacity(0.1))
                    .stroke(selectedFilter != nil ? Color.tazeBitPrimary : Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
    }















    private var productList: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(selectedTab == 0 ? "Aktif Ürünler" : selectedTab == 1 ? "İsraf Edilen Ürünler" : "Tüketilen Ürünler")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.textPrimary)

            LazyVStack(spacing: 12) {
                ForEach(filteredProducts) { product in
                    ProductRowView(
                        product: product,
                        onEdit: (selectedTab == 0 || selectedTab == 1) ? {
                            productViewModel.editProduct(product)
                        } : nil,
                        onDelete: {
                            productViewModel.showDeleteConfirmation(for: product)
                        },
                        onConsume: (selectedTab == 0 || selectedTab == 1) ? {
                            productToConsume = product
                        } : nil,
                        onRefresh: {
                            if let household = householdViewModel.selectedHousehold {
                                Task {
                                    await productViewModel.fetchProducts(for: household.id)
                                    await consumptionViewModel.loadConsumptions(for: household.id)
                                }
                            }
                        }
                    )
                    .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
                    .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                        Button("Sil") {
                            productViewModel.showDeleteConfirmation(for: product)
                        }
                        .tint(.red)

                        if selectedTab == 0 || selectedTab == 1 {
                            Button("Düzenle") {
                                productViewModel.editProduct(product)
                            }
                            .tint(.tazeBitPrimary)

                            Button("Tüketim") {
                                productToConsume = product
                            }
                            .tint(.green)
                        }
                    }
                }
            }
        }
    }

    // MARK: - Product List with Pagination
    private var productListWithPagination: some View {
        VStack(spacing: 16) {
            // Product count and view mode info
            productListHeader

            // Product list
            productList

            // Load more button
            loadMoreButton
        }
    }

    private var productListHeader: some View {
        HStack {
            // Total count info
            VStack(alignment: .leading, spacing: 4) {
                Text("Toplam \(getTotalProductCount()) ürün")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Text("Gösterilen: \(filteredProducts.count)")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }

            Spacer()

            // Quick filter chips for critical items
            if selectedTab == 0 {
                quickFilterChips
            }
        }
        .padding(.horizontal, 20)
    }

    private var quickFilterChips: some View {
        HStack(spacing: 8) {
            let expiringSoonCount = activeProducts.filter { $0.isExpiringSoon }.count
            let freshCount = activeProducts.filter { $0.expiryStatus == .fresh }.count

            // Sarı buton - Yakında bitecek ürünler
            if expiringSoonCount > 0 {
                Button(action: {
                    selectedFilter = .expiringSoon
                    resetPagination()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "clock.fill")
                            .font(.caption2)
                        Text("\(expiringSoonCount)")
                            .font(.caption2)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.orange)
                    )
                }
            }

            // Yeşil buton - Taze ürünler
            if freshCount > 0 {
                Button(action: {
                    selectedFilter = .fresh
                    resetPagination()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "leaf.fill")
                            .font(.caption2)
                        Text("\(freshCount)")
                            .font(.caption2)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.green)
                    )
                }
            }
        }
    }



    private var loadMoreButton: some View {
        Group {
            if hasMoreProducts {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        loadMoreProducts()
                    }
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.down.circle.fill")
                            .font(.headline)

                        Text("Daha Fazla Yükle")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Text("(\(remainingProductsCount) kaldı)")
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                    }
                    .foregroundColor(.tazeBitPrimary)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color.cardBackground)
                            .stroke(Color.tazeBitPrimary, lineWidth: 2)
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 20)
                .padding(.top, 8)
            }
        }
    }

    // MARK: - Helper Properties
    private var hasMoreProducts: Bool {
        let totalProducts = getTotalProductCount()
        let displayedProducts = (currentPage + 1) * itemsPerPage
        return displayedProducts < totalProducts
    }

    private var remainingProductsCount: Int {
        let totalProducts = getTotalProductCount()
        let displayedProducts = (currentPage + 1) * itemsPerPage
        return max(0, totalProducts - displayedProducts)
    }

    private func getTotalProductCount() -> Int {
        switch selectedTab {
        case 0: return activeProducts.count
        case 1: return wastedProducts.count
        case 2: return consumedProducts.count
        default: return activeProducts.count
        }
    }

    private var emptyStateView: some View {
        VStack(spacing: 24) {
            // Icon
            ZStack {
                Circle()
                    .fill(Color.tazeBitPrimary.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: getEmptyStateIcon())
                    .font(.system(size: 50))
                    .foregroundColor(.tazeBitPrimary)
            }

            // Text Content
            VStack(spacing: 8) {
                Text(getEmptyStateTitle())
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text(getEmptyStateSubtitle())
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }

            // Action Buttons (sadece aktif ürünler sekmesinde ve arama yoksa)
            if searchText.isEmpty && selectedTab == 0 {
                VStack(spacing: 12) {
                    // AI Detection Button
                    Button(action: {
                        showingPhotoDetection = true
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "camera.viewfinder")
                                .font(.headline)

                            Text("AI ile Fotoğraftan Ekle")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(
                            LinearGradient.tazeBitGradient
                                .cornerRadius(25)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())

                    // Manual Add Button
                    Button(action: {
                        productViewModel.showAddProduct()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "plus.circle.fill")
                                .font(.headline)

                            Text("Manuel Olarak Ekle")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.tazeBitPrimary)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.cardBackground)
                                .stroke(Color.tazeBitPrimary, lineWidth: 2)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }

    private var noHouseholdSelectedView: some View {
        VStack(spacing: 24) {
            // Icon
            ZStack {
                Circle()
                    .fill(Color.orange.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: "house.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.orange)
            }

            // Text Content
            VStack(spacing: 8) {
                Text("Ev Seçilmedi")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text("Ürünleri görüntülemek için önce bir ev seçin")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }

    private func colorForStatus(_ status: ExpiryStatus) -> Color {
        switch status {
        case .expired:
            return .red
        case .expiringSoon:
            return .orange
        case .expiringThisWeek:
            return .yellow
        case .fresh:
            return .green
        case .consumed:
            return .blue
        }
    }

    private func getEmptyStateIcon() -> String {
        if !searchText.isEmpty {
            return "magnifyingglass"
        }

        switch selectedTab {
        case 0: return "tray.fill"
        case 1: return "exclamationmark.triangle.fill"
        case 2: return "checkmark.circle.fill"
        default: return "tray.fill"
        }
    }

    private func getEmptyStateTitle() -> String {
        if !searchText.isEmpty {
            return "Arama sonucu bulunamadı"
        }

        switch selectedTab {
        case 0: return "Henüz aktif ürün yok"
        case 1: return "Henüz israf edilen ürün yok"
        case 2: return "Henüz tüketilen ürün yok"
        default: return "Henüz aktif ürün yok"
        }
    }

    private func getEmptyStateSubtitle() -> String {
        if !searchText.isEmpty {
            return "Farklı anahtar kelimeler deneyin"
        }

        switch selectedTab {
        case 0: return "İlk ürününüzü ekleyerek başlayın"
        case 1: return "Süresi geçen ürünler burada görünecek"
        case 2: return "Ürünlerinizi tükettikçe burada görünecek"
        default: return "İlk ürününüzü ekleyerek başlayın"
        }
    }

    // MARK: - Consumption Helper Functions
    private func hasAnyConsumption(product: Product) -> Bool {
        return consumptionViewModel.consumptions.contains { $0.productId == product.id }
    }

    private func hasConsumedConsumption(product: Product) -> Bool {
        return consumptionViewModel.consumptions.contains {
            $0.productId == product.id && $0.consumedQuantity > 0
        }
    }

    private func hasWastedConsumption(product: Product) -> Bool {
        return consumptionViewModel.consumptions.contains {
            $0.productId == product.id && $0.wastedQuantity > 0
        }
    }
}



// MARK: - View Extensions
extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content) -> some View {

        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}

// MARK: - Date Filter Enum
enum DateFilter: String, CaseIterable {
    case today = "today"
    case thisWeek = "thisWeek"
    case thisMonth = "thisMonth"
    case expired = "expired"

    var displayName: String {
        switch self {
        case .today:
            return "Bugün"
        case .thisWeek:
            return "Bu Hafta"
        case .thisMonth:
            return "Bu Ay"
        case .expired:
            return "Süresi Geçen"
        }
    }

    func matches(product: Product) -> Bool {
        let calendar = Calendar.current
        let today = Date()

        switch self {
        case .today:
            return calendar.isDate(product.expiryDate, inSameDayAs: today)
        case .thisWeek:
            let weekRange = calendar.dateInterval(of: .weekOfYear, for: today)
            return weekRange?.contains(product.expiryDate) ?? false
        case .thisMonth:
            let monthRange = calendar.dateInterval(of: .month, for: today)
            return monthRange?.contains(product.expiryDate) ?? false
        case .expired:
            return product.expiryDate < today
        }
    }
}

#Preview {
    ProductListView()
        .environmentObject(HouseholdViewModel())
        .environmentObject(ProductViewModel())
        .environmentObject(ConsumptionViewModel())
}
