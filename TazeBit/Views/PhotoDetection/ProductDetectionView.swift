import SwiftUI

struct ProductDetectionView: View {
    let image: UIImage
    let householdId: UUID
    let onDismiss: (() -> Void)?

    @State private var detectedProducts: [DetectedProduct] = []
    @State private var selectedProducts: Set<UUID> = []
    @State private var isAnalyzing = false
    @State private var errorMessage = ""
    @State private var showingSuccess = false
    @State private var isAddingProducts = false

    @EnvironmentObject var productViewModel: ProductViewModel
    @Environment(\.dismiss) private var dismiss

    init(image: UIImage, householdId: UUID, onDismiss: (() -> Void)? = nil) {
        self.image = image
        self.householdId = householdId
        self.onDismiss = onDismiss
    }

    var body: some View {
        ZStack {
            Color.tazeBitBackground
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Header Section
                headerSection

                // Content
                ScrollView {
                    VStack(spacing: 20) {
                        // Fotoğraf g<PERSON>im<PERSON>
                        imageSection

                        if isAnalyzing {
                            analysisSection
                        } else if !detectedProducts.isEmpty {
                            // Tespit edilen ü<PERSON>
                            detectedProductsSection

                            // Action buttons
                            actionButtonsSection
                        } else if !errorMessage.isEmpty {
                            errorSection
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
        }
        .onAppear {
            analyzeImage()
        }
        .alert("Başarılı!", isPresented: $showingSuccess) {
            Button("Tamam") {
                if let onDismiss = onDismiss {
                    onDismiss()
                } else {
                    dismiss()
                }
            }
        } message: {
            Text("\(selectedProducts.count) ürün başarıyla eklendi!")
        }
    }

    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        if let onDismiss = onDismiss {
                            onDismiss()
                        } else {
                            dismiss()
                        }
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    Text("AI Ürün Tespiti")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    // Balance space
                    Color.clear
                        .frame(width: 32, height: 32)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    private var imageSection: some View {
        VStack(spacing: 12) {
            Text("Analiz Edilen Fotoğraf")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.textPrimary)

            Image(uiImage: image)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxHeight: 200)
                .cornerRadius(12)
                .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    private var analysisSection: some View {
        VStack(spacing: 16) {
            // AI Icon
            ZStack {
                Circle()
                    .fill(Color.tazeBitPrimary.opacity(0.1))
                    .frame(width: 80, height: 80)

                Image(systemName: "brain.head.profile")
                    .font(.system(size: 32))
                    .foregroundColor(.tazeBitPrimary)
            }

            VStack(spacing: 8) {
                Text("AI Analiz Ediyor...")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text("Fotoğrafınızdaki ürünler tespit ediliyor")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }

            ProgressView()
                .scaleEffect(1.2)
                .tint(.tazeBitPrimary)
        }
        .padding(40)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    private var detectedProductsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Text("Tespit Edilen Ürünler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("\(detectedProducts.count) ürün")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
            }

            // Select All/None buttons
            HStack {
                Button(action: selectAll) {
                    Text("Tümünü Seç")
                        .font(.subheadline)
                        .foregroundColor(.tazeBitPrimary)
                }

                Spacer()

                Button(action: selectNone) {
                    Text("Hiçbirini Seçme")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)
                }
            }

            // Products List
            LazyVStack(spacing: 12) {
                ForEach(detectedProducts) { product in
                    ProductDetectionRow(
                        product: product,
                        isSelected: selectedProducts.contains(product.id)
                    ) {
                        toggleProductSelection(product.id)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // Add Selected Products Button
            Button(action: addSelectedProducts) {
                HStack {
                    if isAddingProducts {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text(isAddingProducts ? "Ekleniyor..." : "Seçilen Ürünleri Ekle (\(selectedProducts.count))")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill((selectedProducts.isEmpty || isAddingProducts) ? Color.gray : Color.tazeBitPrimary)
                )
            }
            .disabled(selectedProducts.isEmpty || isAddingProducts)

            // Retry Button
            Button(action: analyzeImage) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16, weight: .medium))

                    Text("Tekrar Analiz Et")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.textPrimary)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.cardBackground)
                        .stroke(Color.borderColor, lineWidth: 1)
                )
            }
            .disabled(isAnalyzing)
        }
        .padding(.bottom, 20)
    }

    private var errorSection: some View {
        VStack(spacing: 16) {
            // Error Icon
            ZStack {
                Circle()
                    .fill(Color.red.opacity(0.1))
                    .frame(width: 80, height: 80)

                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.red)
            }

            VStack(spacing: 8) {
                Text("Analiz Başarısız")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text(errorMessage)
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: analyzeImage) {
                Text("Tekrar Dene")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.tazeBitPrimary)
                    )
            }
        }
        .padding(40)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Actions
    private func analyzeImage() {
        Task {
            isAnalyzing = true
            errorMessage = ""
            detectedProducts = []
            selectedProducts = []

            do {
                let products = try await OpenAIVisionService.shared.analyzeImage(image)

                await MainActor.run {
                    detectedProducts = products
                    // Tüm ürünleri varsayılan olarak seç
                    selectedProducts = Set(products.map { $0.id })
                    print("✅ \(products.count) ürün tespit edildi")
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    print("❌ Vision analiz hatası: \(error)")
                }
            }

            isAnalyzing = false
        }
    }

    private func toggleProductSelection(_ productId: UUID) {
        if selectedProducts.contains(productId) {
            selectedProducts.remove(productId)
        } else {
            selectedProducts.insert(productId)
        }
    }

    private func selectAll() {
        selectedProducts = Set(detectedProducts.map { $0.id })
    }

    private func selectNone() {
        selectedProducts.removeAll()
    }

    private func addSelectedProducts() {
        // Eğer zaten ekleme işlemi devam ediyorsa, yeni işlem başlatma
        guard !isAddingProducts else { return }

        Task {
            await MainActor.run {
                isAddingProducts = true
            }

            let selectedProductsList = detectedProducts.filter { selectedProducts.contains($0.id) }

            for detectedProduct in selectedProductsList {
                await productViewModel.addDetectedProduct(detectedProduct, to: householdId)
            }

            // Tüm ürünler eklendikten sonra ürün listesini yenile
            await productViewModel.fetchProducts(for: householdId)

            await MainActor.run {
                isAddingProducts = false
                showingSuccess = true
            }
        }
    }
}

#Preview {
    ProductDetectionView(
        image: UIImage(systemName: "photo")!,
        householdId: UUID()
    )
    .environmentObject(ProductViewModel())
}
