import SwiftUI

struct ProductDetectionRow: View {
    let product: DetectedProduct
    let isSelected: Bool
    let onToggle: () -> Void
    
    var body: some View {
        Button(action: onToggle) {
            HStack(spacing: 16) {
                // Checkbox
                ZStack {
                    Circle()
                        .fill(isSelected ? Color.tazeBitPrimary : Color.clear)
                        .frame(width: 24, height: 24)
                        .overlay(
                            Circle()
                                .stroke(isSelected ? Color.tazeBitPrimary : Color.borderColor, lineWidth: 2)
                        )
                    
                    if isSelected {
                        Image(systemName: "checkmark")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                .animation(.easeInOut(duration: 0.2), value: isSelected)
                
                // Kategori ikonu
                Text(categoryIcon(for: product.category))
                    .font(.title2)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(categoryColor(for: product.category).opacity(0.1))
                    )
                
                // Ürün bilgileri
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name.capitalized)
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(.textPrimary)
                        .lineLimit(1)
                    
                    HStack(spacing: 8) {
                        Text("\(product.estimatedQuantity) \(product.unit)")
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)

                        Text("•")
                            .font(.caption)
                            .foregroundColor(.textSecondary)

                        Text(product.productCategory.displayName)
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)

                        if let calories = product.caloriesPer100g {
                            Text("•")
                                .font(.caption)
                                .foregroundColor(.textSecondary)

                            Text("\(calories) kcal/100g")
                                .font(.subheadline)
                                .foregroundColor(.orange)
                                .fontWeight(.medium)
                        }
                    }
                    
                    // Güven skoru
                    HStack(spacing: 4) {
                        Image(systemName: "brain.head.profile")
                            .font(.caption2)
                            .foregroundColor(confidenceColor(for: product.confidence))
                        
                        Text("Güven: %\(Int(product.confidence * 100))")
                            .font(.caption)
                            .foregroundColor(confidenceColor(for: product.confidence))
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        // Açıklama varsa göster
                        if let description = product.description, !description.isEmpty {
                            Text(description)
                                .font(.caption2)
                                .foregroundColor(.textSecondary)
                                .lineLimit(1)
                        }
                    }
                }
                
                Spacer()
                
                // Selection indicator
                VStack {
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title3)
                            .foregroundColor(.tazeBitPrimary)
                    } else {
                        Image(systemName: "circle")
                            .font(.title3)
                            .foregroundColor(.borderColor)
                    }
                    
                    Spacer()
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.tazeBitPrimary.opacity(0.05) : Color.cardBackground)
                    .stroke(
                        isSelected ? Color.tazeBitPrimary.opacity(0.3) : Color.borderColor,
                        lineWidth: isSelected ? 2 : 1
                    )
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func categoryIcon(for category: String) -> String {
        switch category.lowercased() {
        case "fruits":
            return "🍎"
        case "vegetables":
            return "🥕"
        case "dairy":
            return "🥛"
        case "meat":
            return "🥩"
        case "beverages":
            return "🥤"
        case "snacks":
            return "🍿"
        case "frozen":
            return "🧊"
        case "canned":
            return "🥫"
        case "bakery":
            return "🍞"
        default:
            return "📦"
        }
    }
    
    private func categoryColor(for category: String) -> Color {
        switch category.lowercased() {
        case "fruits":
            return .red
        case "vegetables":
            return .green
        case "dairy":
            return .blue
        case "meat":
            return .red
        case "beverages":
            return .cyan
        case "snacks":
            return .orange
        case "frozen":
            return .blue
        case "canned":
            return .yellow
        case "bakery":
            return .brown
        default:
            return .gray
        }
    }
    
    private func confidenceColor(for confidence: Double) -> Color {
        switch confidence {
        case 0.9...1.0:
            return .green
        case 0.8..<0.9:
            return .blue
        case 0.7..<0.8:
            return .orange
        default:
            return .red
        }
    }
}

#Preview {
    VStack(spacing: 12) {
        ProductDetectionRow(
            product: DetectedProduct(
                name: "elma",
                category: "fruits",
                estimatedQuantity: 5,
                unit: "adet",
                confidence: 0.95,
                description: "Kırmızı elma",
                storageLocation: "Buzdolabı",
                shelfLifeDays: 7,
                caloriesPer100g: 52
            ),
            isSelected: true,
            onToggle: {}
        )

        ProductDetectionRow(
            product: DetectedProduct(
                name: "süt",
                category: "dairy",
                estimatedQuantity: 1,
                unit: "litre",
                confidence: 0.87,
                description: nil,
                storageLocation: "Buzdolabı",
                shelfLifeDays: 5,
                caloriesPer100g: 64
            ),
            isSelected: false,
            onToggle: {}
        )
    }
    .padding()
    .background(Color.tazeBitBackground)
}
