import SwiftUI

struct PhotoDetectionFlow: View {
    let householdId: UUID

    @State private var selectedImage: UIImage?
    @State private var currentStep: DetectionStep = .photoSelection

    @Environment(\.dismiss) private var dismiss

    enum DetectionStep {
        case photoSelection
        case productDetection
    }

    var body: some View {
        NavigationView {
            ZStack {
                Color.tazeBitBackground
                    .ignoresSafeArea()

                switch currentStep {
                case .photoSelection:
                    PhotoPickerView(selectedImage: $selectedImage)
                        .navigationBarTitleDisplayMode(.inline)
                        .toolbar {
                            ToolbarItem(placement: .navigationBarLeading) {
                                Button("İptal") {
                                    dismiss()
                                }
                                .foregroundColor(.textPrimary)
                            }
                        }
                        .onChange(of: selectedImage) { _, image in
                            if image != nil {
                                currentStep = .productDetection
                            }
                        }

                case .productDetection:
                    if let image = selectedImage {
                        ProductDetectionView(
                            image: image,
                            householdId: householdId,
                            onDismiss: {
                                dismiss()
                            }
                        )
                        .navigationBarHidden(true)
                    }
                }
            }
        }
    }
}

#Preview {
    PhotoDetectionFlow(householdId: UUID())
        .environmentObject(ProductViewModel())
}
