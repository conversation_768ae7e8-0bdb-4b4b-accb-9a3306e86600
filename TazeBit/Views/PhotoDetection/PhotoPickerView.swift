import SwiftUI
import PhotosUI
import UIKit

struct PhotoPickerView: View {
    @Binding var selectedImage: UIImage?
    @State private var selectedItem: PhotosPickerItem?
    @State private var showingCamera = false
    @State private var showingImagePicker = false

    var body: some View {
        VStack(spacing: 20) {
            // Header
            VStack(spacing: 8) {
                Image(systemName: "camera.viewfinder")
                    .font(.system(size: 60))
                    .foregroundColor(.tazeBitPrimary)

                Text("AI ile Ürün Tespit Et")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)

                Text("Buzdolabı veya mutfak fotoğrafı çekerek ürünleri otomatik olarak tespit edin")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            .padding(.top, 40)

            Spacer()

            // Action Buttons
            VStack(spacing: 16) {
                // Kamera <PERSON>(action: {
                    showingCamera = true
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "camera.fill")
                            .font(.title3)

                        Text("Fotoğraf Çek")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        LinearGradient.tazeBitGradient
                            .cornerRadius(16)
                    )
                }
                .buttonStyle(PlainButtonStyle())

                // Galeri Butonu
                PhotosPicker(
                    selection: $selectedItem,
                    matching: .images,
                    photoLibrary: .shared()
                ) {
                    HStack(spacing: 12) {
                        Image(systemName: "photo.fill")
                            .font(.title3)

                        Text("Galeriden Seç")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.tazeBitPrimary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.tazeBitPrimary, lineWidth: 2)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 20)

            Spacer()

            // Tips Section
            VStack(alignment: .leading, spacing: 12) {
                Text("💡 İpuçları:")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                VStack(alignment: .leading, spacing: 8) {
                    TipRow(icon: "lightbulb.fill", text: "İyi aydınlatılmış fotoğraflar çekin")
                    TipRow(icon: "eye.fill", text: "Ürünlerin net görünmesini sağlayın")
                    TipRow(icon: "rectangle.fill", text: "Ürünleri merkeze alın")
                    TipRow(icon: "hand.raised.fill", text: "Elinizi fotoğrafın önünde tutmayın")
                }
            }
            .padding(20)
            .background(Color.cardBackground)
            .cornerRadius(16)
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .background(Color.tazeBitBackground)
        .onChange(of: selectedItem) { _, newItem in
            Task {
                if let data = try? await newItem?.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    selectedImage = image
                }
            }
        }
        .fullScreenCover(isPresented: $showingCamera) {
            CameraView(selectedImage: $selectedImage)
        }
    }
}

struct TipRow: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.tazeBitPrimary)
                .frame(width: 16)

            Text(text)
                .font(.subheadline)
                .foregroundColor(.textSecondary)

            Spacer()
        }
    }
}

// MARK: - Camera View
struct CameraView: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .camera
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraView

        init(_ parent: CameraView) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

#Preview {
    PhotoPickerView(selectedImage: .constant(nil))
}
