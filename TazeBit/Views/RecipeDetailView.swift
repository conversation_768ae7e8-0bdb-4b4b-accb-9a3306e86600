//
//  RecipeDetailView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 29.05.2025.
//

import SwiftUI

struct RecipeDetailView: View {
    let recipe: Recipe
    @ObservedObject var recipeViewModel: RecipeViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0

    // Shopping list creation
    let householdId: UUID?
    let onCreateShoppingList: ((Recipe) -> Void)?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // Header image
                    headerImageView

                    // Content
                    VStack(spacing: 20) {
                        // Title and basic info
                        titleSection

                        // Stats
                        statsSection

                        // Tab selector
                        tabSelector

                        // Tab content
                        if selectedTab == 0 {
                            ingredientsSection
                        } else {
                            instructionsSection
                        }

                        // Tips
                        if let tips = recipe.tips {
                            tipsSection(tips: tips)
                        }

                        // Actions
                        actionsSection
                    }
                    .padding()
                }
            }
            .navigationBarHidden(true)
        }
    }

    private var headerImageView: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color("TazeBitPrimary").opacity(0.3),
                    Color("TazeBitPrimary").opacity(0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .frame(height: 200)

            // Recipe icon
            VStack {
                Image(systemName: "fork.knife.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(Color("TazeBitPrimary").opacity(0.6))

                Text(recipe.cuisine.emoji)
                    .font(.largeTitle)
            }

            // Navigation buttons
            VStack {
                HStack {
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .background(
                                Circle()
                                    .fill(.black.opacity(0.3))
                                    .frame(width: 32, height: 32)
                            )
                    }

                    Spacer()

                    Button(action: {
                        recipeViewModel.toggleFavorite(recipe)
                    }) {
                        Image(systemName: recipeViewModel.isFavorite(recipe) ? "heart.fill" : "heart")
                            .font(.title2)
                            .foregroundColor(recipeViewModel.isFavorite(recipe) ? .red : .white)
                            .background(
                                Circle()
                                    .fill(.black.opacity(0.3))
                                    .frame(width: 32, height: 32)
                            )
                    }
                }

                Spacer()

                // Difficulty badge
                HStack {
                    VStack {
                        Text(recipe.difficulty.emoji)
                            .font(.title3)
                        Text(recipe.difficulty.displayName)
                            .font(.caption)
                            .fontWeight(.semibold)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(.black.opacity(0.6))
                    .foregroundColor(.white)
                    .cornerRadius(12)

                    Spacer()
                }
            }
            .padding()
        }
    }

    private var titleSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(recipe.name)
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            HStack {
                Text(recipe.cuisine.displayName)
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text("Oluşturulma: \(recipe.createdAt, formatter: dateFormatter)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }

    private var statsSection: some View {
        HStack(spacing: 20) {
            StatItem(
                icon: "clock.fill",
                title: "Süre",
                value: recipe.totalTimeFormatted,
                color: Color("TazeBitPrimary")
            )

            StatItem(
                icon: "person.2.fill",
                title: "Porsiyon",
                value: "\(recipe.servings) kişi",
                color: .blue
            )

            StatItem(
                icon: "list.bullet",
                title: "Malzeme",
                value: "\(recipe.ingredients.count) adet",
                color: .orange
            )
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }

    private var tabSelector: some View {
        HStack(spacing: 0) {
            Button(action: { selectedTab = 0 }) {
                VStack(spacing: 4) {
                    Text("Malzemeler")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Rectangle()
                        .fill(selectedTab == 0 ? Color("TazeBitPrimary") : Color.clear)
                        .frame(height: 2)
                }
                .foregroundColor(selectedTab == 0 ? Color("TazeBitPrimary") : .secondary)
                .frame(maxWidth: .infinity)
            }

            Button(action: { selectedTab = 1 }) {
                VStack(spacing: 4) {
                    Text("Tarif")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Rectangle()
                        .fill(selectedTab == 1 ? Color("TazeBitPrimary") : Color.clear)
                        .frame(height: 2)
                }
                .foregroundColor(selectedTab == 1 ? Color("TazeBitPrimary") : .secondary)
                .frame(maxWidth: .infinity)
            }
        }
    }

    private var ingredientsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            ForEach(recipe.ingredients, id: \.id) { ingredient in
                HStack {
                    Image(systemName: ingredient.isAvailable ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(ingredient.isAvailable ? .green : .secondary)

                    VStack(alignment: .leading, spacing: 2) {
                        Text(ingredient.displayText)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .strikethrough(ingredient.isAvailable)

                        if ingredient.isOptional {
                            Text("(İsteğe bağlı)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                    if !ingredient.isAvailable {
                        Button("Ekle") {
                            // Add to shopping list
                        }
                        .font(.caption)
                        .foregroundColor(Color("TazeBitPrimary"))
                    }
                }
                .padding()
                .background(ingredient.isAvailable ? Color.green.opacity(0.1) : Color(.secondarySystemBackground))
                .cornerRadius(8)
            }
        }
    }

    private var instructionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            ForEach(Array(recipe.instructions.enumerated()), id: \.offset) { index, instruction in
                HStack(alignment: .top, spacing: 12) {
                    Text("\(index + 1)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(width: 28, height: 28)
                        .background(Color("TazeBitPrimary"))
                        .clipShape(Circle())

                    Text(instruction)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .fixedSize(horizontal: false, vertical: true)

                    Spacer()
                }
                .padding()
                .background(Color(.secondarySystemBackground))
                .cornerRadius(12)
            }
        }
    }

    private func tipsSection(tips: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)

                Text("İpucu")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            Text(tips)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.yellow.opacity(0.1))
        .cornerRadius(12)
    }

    private var actionsSection: some View {
        VStack(spacing: 12) {
            // Missing ingredients section (if any)
            if !recipe.missingIngredients.isEmpty {
                missingIngredientsDetailSection

                // Shopping list creation button
                Button("Alışveriş Listesi Oluştur") {
                    onCreateShoppingList?(recipe)
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color.orange)
                .cornerRadius(12)
            }
        }
    }

    private var missingIngredientsDetailSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "cart.badge.plus")
                    .foregroundColor(.orange)

                Text("Eksik Malzemeler")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("\(recipe.missingIngredientsCount) adet")
                    .font(.subheadline)
                    .foregroundColor(.orange)
                    .fontWeight(.semibold)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(recipe.missingIngredients, id: \.id) { ingredient in
                    HStack {
                        Text(ingredient.name)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .lineLimit(1)

                        Spacer()

                        Text(ingredient.displayText.replacingOccurrences(of: ingredient.name, with: ""))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }

    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "tr_TR")
        return formatter
    }
}

struct StatItem: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)

            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    RecipeDetailView(
        recipe: Recipe(
            name: "Domates Soslu Makarna",
            difficulty: .easy,
            preparationTime: 15,
            cookingTime: 20,
            servings: 4,
            ingredients: [
                RecipeIngredient(name: "Makarna", amount: "500", unit: "gr", isAvailable: true),
                RecipeIngredient(name: "Domates", amount: "3", unit: "adet", isAvailable: true),
                RecipeIngredient(name: "Soğan", amount: "1", unit: "adet", isAvailable: false)
            ],
            instructions: [
                "Makarnayı kaynar tuzlu suda haşlayın",
                "Soğan ve sarımsağı kavurun",
                "Domatesleri ekleyip pişirin"
            ],
            tips: "Taze fesleğen ekleyerek lezzetini artırabilirsiniz",
            cuisine: .turkish,
            tags: ["Kolay", "Hızlı"]
        ),
        recipeViewModel: RecipeViewModel(),
        householdId: UUID(),
        onCreateShoppingList: { _ in }
    )
}
