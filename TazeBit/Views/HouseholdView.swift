//
//  HouseholdView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct HouseholdSelectionView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss
    let onDismiss: () -> Void

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 0) {
                        // Header Section
                        selectionHeaderSection

                        // Content Section
                        VStack(spacing: 20) {
                            if householdViewModel.isLoading {
                                loadingView
                            } else if householdViewModel.households.isEmpty {
                                emptyStateView
                            } else {
                                selectionHouseholdGrid
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
                .refreshable {
                    await householdViewModel.fetchHouseholds()
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
            .sheet(isPresented: $householdViewModel.showingCreateHousehold) {
                CreateHouseholdView()
                    .environmentObject(householdViewModel)
            }
        }
    }

    private var selectionHeaderSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    Spacer()

                    // Center Title
                    VStack(spacing: 4) {
                        Text("Ev Seç 🏠")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("\(householdViewModel.households.count) ev")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                    }

                    Spacer()

                    // Close Button
                    Button(action: {
                        onDismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    private var selectionHouseholdGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 16),
            GridItem(.flexible(), spacing: 16)
        ], spacing: 16) {
            ForEach(householdViewModel.households) { household in
                CompactHouseholdCard(
                    household: household,
                    isSelected: household.id == householdViewModel.selectedHousehold?.id,
                    onTap: {
                        householdViewModel.selectHousehold(household)
                        // Auto dismiss after selection
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            onDismiss()
                        }
                    }
                )
            }
        }
        .padding(.horizontal, 4) // Extra padding to prevent edge clipping
    }

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.tazeBitPrimary)

            Text("Evler yükleniyor...")
                .font(.subheadline)
                .foregroundColor(.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }

    private var emptyStateView: some View {
        VStack(spacing: 24) {
            // Icon
            ZStack {
                Circle()
                    .fill(Color.tazeBitPrimary.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: "house.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.tazeBitPrimary)
            }

            // Text Content
            VStack(spacing: 8) {
                Text("Henüz ev yok")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text("İlk evinizi oluşturarak TazeBit'e başlayın")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }

            // Action Button
            Button(action: {
                householdViewModel.showCreateHousehold()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                        .font(.headline)

                    Text("İlk Evi Oluştur")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    LinearGradient.tazeBitGradient
                        .cornerRadius(25)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }
}

struct HouseholdView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 0) {
                        // Header Section
                        headerSection

                        // Content Section
                        VStack(spacing: 20) {
                            if householdViewModel.isLoading {
                                loadingView
                            } else if householdViewModel.households.isEmpty {
                                emptyStateView
                            } else {
                                householdGrid
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
                .refreshable {
                    await householdViewModel.fetchHouseholds()
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
            .sheet(isPresented: $householdViewModel.showingCreateHousehold) {
                CreateHouseholdView()
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $householdViewModel.showingJoinHousehold) {
                JoinHouseholdView()
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $householdViewModel.showingInviteCode) {
                InviteCodeView()
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $householdViewModel.showingLeaveConfirmation) {
                LeaveHouseholdView()
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $householdViewModel.showingHouseholdMembers) {
                HouseholdMembersView()
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $householdViewModel.showingEditHousehold) {
                EditHouseholdView()
                    .environmentObject(householdViewModel)
            }
        }
    }

    private var headerSection: some View {
        ZStack {
            // Gradient Background
            LinearGradient.tazeBitGradient
                .frame(height: 200)
                .clipShape(
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 30,
                        bottomTrailingRadius: 30,
                        topTrailingRadius: 0
                    )
                )
                .ignoresSafeArea(.all)

            VStack(spacing: 0) {
                // Top Section
                HStack {
                    // Left Action Button
                    Button(action: {
                        householdViewModel.showCreateHousehold()
                    }) {
                        ZStack {
                            // Outer transparent circle
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .frame(width: 44, height: 44)

                            // Inner solid circle
                            Circle()
                                .fill(Color.white)
                                .frame(width: 32, height: 32)

                            // Icon
                            Image(systemName: "plus")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.tazeBitPrimary)
                        }
                    }

                    Spacer()

                    // Center Title
                    VStack(spacing: 4) {
                        Text("Evlerim 🏠")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("\(householdViewModel.households.count) ev")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                    }

                    Spacer()

                    // Right Action Button
                    Button(action: {
                        householdViewModel.showJoinHousehold()
                    }) {
                        ZStack {
                            // Outer transparent circle
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .frame(width: 44, height: 44)

                            // Inner solid circle
                            Circle()
                                .fill(Color.white)
                                .frame(width: 32, height: 32)

                            // Icon
                            Image(systemName: "person.badge.plus")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.tazeBitPrimary)
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)
                .padding(.bottom, 30)
            }
        }
        .ignoresSafeArea(.all)
    }

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.tazeBitPrimary)

            Text("Evler yükleniyor...")
                .font(.subheadline)
                .foregroundColor(.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }

    private var householdGrid: some View {
        LazyVStack(spacing: 16) {
            ForEach(householdViewModel.households) { household in
                ModernHouseholdCard(
                    household: household,
                    isSelected: household.id == householdViewModel.selectedHousehold?.id,
                    onTap: {
                        householdViewModel.selectHousehold(household)
                    },
                    householdViewModel: householdViewModel
                )
            }
        }
    }

    private var emptyStateView: some View {
        VStack(spacing: 24) {
            // Icon
            ZStack {
                Circle()
                    .fill(Color.tazeBitPrimary.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: "house.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.tazeBitPrimary)
            }

            // Text Content
            VStack(spacing: 8) {
                Text("Henüz ev yok")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text("İlk evinizi oluşturarak TazeBit'e başlayın")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }

            // Action Buttons
            VStack(spacing: 12) {
                Button(action: {
                    householdViewModel.showCreateHousehold()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "plus.circle.fill")
                            .font(.headline)

                        Text("İlk Evi Oluştur")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient.tazeBitGradient
                            .cornerRadius(25)
                    )
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    householdViewModel.showJoinHousehold()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "person.badge.plus")
                            .font(.headline)

                        Text("Mevcut Eve Katıl")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.tazeBitPrimary)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(Color.tazeBitPrimary, lineWidth: 2)
                            .fill(Color.clear)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }
}

// MARK: - Supporting Views

struct CompactHouseholdCard: View {
    let household: Household
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Header with Icon and Selection
                HStack {
                    // House Icon
                    ZStack {
                        Circle()
                            .fill(isSelected ? Color.tazeBitPrimary : Color.tazeBitSecondary.opacity(0.3))
                            .frame(width: 40, height: 40)

                        Image(systemName: "house.fill")
                            .font(.title3)
                            .foregroundColor(isSelected ? .white : .tazeBitSecondary)
                    }

                    Spacer()

                    // Selection Indicator
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.tazeBitPrimary)
                    }
                }

                // Household Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(household.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)
                        .lineLimit(1)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Text(household.createdAt, style: .date)
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                Spacer()
            }
            .padding(16)
            .frame(height: 120)
            .background(Color.cardBackground)
            .cornerRadius(12)
            .shadow(color: Color.cardShadow, radius: isSelected ? 6 : 2, x: 0, y: 1)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.tazeBitPrimary : Color.clear, lineWidth: 2)
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ModernHouseholdCard: View {
    let household: Household
    let isSelected: Bool
    let onTap: () -> Void
    @ObservedObject var householdViewModel: HouseholdViewModel
    @State private var showingDetail = false

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Header with Icon and Selection
                HStack {
                    // House Icon
                    ZStack {
                        Circle()
                            .fill(isSelected ? Color.tazeBitPrimary : Color.tazeBitSecondary.opacity(0.3))
                            .frame(width: 50, height: 50)

                        Image(systemName: "house.fill")
                            .font(.title2)
                            .foregroundColor(isSelected ? .white : .tazeBitSecondary)
                    }

                    Spacer()

                    // Action Buttons
                    HStack(spacing: 12) {
                        // Detail Button
                        Button(action: {
                            showingDetail = true
                        }) {
                            Image(systemName: "info.circle.fill")
                                .font(.title2)
                                .foregroundColor(.tazeBitSecondary)
                                .frame(width: 44, height: 44)
                                .background(
                                    Circle()
                                        .fill(Color.tazeBitSecondary.opacity(0.1))
                                )
                        }
                        .buttonStyle(PlainButtonStyle())

                        // More Actions Menu
                        Menu {
                            Button(action: {
                                // Set the current household as selected before showing edit
                                householdViewModel.selectedHousehold = household
                                householdViewModel.showEditHousehold()
                            }) {
                                Label("Ev Düzenle", systemImage: "pencil")
                            }

                            Button(action: {
                                // Set the current household as selected before showing members
                                householdViewModel.selectedHousehold = household
                                householdViewModel.showHouseholdMembers()
                            }) {
                                Label("Üyeleri Görüntüle", systemImage: "person.2")
                            }

                            Button(action: {
                                // Set the current household as selected before showing invite code
                                householdViewModel.selectedHousehold = household
                                householdViewModel.showInviteCode()
                            }) {
                                Label("Davet Kodu Paylaş", systemImage: "square.and.arrow.up")
                            }

                            Divider()

                            Button(role: .destructive, action: {
                                // Set the current household as selected before showing leave confirmation
                                householdViewModel.selectedHousehold = household
                                householdViewModel.showLeaveConfirmation()
                            }) {
                                Label("Evden Ayrıl", systemImage: "door.right.hand.open")
                            }
                        } label: {
                            Image(systemName: "ellipsis.circle.fill")
                                .font(.title2)
                                .foregroundColor(.tazeBitSecondary)
                                .frame(width: 44, height: 44)
                                .background(
                                    Circle()
                                        .fill(Color.tazeBitSecondary.opacity(0.1))
                                )
                        }
                        .buttonStyle(PlainButtonStyle())

                        // Selection Indicator
                        if isSelected {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(.tazeBitPrimary)
                        }
                    }
                }

                // Household Info
                VStack(alignment: .leading, spacing: 6) {
                    Text(household.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)
                        .lineLimit(1)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    if let description = household.description, !description.isEmpty {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                            .lineLimit(2)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }

                    Text(household.createdAt, style: .date)
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                Spacer()
            }
            .padding(20)
            .frame(height: 160)
            .background(Color.cardBackground)
            .cornerRadius(16)
            .shadow(color: Color.cardShadow, radius: isSelected ? 8 : 4, x: 0, y: 2)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? Color.tazeBitPrimary : Color.clear, lineWidth: 2)
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingDetail) {
            HouseholdDetailView(household: household)
                .environmentObject(householdViewModel)
                .environmentObject(ProductViewModel())
        }
    }
}

struct CreateHouseholdView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 24) {
                            // Form Content
                            VStack(spacing: 20) {
                                // Form Fields
                                VStack(spacing: 16) {
                                    // House Name Field
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("Ev Adı")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                            .foregroundColor(.textSecondary)

                                        TextField("Örn: Aile Evi, Yazlık...", text: $householdViewModel.newHouseholdName)
                                            .textFieldStyle(CustomTextFieldStyle())
                                    }

                                    // Description Field
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("Açıklama (İsteğe Bağlı)")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                            .foregroundColor(.textSecondary)

                                        ZStack(alignment: .topLeading) {
                                            RoundedRectangle(cornerRadius: 12)
                                                .fill(Color.cardBackground)
                                                .stroke(Color.borderColor, lineWidth: 1)
                                                .frame(height: 100)

                                            TextEditor(text: $householdViewModel.newHouseholdDescription)
                                                .padding(16)
                                                .background(Color.clear)
                                                .font(.body)
                                                .foregroundColor(.textPrimary)
                                                .scrollContentBackground(.hidden)

                                            if householdViewModel.newHouseholdDescription.isEmpty {
                                                Text("Bu ev hakkında kısa bir açıklama...")
                                                    .font(.body)
                                                    .foregroundColor(.textSecondary)
                                                    .padding(.horizontal, 20)
                                                    .padding(.vertical, 24)
                                                    .allowsHitTesting(false)
                                            }
                                        }
                                    }
                                }

                                // Error Message
                                if !householdViewModel.errorMessage.isEmpty {
                                    HStack(spacing: 8) {
                                        Image(systemName: "exclamationmark.triangle.fill")
                                            .foregroundColor(.red)

                                        Text(householdViewModel.errorMessage)
                                            .font(.caption)
                                            .foregroundColor(.red)
                                    }
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .background(Color.red.opacity(0.1))
                                    .cornerRadius(8)
                                }

                                // Error Message
                                if !householdViewModel.errorMessage.isEmpty {
                                    HStack(spacing: 8) {
                                        Image(systemName: "exclamationmark.triangle.fill")
                                            .foregroundColor(.red)

                                        Text(householdViewModel.errorMessage)
                                            .font(.caption)
                                            .foregroundColor(.red)
                                    }
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .background(Color.red.opacity(0.1))
                                    .cornerRadius(8)
                                }

                                // Action Buttons
                                VStack(spacing: 12) {
                                    // Create Button
                                    Button(action: {
                                        Task {
                                            await householdViewModel.createHousehold()
                                            if householdViewModel.errorMessage.isEmpty {
                                                dismiss()
                                            }
                                        }
                                    }) {
                                        HStack(spacing: 8) {
                                            if householdViewModel.isLoading {
                                                ProgressView()
                                                    .scaleEffect(0.8)
                                                    .tint(.white)
                                            } else {
                                                Image(systemName: "plus.circle.fill")
                                                    .font(.headline)
                                            }

                                            Text(householdViewModel.isLoading ? "Oluşturuluyor..." : "Evi Oluştur")
                                                .font(.headline)
                                                .fontWeight(.semibold)
                                        }
                                        .foregroundColor(.white)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 16)
                                        .background(
                                            LinearGradient.tazeBitGradient
                                                .cornerRadius(12)
                                        )
                                    }
                                    .disabled(householdViewModel.isLoading || householdViewModel.newHouseholdName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                                    .buttonStyle(PlainButtonStyle())

                                    // Cancel Button
                                    Button("İptal") {
                                        dismiss()
                                    }
                                    .font(.headline)
                                    .foregroundColor(.textSecondary)
                                    .padding(.vertical, 8)
                                }
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    Text("Yeni Ev Oluştur")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    // Save Button
                    Button(action: {
                        Task {
                            await householdViewModel.createHousehold()
                            if householdViewModel.errorMessage.isEmpty {
                                dismiss()
                            }
                        }
                    }) {
                        if householdViewModel.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                                .frame(width: 32, height: 32)
                        } else {
                            Image(systemName: "checkmark")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(Color.white.opacity(0.2))
                                )
                        }
                    }
                    .disabled(householdViewModel.isLoading || householdViewModel.newHouseholdName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }
}

#Preview {
    HouseholdView()
        .environmentObject(HouseholdViewModel())
}
