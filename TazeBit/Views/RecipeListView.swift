//
//  RecipeListView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 29.05.2025.
//

import SwiftUI

struct RecipeListView: View {
    @ObservedObject var recipeViewModel: RecipeViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    @State private var showingRecipeDetail = false
    @State private var selectedRecipe: Recipe?
    @State private var showingSuccessMessage = false
    @State private var successMessage = ""

    // Household ID for shopping list creation
    let householdId: UUID?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection

                // Tab selector
                tabSelector

                // Content
                TabView(selection: $selectedTab) {
                    // AI Suggestions
                    suggestedRecipesView
                        .tag(0)

                    // Favorites
                    favoriteRecipesView
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingRecipeDetail) {
            if let recipe = selectedRecipe {
                RecipeDetailView(
                    recipe: recipe,
                    recipeViewModel: recipeViewModel,
                    householdId: householdId,
                    onCreateShoppingList: { recipe in
                        createShoppingListForRecipe(recipe)
                    }
                )
            }
        }
        .alert("Başarılı!", isPresented: $showingSuccessMessage) {
            Button("Tamam") { }
        } message: {
            Text(successMessage)
        }
    }

    private var headerSection: some View {
        HStack {
            Button("Kapat") {
                dismiss()
            }
            .foregroundColor(Color("TazeBitPrimary"))

            Spacer()

            Text("Yemek Tarifleri")
                .font(.headline)
                .fontWeight(.bold)

            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
    }

    private var tabSelector: some View {
        HStack(spacing: 0) {
            Button(action: { selectedTab = 0 }) {
                VStack(spacing: 4) {
                    HStack {
                        Image(systemName: "sparkles")
                            .font(.caption)
                        Text("AI Önerileri")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Rectangle()
                        .fill(selectedTab == 0 ? Color("TazeBitPrimary") : Color.clear)
                        .frame(height: 2)
                }
                .foregroundColor(selectedTab == 0 ? Color("TazeBitPrimary") : .secondary)
                .frame(maxWidth: .infinity)
            }

            Button(action: { selectedTab = 1 }) {
                VStack(spacing: 4) {
                    HStack {
                        Image(systemName: "heart.fill")
                            .font(.caption)
                        Text("Favoriler")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Rectangle()
                        .fill(selectedTab == 1 ? Color("TazeBitPrimary") : Color.clear)
                        .frame(height: 2)
                }
                .foregroundColor(selectedTab == 1 ? Color("TazeBitPrimary") : .secondary)
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    private var suggestedRecipesView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if recipeViewModel.isLoading {
                    loadingView
                } else if recipeViewModel.suggestedRecipes.isEmpty {
                    emptyStateView
                } else {
                    // Generation info
                    if let lastGenerated = recipeViewModel.lastGeneratedAt {
                        generationInfoView(date: lastGenerated)
                    }

                    // Recipes
                    ForEach(recipeViewModel.suggestedRecipes, id: \.id) { recipe in
                        RecipeCard(
                            recipe: recipe,
                            isFavorite: recipeViewModel.isFavorite(recipe),
                            onFavoriteToggle: {
                                recipeViewModel.toggleFavorite(recipe)
                            },
                            onTap: {
                                selectedRecipe = recipe
                                showingRecipeDetail = true
                            },
                            onAddMissingIngredients: {
                                createShoppingListForRecipe(recipe)
                            }
                        )
                    }

                    // Suggestions
                    if !recipeViewModel.suggestions.isEmpty {
                        suggestionsView
                    }
                }
            }
            .padding()
        }
    }

    private var favoriteRecipesView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if recipeViewModel.favoriteRecipes.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "heart")
                            .font(.system(size: 50))
                            .foregroundColor(.secondary)

                        Text("Henüz favori tarif yok")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Text("Beğendiğiniz tarifleri favorilere ekleyerek buradan kolayca erişebilirsiniz")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    ForEach(recipeViewModel.favoriteRecipes, id: \.id) { recipe in
                        RecipeCard(
                            recipe: recipe,
                            isFavorite: true,
                            onFavoriteToggle: {
                                recipeViewModel.toggleFavorite(recipe)
                            },
                            onTap: {
                                selectedRecipe = recipe
                                showingRecipeDetail = true
                            },
                            onAddMissingIngredients: {
                                createShoppingListForRecipe(recipe)
                            }
                        )
                    }
                }
            }
            .padding()
        }
    }

    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)

            Text("AI yemek tarifleri hazırlıyor...")
                .font(.headline)
                .foregroundColor(.secondary)

            Text("Bu işlem birkaç saniye sürebilir")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }

    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "chef.hat")
                .font(.system(size: 50))
                .foregroundColor(.secondary)

            Text("Tarif önerisi almak için")
                .font(.headline)
                .foregroundColor(.secondary)

            Text("Ana sayfadan 'Tarif Önerisi Al' butonuna tıklayın")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private func generationInfoView(date: Date) -> some View {
        HStack {
            Image(systemName: "clock")
                .foregroundColor(.secondary)
                .font(.caption)

            Text("Son güncelleme: \(date, formatter: dateFormatter)")
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            Button("Yenile") {
                // Refresh action will be implemented
            }
            .font(.caption)
            .foregroundColor(Color("TazeBitPrimary"))
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(8)
    }

    private var missingIngredientsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "cart.badge.plus")
                    .foregroundColor(Color("TazeBitPrimary"))

                Text("Eksik Malzemeler")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(recipeViewModel.missingIngredients, id: \.self) { ingredient in
                    Text(ingredient)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color("TazeBitPrimary").opacity(0.1))
                        .foregroundColor(Color("TazeBitPrimary"))
                        .cornerRadius(6)
                }
            }

            Button("Alışveriş Listesine Ekle") {
                // Add to shopping list action
            }
            .font(.subheadline)
            .foregroundColor(Color("TazeBitPrimary"))
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }

    private var suggestionsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "lightbulb")
                    .foregroundColor(.orange)

                Text("Öneriler")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            ForEach(recipeViewModel.suggestions, id: \.self) { suggestion in
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)

                    Text(suggestion)
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }





    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter
    }

    // MARK: - Shopping List Creation

    private func createShoppingListForRecipe(_ recipe: Recipe) {
        guard let householdId = householdId else {
            successMessage = "Ev seçilmemiş. Lütfen önce bir ev seçin."
            showingSuccessMessage = true
            return
        }

        guard !recipe.missingIngredients.isEmpty else {
            successMessage = "Bu tarif için eksik malzeme bulunmuyor."
            showingSuccessMessage = true
            return
        }

        Task {
            let success = await recipeViewModel.createShoppingListForRecipe(recipe, householdId: householdId)

            await MainActor.run {
                if success {
                    successMessage = "'\(recipe.name)' için alışveriş listesi oluşturuldu! \(recipe.missingIngredientsCount) malzeme eklendi."
                } else {
                    successMessage = "Alışveriş listesi oluşturulamadı. Lütfen tekrar deneyin."
                }
                showingSuccessMessage = true
            }
        }
    }
}

#Preview {
    RecipeListView(recipeViewModel: RecipeViewModel(), householdId: UUID())
}
