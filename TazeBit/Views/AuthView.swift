//
//  AuthView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct AuthView: View {
    @StateObject private var viewModel = AuthViewModel()
    @State private var showWelcomeAnimation = true

    var body: some View {
        ZStack {
            // Animated Gradient Background
            AnimatedGradientBackground(
                colors: Color.gradientColors
            )

            ScrollView {
                VStack(spacing: 0) {
                    // Welcome Section
                    welcomeSection
                        .padding(.top, 60)
                        .opacity(showWelcomeAnimation ? 1 : 0)
                        .offset(y: showWelcomeAnimation ? 0 : -50)
                        .animation(.easeOut(duration: 1).delay(0.2), value: showWelcomeAnimation)

                    // Auth Form
                    authFormSection
                        .padding(.top, 40)
                        .opacity(showWelcomeAnimation ? 1 : 0)
                        .offset(y: showWelcomeAnimation ? 0 : 50)
                        .animation(.easeOut(duration: 1).delay(0.5), value: showWelcomeAnimation)
                }
                .padding(.horizontal, 24)
                .padding(.bottom, 40)
            }
        }
        .onAppear {
            showWelcomeAnimation = true
        }
    }

    private var welcomeSection: some View {
        VStack(spacing: 16) {
            // App Icon with Animation
            ZStack {
                Circle()
                    .fill(Color.white.opacity(0.2))
                    .frame(width: 120, height: 120)

                Image(systemName: "leaf.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
            }
            .scaleEffect(showWelcomeAnimation ? 1 : 0.5)
            .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.1), value: showWelcomeAnimation)

            // App Title
            VStack(spacing: 8) {
                Text("TazeBit")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)

                Text("Yiyeceklerinizi taze tutun")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
            }
        }
    }

    private var authFormSection: some View {
        VStack(spacing: 24) {
            // Mode Title
            Text(viewModel.isSignUpMode ? "Hesap Oluştur" : "Hoş Geldiniz")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .animation(.easeInOut(duration: 0.3), value: viewModel.isSignUpMode)

            // Form Fields
            VStack(spacing: 16) {
                if viewModel.isSignUpMode {
                    CustomTextField(
                        title: "Ad Soyad",
                        text: $viewModel.fullName,
                        icon: "person",
                        autocapitalization: .words
                    )
                    .transition(.asymmetric(
                        insertion: .move(edge: .top).combined(with: .opacity),
                        removal: .move(edge: .top).combined(with: .opacity)
                    ))
                }

                CustomTextField(
                    title: "Email",
                    text: $viewModel.email,
                    icon: "envelope",
                    keyboardType: .emailAddress,
                    autocapitalization: .none
                )

                CustomTextField(
                    title: "Şifre",
                    text: $viewModel.password,
                    icon: "lock",
                    isSecure: true
                )
            }
            .animation(.easeInOut(duration: 0.3), value: viewModel.isSignUpMode)

            // Error Message
            if !viewModel.errorMessage.isEmpty {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    Text(viewModel.errorMessage)
                        .font(.caption)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.red.opacity(0.2))
                .cornerRadius(8)
                .transition(.scale.combined(with: .opacity))
            }

            // Action Buttons
            VStack(spacing: 12) {
                CustomButton(
                    title: viewModel.isSignUpMode ? "Hesap Oluştur" : "Giriş Yap",
                    action: {
                        Task {
                            if viewModel.isSignUpMode {
                                await viewModel.signUp()
                            } else {
                                await viewModel.signIn()
                            }
                        }
                    },
                    isLoading: viewModel.isLoading,
                    style: .primary
                )

                CustomButton(
                    title: viewModel.isSignUpMode ? "Zaten hesabın var mı?" : "Hesabın yok mu?",
                    action: viewModel.toggleMode,
                    style: .outline
                )
            }
            .padding(.top, 8)
        }
        .padding(.horizontal, 8)
    }
}

#Preview {
    AuthView()
}
