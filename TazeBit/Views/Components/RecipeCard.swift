//
//  RecipeCard.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 29.05.2025.
//

import SwiftUI

struct RecipeCard: View {
    let recipe: Recipe
    let isFavorite: Bool
    let onFavoriteToggle: () -> Void
    let onTap: () -> Void
    let onAddMissingIngredients: (() -> Void)?

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 0) {
                // Header with image placeholder and favorite button
                ZStack {
                    // Recipe image placeholder
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color("TazeBitPrimary").opacity(0.3),
                                    Color("TazeBitPrimary").opacity(0.1)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(height: 120)
                        .overlay(
                            VStack(spacing: 8) {
                                // Recipe category icon based on name/type
                                Image(systemName: recipeIconName)
                                    .font(.system(size: 32, weight: .medium))
                                    .foregroundColor(Color("TazeBitPrimary").opacity(0.7))

                                // Recipe name preview
                                Text(recipe.name.prefix(15))
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color("TazeBitPrimary").opacity(0.8))
                                    .lineLimit(1)
                            }
                        )

                    // Favorite button
                    VStack {
                        HStack {
                            Spacer()

                            Button(action: onFavoriteToggle) {
                                Image(systemName: isFavorite ? "heart.fill" : "heart")
                                    .font(.title3)
                                    .foregroundColor(isFavorite ? .red : .white)
                                    .background(
                                        Circle()
                                            .fill(.black.opacity(0.3))
                                            .frame(width: 32, height: 32)
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }

                        Spacer()
                    }
                    .padding(12)

                    // Difficulty badge
                    VStack {
                        HStack {
                            VStack {
                                Text(recipe.difficulty.emoji)
                                    .font(.caption2)
                                Text(recipe.difficulty.displayName)
                                    .font(.caption2)
                                    .fontWeight(.medium)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(.black.opacity(0.6))
                            .foregroundColor(.white)
                            .cornerRadius(8)

                            Spacer()
                        }

                        Spacer()
                    }
                    .padding(12)
                }

                // Recipe info
                VStack(alignment: .leading, spacing: 12) {
                    // Title and cuisine
                    VStack(alignment: .leading, spacing: 4) {
                        Text(recipe.name)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        HStack {
                            Text(recipe.cuisine.displayName)
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Spacer()

                            Text(recipe.totalTimeFormatted)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    // Stats
                    HStack(spacing: 16) {
                        // Time
                        HStack(spacing: 4) {
                            Image(systemName: "clock")
                                .font(.caption)
                                .foregroundColor(Color("TazeBitPrimary"))

                            Text("\(recipe.totalTime)dk")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        }

                        // Servings
                        HStack(spacing: 4) {
                            Image(systemName: "person.2")
                                .font(.caption)
                                .foregroundColor(Color("TazeBitPrimary"))

                            Text("\(recipe.servings) kişi")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        }

                        // Ingredients count
                        HStack(spacing: 4) {
                            Image(systemName: "list.bullet")
                                .font(.caption)
                                .foregroundColor(Color("TazeBitPrimary"))

                            Text("\(recipe.ingredients.count) malzeme")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        }

                        Spacer()
                    }

                    // Available ingredients indicator
                    if !recipe.ingredients.isEmpty {
                        availableIngredientsIndicator
                    }

                    // Missing ingredients section
                    if !recipe.missingIngredients.isEmpty {
                        missingIngredientsSection
                    }

                    // Tags
                    if !recipe.tags.isEmpty {
                        tagsView
                    }
                }
                .padding()
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }

    // MARK: - Computed Properties

    private var recipeIconName: String {
        let recipeName = recipe.name.lowercased()

        // Meat dishes
        if recipeName.contains("tavuk") || recipeName.contains("chicken") {
            return "bird.fill"
        } else if recipeName.contains("et") || recipeName.contains("beef") || recipeName.contains("dana") {
            return "leaf.fill"
        } else if recipeName.contains("balık") || recipeName.contains("fish") || recipeName.contains("salmon") {
            return "fish.fill"
        }

        // Pasta and rice dishes
        else if recipeName.contains("makarna") || recipeName.contains("pasta") || recipeName.contains("spagetti") {
            return "fork.knife.circle.fill"
        } else if recipeName.contains("pilav") || recipeName.contains("rice") || recipeName.contains("pirinç") {
            return "circle.grid.3x3.fill"
        }

        // Soups
        else if recipeName.contains("çorba") || recipeName.contains("soup") {
            return "drop.fill"
        }

        // Desserts
        else if recipeName.contains("tatlı") || recipeName.contains("dessert") || recipeName.contains("pasta") && recipeName.contains("tatlı") {
            return "birthday.cake.fill"
        } else if recipeName.contains("kurabiye") || recipeName.contains("cookie") {
            return "circle.fill"
        }

        // Vegetables
        else if recipeName.contains("salata") || recipeName.contains("salad") {
            return "leaf.circle.fill"
        } else if recipeName.contains("sebze") || recipeName.contains("vegetable") {
            return "carrot.fill"
        }

        // Breakfast
        else if recipeName.contains("kahvaltı") || recipeName.contains("breakfast") || recipeName.contains("omlet") {
            return "sun.max.fill"
        }

        // Default
        else {
            return "fork.knife"
        }
    }

    private var availableIngredientsIndicator: some View {
        let availableCount = recipe.availableIngredientsCount
        let totalCount = recipe.ingredients.count
        let percentage = recipe.ingredientAvailabilityPercentage

        return VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text("Mevcut Malzemeler")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(availableCount)/\(totalCount)")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(percentage >= 0.7 ? .green : percentage >= 0.4 ? .orange : .red)
            }

            // Progress bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 4)
                        .cornerRadius(2)

                    Rectangle()
                        .fill(percentage >= 0.7 ? .green : percentage >= 0.4 ? .orange : .red)
                        .frame(width: geometry.size.width * percentage, height: 4)
                        .cornerRadius(2)
                }
            }
            .frame(height: 4)
        }
    }

    private var missingIngredientsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "cart.badge.plus")
                    .font(.caption)
                    .foregroundColor(.orange)

                Text("Eksik Malzemeler (\(recipe.missingIngredientsCount))")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)

                Spacer()

                Button("Listeye Ekle") {
                    onAddMissingIngredients?()
                }
                .font(.caption2)
                .foregroundColor(Color("TazeBitPrimary"))
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color("TazeBitPrimary").opacity(0.1))
                .cornerRadius(6)
            }

            // Show first 3 missing ingredients
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 6) {
                ForEach(recipe.missingIngredients.prefix(3), id: \.id) { ingredient in
                    HStack {
                        Text(ingredient.name)
                            .font(.caption2)
                            .fontWeight(.medium)
                            .lineLimit(1)

                        Spacer()

                        Text(ingredient.amount)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(4)
                }
            }

            if recipe.missingIngredientsCount > 3 {
                Text("ve \(recipe.missingIngredientsCount - 3) malzeme daha...")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.top, 2)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.orange.opacity(0.05))
        .cornerRadius(8)
    }

    private var tagsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(recipe.tags.prefix(3), id: \.self) { tag in
                    Text(tag)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color("TazeBitPrimary").opacity(0.1))
                        .foregroundColor(Color("TazeBitPrimary"))
                        .cornerRadius(6)
                }

                if recipe.tags.count > 3 {
                    Text("+\(recipe.tags.count - 3)")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(.systemGray5))
                        .foregroundColor(.secondary)
                        .cornerRadius(6)
                }
            }
            .padding(.horizontal, 1) // Prevent clipping
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        RecipeCard(
            recipe: Recipe(
                name: "Domates Soslu Makarna",
                difficulty: .easy,
                preparationTime: 15,
                cookingTime: 20,
                servings: 4,
                ingredients: [
                    RecipeIngredient(name: "Makarna", amount: "500", unit: "gr", isAvailable: true),
                    RecipeIngredient(name: "Domates", amount: "3", unit: "adet", isAvailable: true),
                    RecipeIngredient(name: "Soğan", amount: "1", unit: "adet", isAvailable: false),
                    RecipeIngredient(name: "Sarımsak", amount: "2", unit: "diş", isAvailable: true)
                ],
                instructions: [
                    "Makarnayı kaynar tuzlu suda haşlayın",
                    "Soğan ve sarımsağı kavurun",
                    "Domatesleri ekleyip pişirin",
                    "Makarna ile karıştırıp servis yapın"
                ],
                tips: "Taze fesleğen ekleyerek lezzetini artırabilirsiniz",
                cuisine: .turkish,
                tags: ["Kolay", "Hızlı", "Vejetaryen"]
            ),
            isFavorite: false,
            onFavoriteToggle: {},
            onTap: {},
            onAddMissingIngredients: {}
        )

        RecipeCard(
            recipe: Recipe(
                name: "Tavuklu Pilav",
                difficulty: .medium,
                preparationTime: 20,
                cookingTime: 40,
                servings: 6,
                ingredients: [
                    RecipeIngredient(name: "Tavuk", amount: "500", unit: "gr", isAvailable: true),
                    RecipeIngredient(name: "Pirinç", amount: "2", unit: "su bardağı", isAvailable: true)
                ],
                instructions: ["Tavuğu haşlayın", "Pirinçle karıştırın"],
                cuisine: .turkish,
                tags: ["Ana Yemek"]
            ),
            isFavorite: true,
            onFavoriteToggle: {},
            onTap: {},
            onAddMissingIngredients: {}
        )
    }
    .padding()
}
