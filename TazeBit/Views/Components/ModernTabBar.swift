//
//  ModernTabBar.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 27.05.2025.
//

import SwiftUI

struct TabBarItem {
    let icon: String
    let title: String
    let tag: Int
}

struct ModernTabBar: View {
    @Binding var selectedTab: Int
    let items: [TabBarItem]

    @State private var tabItemWidths: [CGFloat] = []
    @State private var totalWidth: CGFloat = 0
    @State private var backgroundOffset: CGFloat = 0

    var body: some View {
        HStack(spacing: 0) {
            ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                TabBarButton(
                    item: item,
                    isSelected: selectedTab == item.tag,
                    action: {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            selectedTab = item.tag
                        }
                    }
                )
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            ZStack {
                // Main background with glassmorphism effect
                RoundedRectangle(cornerRadius: 28)
                    .fill(.ultraThinMaterial)
                    .background(
                        RoundedRectangle(cornerRadius: 28)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(stops: [
                                        .init(color: Color.white.opacity(0.8), location: 0.0),
                                        .init(color: Color.tazeBitPrimary.opacity(0.1), location: 0.5),
                                        .init(color: Color.gradientEnd.opacity(0.05), location: 1.0)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
                    .shadow(color: .tazeBitPrimary.opacity(0.15), radius: 20, x: 0, y: 8)
                    .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 4)

                // Inner glow effect
                RoundedRectangle(cornerRadius: 28)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.tazeBitPrimary.opacity(0.4),
                                Color.gradientEnd.opacity(0.2),
                                Color.tazeBitPrimary.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1.5
                    )
                    .blur(radius: 0.5)

                // Subtle inner shadow
                RoundedRectangle(cornerRadius: 28)
                    .stroke(Color.white.opacity(0.6), lineWidth: 0.5)
                    .padding(1)
            }
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 4) // Further reduced bottom padding for better positioning
        .scaleEffect(0.98)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: selectedTab)
    }
}

struct TabBarButton: View {
    let item: TabBarItem
    let isSelected: Bool
    let action: () -> Void

    @State private var isPressed = false
    @State private var bounceScale: CGFloat = 1.0

    var body: some View {
        Button(action: {
            action()

            // Bounce animation
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                bounceScale = 1.2
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    bounceScale = 1.0
                }
            }

            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }) {
            VStack(spacing: 6) {
                ZStack {
                    // Background circle for selected state with enhanced effects
                    if isSelected {
                        // Outer glow
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color.tazeBitPrimary.opacity(0.3),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 20,
                                    endRadius: 35
                                )
                            )
                            .frame(width: 50, height: 50)
                            .animation(.easeInOut(duration: 0.6), value: isSelected)

                        // Main background circle
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(stops: [
                                        .init(color: Color.tazeBitPrimary, location: 0.0),
                                        .init(color: Color.gradientStart, location: 0.3),
                                        .init(color: Color.gradientEnd, location: 1.0)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 44, height: 44)
                            .shadow(color: .tazeBitPrimary.opacity(0.5), radius: 12, x: 0, y: 6)
                            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    .padding(2)
                            )
                            .transition(.scale.combined(with: .opacity))
                    }

                    // Icon with enhanced animations
                    Image(systemName: item.icon)
                        .font(.system(size: 19, weight: .semibold))
                        .foregroundColor(isSelected ? .white : .gray.opacity(0.7))
                        .scaleEffect(isSelected ? 1.1 : 1.0)
                        .scaleEffect(bounceScale)
                        .animation(.spring(response: 0.5, dampingFraction: 0.7), value: isSelected)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: bounceScale)
                }

                // Title with better typography
                Text(item.title)
                    .font(.system(size: 11, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? .tazeBitPrimary : .gray.opacity(0.8))
                    .animation(.easeInOut(duration: 0.3), value: isSelected)
            }
            .scaleEffect(isPressed ? 0.92 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

#Preview {
    VStack {
        Spacer()

        ModernTabBar(
            selectedTab: .constant(0),
            items: [
                TabBarItem(icon: "house.fill", title: "Ana Sayfa", tag: 0),
                TabBarItem(icon: "list.bullet", title: "Ürünler", tag: 1),
                TabBarItem(icon: "person.3.fill", title: "Evler", tag: 2),
                TabBarItem(icon: "person.circle.fill", title: "Profil", tag: 3)
            ]
        )
    }
    .background(Color.gray.opacity(0.1))
}
