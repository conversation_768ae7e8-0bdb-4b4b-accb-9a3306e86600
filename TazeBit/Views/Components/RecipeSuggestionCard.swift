//
//  RecipeSuggestionCard.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 29.05.2025.
//

import SwiftUI

struct RecipeSuggestionCard: View {
    let products: [Product]
    @ObservedObject var recipeViewModel: RecipeViewModel
    @State private var showingRecipeList = false
    let householdId: UUID?

    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: "fork.knife")
                            .foregroundColor(.white)
                            .font(.title2)

                        Text("Bugün Ne Pişirsem?")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }

                    Text(recipeViewModel.getRecipeSuggestionText(for: products))
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.leading)
                }

                Spacer()

                // AI Icon
                ZStack {
                    Circle()
                        .fill(.white.opacity(0.2))
                        .frame(width: 50, height: 50)

                    Image(systemName: "brain.head.profile")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            .padding()
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color("TazeBitPrimary"),
                        Color("TazeBitPrimary").opacity(0.8)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )

            // Content
            VStack(spacing: 16) {
                if recipeViewModel.hasExpiringProducts(products) {
                    // Expiring products info
                    ExpiringProductsInfo(products: recipeViewModel.getExpiringProducts(from: products))
                }

                // Action buttons
                VStack(spacing: 12) {
                    // Main suggestion button
                    Button(action: {
                        Task {
                            await recipeViewModel.generateRecipesForExpiringProducts(products)
                            showingRecipeList = true
                        }
                    }) {
                        HStack {
                            if recipeViewModel.isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            } else {
                                Image(systemName: "sparkles")
                                    .font(.title3)
                            }

                            Text(recipeViewModel.isLoading ? "AI Düşünüyor..." : "Tarif Önerisi Al")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color("TazeBitPrimary"),
                                    Color("TazeBitPrimary").opacity(0.8)
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                    }
                    .disabled(recipeViewModel.isLoading || products.isEmpty)

                    // Quick actions
                    HStack(spacing: 12) {
                        // Favorites button
                        Button(action: {
                            showingRecipeList = true
                        }) {
                            HStack {
                                Image(systemName: "heart.fill")
                                    .font(.caption)
                                Text("Favoriler")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(Color("TazeBitPrimary"))
                            .frame(maxWidth: .infinity)
                            .frame(height: 36)
                            .background(Color("TazeBitPrimary").opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                }

                // Error message
                if !recipeViewModel.errorMessage.isEmpty {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                        Text(recipeViewModel.errorMessage)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding()
            .background(Color(.systemBackground))
        }
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .sheet(isPresented: $showingRecipeList) {
            RecipeListView(recipeViewModel: recipeViewModel, householdId: householdId)
        }
    }
}

struct ExpiringProductsInfo: View {
    let products: [Product]

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "clock.fill")
                    .foregroundColor(.orange)
                    .font(.caption)

                Text("Yakında Bozulacak Ürünler")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)

                Spacer()
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(products.prefix(4), id: \.id) { product in
                    HStack {
                        Text(product.name)
                            .font(.caption2)
                            .fontWeight(.medium)
                            .lineLimit(1)

                        Spacer()

                        Text(product.quantityWithUnit)
                            .font(.caption2)
                            .foregroundColor(.orange)
                            .fontWeight(.semibold)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(6)
                }
            }

            if products.count > 4 {
                Text("ve \(products.count - 4) ürün daha...")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
            }
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }
}

#Preview {
    RecipeSuggestionCard(
        products: [
            Product(
                id: UUID(),
                householdId: UUID(),
                name: "Domates",
                category: "vegetables",
                storageLocation: "refrigerator",
                expiryDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()) ?? Date(),
                purchaseDate: Date(),
                originalQuantity: 5,
                currentQuantity: 3,
                unit: "piece",
                notes: nil,
                addedBy: UUID(),
                shoppingListItemId: nil,
                createdAt: Date(),
                updatedAt: Date()
            ),
            Product(
                id: UUID(),
                householdId: UUID(),
                name: "Soğan",
                category: "vegetables",
                storageLocation: "pantry",
                expiryDate: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date(),
                purchaseDate: Date(),
                originalQuantity: 3,
                currentQuantity: 2,
                unit: "piece",
                notes: nil,
                addedBy: UUID(),
                shoppingListItemId: nil,
                createdAt: Date(),
                updatedAt: Date()
            )
        ],
        recipeViewModel: RecipeViewModel(),
        householdId: UUID()
    )
    .padding()
}
