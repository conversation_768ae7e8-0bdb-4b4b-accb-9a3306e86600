//
//  ReportComponents.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 27.05.2025.
//

import SwiftUI

// MARK: - Time Range Button
struct TimeRangeButton: View {
    let timeRange: TimeRange
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(timeRange.rawValue)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.tazeBitPrimary : Color.cardBackground)
                        .stroke(Color.borderColor, lineWidth: isSelected ? 0 : 1)
                )
        }
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Quick Stat Card
struct QuickStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 12) {
            H<PERSON><PERSON><PERSON> {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(color.opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - Detailed Report Card
struct DetailedReportCard: View {
    let reportType: ReportType
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: reportType.icon)
                    .font(.title)
                    .foregroundColor(.tazeBitPrimary)

                Text(reportType.rawValue)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)
                    .multilineTextAlignment(.center)

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            .frame(maxWidth: .infinity)
            .padding(16)
            .background(Color.cardBackground)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.borderColor, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Expiry Status Chart View
struct ExpiryStatusChartView: View {
    let products: [Product]

    private var chartData: [ChartData] {
        let expired = products.filter { $0.expiryStatus == .expired }.count
        let expiringSoon = products.filter { $0.expiryStatus == .expiringSoon }.count
        let expiringThisWeek = products.filter { $0.expiryStatus == .expiringThisWeek }.count
        let fresh = products.filter { $0.expiryStatus == .fresh }.count

        return [
            ChartData(label: "Süresi Geçen", value: expired, color: .red),
            ChartData(label: "Yakında Bitecek", value: expiringSoon, color: .orange),
            ChartData(label: "Bu Hafta", value: expiringThisWeek, color: .yellow),
            ChartData(label: "Taze", value: fresh, color: .green)
        ].filter { $0.value > 0 }
    }

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Son Kullanma Durumu")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if chartData.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "chart.pie")
                        .font(.title)
                        .foregroundColor(.textSecondary)

                    Text("Henüz ürün yok")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)
                }
                .frame(height: 120)
            } else {
                HStack(spacing: 16) {
                    // Simple Bar Chart
                    HStack(alignment: .bottom, spacing: 8) {
                        ForEach(chartData, id: \.label) { data in
                            VStack(spacing: 4) {
                                Rectangle()
                                    .fill(data.color)
                                    .frame(width: 20, height: CGFloat(data.value * 10 + 20))
                                    .cornerRadius(4)

                                Text("\(data.value)")
                                    .font(.caption2)
                                    .foregroundColor(.textSecondary)
                            }
                        }
                    }
                    .frame(height: 120)

                    Spacer()

                    // Legend
                    VStack(alignment: .leading, spacing: 8) {
                        ForEach(chartData, id: \.label) { data in
                            HStack(spacing: 8) {
                                Circle()
                                    .fill(data.color)
                                    .frame(width: 8, height: 8)

                                Text(data.label)
                                    .font(.caption)
                                    .foregroundColor(.textSecondary)

                                Spacer()

                                Text("\(data.value)")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.textPrimary)
                            }
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }
}

// MARK: - Category Distribution Chart View
struct CategoryDistributionChartView: View {
    let products: [Product]

    private var categoryData: [ChartData] {
        let categories = Dictionary(grouping: products) { product in
            ProductCategory(rawValue: product.category ?? "other") ?? .other
        }

        return categories.map { category, products in
            ChartData(
                label: category.displayName,
                value: products.count,
                color: categoryColor(for: category)
            )
        }
        .sorted { $0.value > $1.value }
        .prefix(5)
        .map { $0 }
    }

    private func categoryColor(for category: ProductCategory) -> Color {
        switch category {
        case .dairy: return .blue
        case .meat: return .red
        case .vegetables: return .green
        case .fruits: return .orange
        case .grains: return .brown
        case .beverages: return .cyan
        case .snacks: return .purple
        case .frozen: return .indigo
        case .canned: return .teal
        case .other: return .gray
        }
    }

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Kategori Dağılımı")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if categoryData.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "square.grid.2x2")
                        .font(.title)
                        .foregroundColor(.textSecondary)

                    Text("Henüz ürün yok")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)
                }
                .frame(height: 120)
            } else {
                VStack(spacing: 8) {
                    ForEach(categoryData, id: \.label) { data in
                        HStack {
                            Text(data.label)
                                .font(.caption)
                                .foregroundColor(.textPrimary)

                            Spacer()

                            // Progress bar
                            GeometryReader { geometry in
                                ZStack(alignment: .leading) {
                                    Rectangle()
                                        .fill(Color.borderColor)
                                        .frame(height: 6)
                                        .cornerRadius(3)

                                    Rectangle()
                                        .fill(data.color)
                                        .frame(
                                            width: geometry.size.width * CGFloat(data.value) / CGFloat(products.count),
                                            height: 6
                                        )
                                        .cornerRadius(3)
                                }
                            }
                            .frame(height: 6)

                            Text("\(data.value)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.textPrimary)
                                .frame(minWidth: 20)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }
}

// MARK: - Waste Analysis Chart View
struct WasteAnalysisChartView: View {
    let products: [Product]
    let consumptions: [Consumption]

    private var wasteData: [ChartData] {
        // Consumption kayıtlarından israf verilerini al
        let wastedConsumptions = consumptions.filter { $0.wastedQuantity > 0 }

        // Kategori bazında grupla
        let categoryWasteMap = Dictionary(grouping: wastedConsumptions) { consumption in
            let product = products.first { $0.id == consumption.productId }
            return ProductCategory(rawValue: product?.category ?? "other") ?? .other
        }

        return categoryWasteMap.map { category, consumptions in
            let totalWasted = consumptions.reduce(0) { $0 + $1.wastedQuantity }
            return ChartData(
                label: category.displayName,
                value: totalWasted,
                color: categoryColor(for: category)
            )
        }
        .sorted { $0.value > $1.value }
        .prefix(5)
        .map { $0 }
    }

    private func categoryColor(for category: ProductCategory) -> Color {
        switch category {
        case .dairy: return .blue
        case .meat: return .red
        case .vegetables: return .green
        case .fruits: return .orange
        case .grains: return .brown
        case .beverages: return .cyan
        case .snacks: return .purple
        case .frozen: return .indigo
        case .canned: return .teal
        case .other: return .gray
        }
    }

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("İsraf Analizi")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if wasteData.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.green)

                    Text("Hiç israf yok! 🎉")
                        .font(.subheadline)
                        .foregroundColor(.green)
                }
                .frame(height: 120)
            } else {
                VStack(spacing: 8) {
                    ForEach(wasteData, id: \.label) { data in
                        HStack {
                            Text(data.label)
                                .font(.caption)
                                .foregroundColor(.textPrimary)

                            Spacer()

                            // Progress bar
                            GeometryReader { geometry in
                                ZStack(alignment: .leading) {
                                    Rectangle()
                                        .fill(Color.borderColor)
                                        .frame(height: 6)
                                        .cornerRadius(3)

                                    Rectangle()
                                        .fill(data.color)
                                        .frame(
                                            width: geometry.size.width * CGFloat(data.value) / CGFloat(wasteData.map { $0.value }.max() ?? 1),
                                            height: 6
                                        )
                                        .cornerRadius(3)
                                }
                            }
                            .frame(height: 6)

                            Text("\(data.value)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.textPrimary)
                                .frame(minWidth: 20)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }
}

// MARK: - Waste Report Charts
struct WasteReportChartsView: View {
    let products: [Product]
    let consumptions: [Consumption]

    private var wastedConsumptions: [Consumption] {
        consumptions.filter { $0.wastedQuantity > 0 }
    }

    private var categoryWasteData: [ChartData] {
        let categoryWasteMap = Dictionary(grouping: wastedConsumptions) { consumption in
            let product = products.first { $0.id == consumption.productId }
            return ProductCategory(rawValue: product?.category ?? "other") ?? .other
        }

        return categoryWasteMap.map { category, consumptions in
            let totalWasted = consumptions.reduce(0) { $0 + $1.wastedQuantity }
            return ChartData(
                label: category.displayName,
                value: totalWasted,
                color: categoryColor(for: category)
            )
        }
        .sorted { $0.value > $1.value }
    }

    private var productWasteData: [ChartData] {
        // Ürün adına göre grupla
        let productWasteMap = Dictionary(grouping: wastedConsumptions) { consumption in
            products.first { $0.id == consumption.productId }?.name ?? "Bilinmeyen"
        }

        return productWasteMap.map { productName, consumptions in
            let totalWasted = consumptions.reduce(0) { $0 + $1.wastedQuantity }
            return ChartData(
                label: productName,
                value: totalWasted,
                color: .red.opacity(0.7)
            )
        }
        .sorted { $0.value > $1.value }
        .prefix(5) // En çok israf edilen 5 ürün
        .map { $0 }
    }

    private func categoryColor(for category: ProductCategory) -> Color {
        switch category {
        case .dairy: return .blue
        case .meat: return .red
        case .vegetables: return .green
        case .fruits: return .orange
        case .grains: return .brown
        case .beverages: return .cyan
        case .snacks: return .purple
        case .frozen: return .indigo
        case .canned: return .teal
        case .other: return .gray
        }
    }

    var body: some View {
        VStack(spacing: 20) {
            // Kategori Pasta Grafiği
            categoryPieChart

            // En Çok İsraf Edilen Ürünler
            topWastedProducts
        }
    }

    private var categoryPieChart: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Kategori Bazında İsraf")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if categoryWasteData.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.green)

                    Text("Hiç israf yok! 🎉")
                        .font(.subheadline)
                        .foregroundColor(.green)
                }
                .frame(height: 120)
            } else {
                HStack(spacing: 20) {
                    // Pasta Grafiği
                    PieChartView(data: categoryWasteData)
                        .frame(width: 120, height: 120)

                    // Legend
                    VStack(alignment: .leading, spacing: 6) {
                        ForEach(categoryWasteData.prefix(4), id: \.label) { data in
                            HStack(spacing: 8) {
                                Circle()
                                    .fill(data.color)
                                    .frame(width: 12, height: 12)

                                Text(data.label)
                                    .font(.caption)
                                    .foregroundColor(.textPrimary)

                                Spacer()

                                Text("\(data.value)")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.textPrimary)
                            }
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }

    private var topWastedProducts: some View {
        VStack(spacing: 12) {
            HStack {
                Text("En Çok İsraf Edilen Ürünler")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if productWasteData.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.green)

                    Text("Hiç israf yok! 🎉")
                        .font(.subheadline)
                        .foregroundColor(.green)
                }
                .frame(height: 80)
            } else {
                VStack(spacing: 8) {
                    ForEach(productWasteData, id: \.label) { data in
                        HStack {
                            Text(data.label)
                                .font(.caption)
                                .foregroundColor(.textPrimary)
                                .lineLimit(1)

                            Spacer()

                            // Progress bar
                            GeometryReader { geometry in
                                ZStack(alignment: .leading) {
                                    Rectangle()
                                        .fill(Color.borderColor)
                                        .frame(height: 6)
                                        .cornerRadius(3)

                                    Rectangle()
                                        .fill(data.color)
                                        .frame(
                                            width: geometry.size.width * CGFloat(data.value) / CGFloat(productWasteData.map { $0.value }.max() ?? 1),
                                            height: 6
                                        )
                                        .cornerRadius(3)
                                }
                            }
                            .frame(height: 6)

                            Text("\(data.value)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.textPrimary)
                                .frame(minWidth: 20)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }
}

// MARK: - Pie Chart View
struct PieChartView: View {
    let data: [ChartData]

    private var total: Int {
        data.reduce(0) { $0 + $1.value }
    }

    var body: some View {
        ZStack {
            if data.isEmpty {
                Circle()
                    .fill(Color.gray.opacity(0.3))
            } else {
                ForEach(Array(data.enumerated()), id: \.offset) { index, item in
                    PieSlice(
                        startAngle: startAngle(for: index),
                        endAngle: endAngle(for: index),
                        color: item.color
                    )
                }
            }
        }
    }

    private func startAngle(for index: Int) -> Angle {
        let previousValues = data.prefix(index).reduce(0) { $0 + $1.value }
        return Angle(degrees: Double(previousValues) / Double(total) * 360 - 90)
    }

    private func endAngle(for index: Int) -> Angle {
        let previousValues = data.prefix(index + 1).reduce(0) { $0 + $1.value }
        return Angle(degrees: Double(previousValues) / Double(total) * 360 - 90)
    }
}

// MARK: - Pie Slice
struct PieSlice: View {
    let startAngle: Angle
    let endAngle: Angle
    let color: Color

    var body: some View {
        Path { path in
            let center = CGPoint(x: 60, y: 60)
            let radius: CGFloat = 50

            path.move(to: center)
            path.addArc(
                center: center,
                radius: radius,
                startAngle: startAngle,
                endAngle: endAngle,
                clockwise: false
            )
            path.closeSubpath()
        }
        .fill(color)
    }
}

// MARK: - Summary Stat Card
struct SummaryStatCard: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)

            Text(title)
                .font(.caption)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - Sort Button
struct SortButton: View {
    let sortOption: SortOption
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(sortOption.rawValue)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .textPrimary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? Color.tazeBitPrimary : Color.cardBackground)
                        .stroke(Color.borderColor, lineWidth: isSelected ? 0 : 1)
                )
        }
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Report Product Row
struct ReportProductRow: View {
    let product: Product
    let reportType: ReportType
    let consumptions: [Consumption]

    var body: some View {
        HStack(spacing: 12) {
            // Category Icon
            Text(categoryIcon)
                .font(.title2)

            // Product Info
            VStack(alignment: .leading, spacing: 4) {
                Text(product.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                if let category = ProductCategory(rawValue: product.category ?? "other") {
                    Text(category.displayName)
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }

            Spacer()

            // Report-specific info
            VStack(alignment: .trailing, spacing: 4) {
                Text(primaryInfo)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(primaryInfoColor)

                Text(secondaryInfo)
                    .font(.caption2)
                    .foregroundColor(.textSecondary)
            }
        }
        .padding(12)
        .background(Color.cardBackground)
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }

    private var categoryIcon: String {
        if let category = ProductCategory(rawValue: product.category ?? "other") {
            return category.icon
        }
        return "📦"
    }

    private var primaryInfo: String {
        switch reportType {
        case .expiry:
            let days = product.daysUntilExpiry
            if days < 0 {
                return "\(-days) gün geçti"
            } else if days == 0 {
                return "Bugün bitiyor"
            } else {
                return "\(days) gün kaldı"
            }
        case .category:
            return product.quantityWithUnit
        case .waste:
            let totalWasted = consumptions
                .filter { $0.productId == product.id && $0.wastedQuantity > 0 }
                .reduce(0) { $0 + $1.wastedQuantity }
            return "\(totalWasted) \(product.unit ?? "adet")"
        case .shopping:
            return (product.purchaseDate ?? product.createdAt).turkishShortString()
        }
    }

    private var secondaryInfo: String {
        switch reportType {
        case .expiry:
            return product.quantityWithUnit
        case .category:
            return "SKT: \(product.expiryDate.turkishShortString())"
        case .waste:
            let wasteConsumptions = consumptions
                .filter { $0.productId == product.id && $0.wastedQuantity > 0 }
                .sorted { $0.consumedAt > $1.consumedAt }

            if let latestWaste = wasteConsumptions.first {
                return "İsraf tarihi: \(latestWaste.consumedAt.turkishShortString())"
            }
            return "İsraf tarihi bilinmiyor"
        case .shopping:
            return product.quantityWithUnit
        }
    }

    private var primaryInfoColor: Color {
        switch reportType {
        case .expiry:
            return product.expiryStatus.color
        case .category:
            return .textPrimary
        case .waste:
            return .red
        case .shopping:
            return .textPrimary
        }
    }
}

// MARK: - Empty Report View
struct EmptyReportView: View {
    let reportType: ReportType

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: reportType.icon)
                .font(.system(size: 48))
                .foregroundColor(.textSecondary)

            VStack(spacing: 8) {
                Text("Rapor Bulunamadı")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text(emptyMessage)
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }

    private var emptyMessage: String {
        switch reportType {
        case .expiry:
            return "Seçilen zaman aralığında son kullanma tarihi yaklaşan ürün bulunmuyor."
        case .category:
            return "Henüz hiç ürün eklenmemiş."
        case .waste:
            return "Süresi geçen ürün bulunmuyor. Harika!"
        case .shopping:
            return "Seçilen zaman aralığında alışveriş yapılmamış."
        }
    }
}



// MARK: - Extensions
extension ExpiryStatus {
    var color: Color {
        switch self {
        case .expired: return .red
        case .expiringSoon: return .orange
        case .expiringThisWeek: return .yellow
        case .fresh: return .green
        case .consumed: return .blue
        }
    }
}

// MARK: - Chart Data Model
struct ChartData {
    let label: String
    let value: Int
    let color: Color
}

#Preview {
    VStack(spacing: 20) {
        QuickStatCard(
            title: "Toplam Ürün",
            value: "24",
            icon: "cube.box.fill",
            color: .blue
        )

        DetailedReportCard(reportType: .expiry) {
            print("Report tapped")
        }
    }
    .padding()
    .background(Color.tazeBitBackground)
}
