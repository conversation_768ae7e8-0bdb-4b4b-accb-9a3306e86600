//
//  FilterButton.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 11.06.2025.
//

import SwiftUI

struct FilterButton: View {
    let title: String
    let count: Int?
    let icon: String?
    let color: String?
    let isSelected: Bool
    let action: () -> Void
    
    // Convenience initializers
    init(title: String, count: Int? = nil, isSelected: Bool, action: @escaping () -> Void) {
        self.title = title
        self.count = count
        self.icon = nil
        self.color = nil
        self.isSelected = isSelected
        self.action = action
    }
    
    init(title: String, count: Int? = nil, icon: String? = nil, isSelected: Bool, action: @escaping () -> Void) {
        self.title = title
        self.count = count
        self.icon = icon
        self.color = nil
        self.isSelected = isSelected
        self.action = action
    }
    
    init(title: String, count: Int? = nil, icon: String? = nil, color: String? = nil, isSelected: Bool, action: @escaping () -> Void) {
        self.title = title
        self.count = count
        self.icon = icon
        self.color = color
        self.isSelected = isSelected
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                // Icon
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.caption)
                        .foregroundColor(iconColor)
                }
                
                // Title
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(textColor)
                
                // Count badge
                if let count = count, count > 0 {
                    Text("\(count)")
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(badgeTextColor)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(badgeBackgroundColor)
                        )
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(backgroundColor)
                    .stroke(borderColor, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Color Computed Properties
    private var backgroundColor: Color {
        if isSelected {
            return Color.tazeBitPrimary
        } else {
            return Color.cardBackground
        }
    }
    
    private var borderColor: Color {
        if isSelected {
            return Color.tazeBitPrimary
        } else {
            return Color.gray.opacity(0.3)
        }
    }
    
    private var textColor: Color {
        if isSelected {
            return .white
        } else {
            return .textPrimary
        }
    }
    
    private var iconColor: Color {
        if let color = color, !isSelected {
            switch color {
            case "red":
                return .red
            case "orange":
                return .orange
            case "yellow":
                return .yellow
            case "green":
                return .green
            default:
                return textColor
            }
        } else {
            return textColor
        }
    }
    
    private var badgeBackgroundColor: Color {
        if isSelected {
            return Color.white.opacity(0.2)
        } else {
            return Color.tazeBitPrimary.opacity(0.1)
        }
    }
    
    private var badgeTextColor: Color {
        if isSelected {
            return .white
        } else {
            return .tazeBitPrimary
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        HStack {
            FilterButton(
                title: "Tümü",
                count: 25,
                isSelected: true
            ) {
                print("All tapped")
            }
            
            FilterButton(
                title: "Kritik",
                count: 3,
                icon: "exclamationmark.triangle.fill",
                color: "red",
                isSelected: false
            ) {
                print("Critical tapped")
            }
        }
        
        HStack {
            FilterButton(
                title: "Süt Ürünleri",
                count: 8,
                icon: "drop.fill",
                isSelected: false
            ) {
                print("Dairy tapped")
            }
            
            FilterButton(
                title: "Buzdolabı",
                count: 15,
                icon: "snowflake",
                isSelected: true
            ) {
                print("Refrigerator tapped")
            }
        }
    }
    .padding()
    .background(Color.tazeBitBackground)
}
