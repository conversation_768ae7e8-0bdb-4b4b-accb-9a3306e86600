import SwiftUI
import UserNotifications

struct NotificationSettingsView: View {
    @EnvironmentObject var notificationManager: NotificationManager
    @EnvironmentObject var productViewModel: ProductViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var pendingNotifications: [UNNotificationRequest] = []
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header Section
                    headerSection
                    
                    // Permission Status Section
                    permissionStatusSection
                    
                    // Notification List Section
                    if notificationManager.isAuthorized {
                        notificationListSection
                    }
                    
                    // Quick Actions Section
                    quickActionsSection
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
            .background(Color.tazeBitBackground.ignoresSafeArea())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Kapat") {
                        dismiss()
                    }
                    .foregroundColor(.tazeBitPrimary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Ayarlar") {
                        notificationManager.openNotificationSettings()
                    }
                    .foregroundColor(.tazeBitPrimary)
                }
            }
        }
        .onAppear {
            loadPendingNotifications()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: notificationManager.isAuthorized ? "bell.circle.fill" : "bell.slash.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(notificationManager.isAuthorized ? .tazeBitPrimary : .gray)
            
            VStack(spacing: 8) {
                Text("Bildirim Ayarları")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)
                
                Text("Son kullanma tarihi yaklaşan ürünler için bildirim alın")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(24)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Permission Status Section
    private var permissionStatusSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("İzin Durumu")
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                StatusBadge(
                    text: statusText,
                    color: statusColor
                )
            }
            
            if !notificationManager.isAuthorized {
                VStack(spacing: 12) {
                    Text("Bildirimler için izin gerekli")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)
                    
                    Button(action: {
                        Task {
                            await requestPermission()
                        }
                    }) {
                        HStack {
                            Image(systemName: "bell.badge")
                            Text("İzin Ver")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.tazeBitPrimary)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Notification List Section
    private var notificationListSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Planlanmış Bildirimler")
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                Button("Yenile") {
                    loadPendingNotifications()
                }
                .font(.caption)
                .foregroundColor(.tazeBitPrimary)
            }
            
            if isLoading {
                ProgressView()
                    .frame(maxWidth: .infinity)
                    .padding()
            } else if pendingNotifications.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "bell.slash")
                        .font(.title)
                        .foregroundColor(.gray)
                    
                    Text("Planlanmış bildirim yok")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(24)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(pendingNotifications.prefix(10), id: \.identifier) { notification in
                        NotificationCard(notification: notification)
                    }
                    
                    if pendingNotifications.count > 10 {
                        Text("ve \(pendingNotifications.count - 10) bildirim daha...")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(spacing: 12) {
            if notificationManager.isAuthorized {
                Button(action: {
                    Task {
                        await scheduleNotifications()
                    }
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("Bildirimleri Yeniden Planla")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.tazeBitPrimary)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                
                Button(action: {
                    notificationManager.cancelAllExpiryNotifications()
                    loadPendingNotifications()
                }) {
                    HStack {
                        Image(systemName: "trash")
                        Text("Tüm Bildirimleri İptal Et")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .foregroundColor(.red)
                    .cornerRadius(12)
                }
            }
            
            Button(action: {
                notificationManager.clearBadge()
            }) {
                HStack {
                    Image(systemName: "app.badge.checkmark")
                    Text("Badge Temizle")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.gray.opacity(0.1))
                .foregroundColor(.gray)
                .cornerRadius(12)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Helper Properties
    private var statusText: String {
        switch notificationManager.authorizationStatus {
        case .authorized:
            return "İzin Verildi"
        case .denied:
            return "İzin Reddedildi"
        case .notDetermined:
            return "Belirlenmedi"
        case .provisional:
            return "Geçici İzin"
        case .ephemeral:
            return "Geçici"
        @unknown default:
            return "Bilinmiyor"
        }
    }
    
    private var statusColor: Color {
        switch notificationManager.authorizationStatus {
        case .authorized:
            return .green
        case .denied:
            return .red
        case .notDetermined:
            return .orange
        case .provisional:
            return .blue
        case .ephemeral:
            return .purple
        @unknown default:
            return .gray
        }
    }
    
    // MARK: - Helper Methods
    private func loadPendingNotifications() {
        isLoading = true
        Task {
            let notifications = await notificationManager.getPendingNotifications()
            await MainActor.run {
                self.pendingNotifications = notifications.filter { $0.identifier.contains("expiry_") }
                self.isLoading = false
            }
        }
    }
    
    private func requestPermission() async {
        await notificationManager.requestAuthorization()
        loadPendingNotifications()
    }
    
    private func scheduleNotifications() async {
        await productViewModel.scheduleNotificationsForProducts()
        loadPendingNotifications()
    }
}

// MARK: - Supporting Views

struct StatusBadge: View {
    let text: String
    let color: Color
    
    var body: some View {
        Text(text)
            .font(.caption)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(color.opacity(0.2))
            .foregroundColor(color)
            .cornerRadius(8)
    }
}

struct NotificationCard: View {
    let notification: UNNotificationRequest
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(notification.content.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                if let trigger = notification.trigger as? UNCalendarNotificationTrigger,
                   let nextTriggerDate = trigger.nextTriggerDate() {
                    Text(nextTriggerDate, style: .date)
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                }
            }
            
            Text(notification.content.body)
                .font(.caption)
                .foregroundColor(.textSecondary)
                .lineLimit(2)
        }
        .padding(12)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }
}

#Preview {
    NotificationSettingsView()
        .environmentObject(NotificationManager.shared)
        .environmentObject(ProductViewModel())
}
