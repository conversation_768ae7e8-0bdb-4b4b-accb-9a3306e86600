//
//  DetailedReportView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 27.05.2025.
//

import SwiftUI

struct DetailedReportView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject private var consumptionViewModel: ConsumptionViewModel
    @Environment(\.dismiss) private var dismiss

    let reportType: ReportType
    let timeRange: TimeRange

    @State private var sortOption: SortOption = .name

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 20) {
                            // Summary Card
                            summaryCard

                            // Waste Charts (only for waste report)
                            if reportType == .waste {
                                wasteChartsSection
                            }

                            // Sort Options
                            sortOptionsSection

                            // Report Content
                            reportContentSection
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    VStack(spacing: 2) {
                        Text(reportType.rawValue)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text(timeRange.rawValue)
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }

                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Waste Charts Section
    private var wasteChartsSection: some View {
        WasteReportChartsView(products: productViewModel.products, consumptions: consumptionViewModel.consumptions)
    }

    // MARK: - Summary Card
    private var summaryCard: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: reportType.icon)
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Özet")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                SummaryStatCard(
                    title: "Toplam",
                    value: "\(filteredProducts.count)",
                    color: .blue
                )

                SummaryStatCard(
                    title: summaryMiddleTitle,
                    value: summaryMiddleValue,
                    color: .orange
                )

                SummaryStatCard(
                    title: summaryRightTitle,
                    value: summaryRightValue,
                    color: .green
                )
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Sort Options Section
    private var sortOptionsSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "arrow.up.arrow.down")
                    .font(.title3)
                    .foregroundColor(.tazeBitPrimary)

                Text("Sıralama")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(SortOption.allCases, id: \.self) { option in
                        SortButton(
                            sortOption: option,
                            isSelected: sortOption == option
                        ) {
                            sortOption = option
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Report Content Section
    private var reportContentSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "list.bullet")
                    .font(.title3)
                    .foregroundColor(.tazeBitPrimary)

                Text("Detaylar")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("\(sortedProducts.count) ürün")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }

            if sortedProducts.isEmpty {
                EmptyReportView(reportType: reportType)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(sortedProducts) { product in
                        ReportProductRow(product: product, reportType: reportType, consumptions: consumptionViewModel.consumptions)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
}

// MARK: - Computed Properties
extension DetailedReportView {
    private var filteredProducts: [Product] {
        let now = Date()
        let calendar = Calendar.current

        switch reportType {
        case .expiry:
            return productViewModel.products.filter { product in
                let daysUntilExpiry = calendar.dateComponents([.day], from: now, to: product.expiryDate).day ?? 0
                return daysUntilExpiry <= timeRange.days
            }
        case .category:
            // Sadece aktif ürünleri göster (süresi geçmemiş ve tüketilmemiş)
            return productViewModel.products.filter { $0.currentQuantity > 0 }
        case .waste:
            // İsraf edilen ürünler: wasted_quantity > 0 olan consumption kayıtları bulunan ürünler
            let wastedProductIds = Set(consumptionViewModel.consumptions
                .filter { $0.wastedQuantity > 0 }
                .map { $0.productId })

            return productViewModel.products.filter { product in
                wastedProductIds.contains(product.id)
            }
        case .shopping:
            // Seçilen zaman aralığında satın alınan ürünler
            return productViewModel.products.filter { product in
                let purchaseDate = product.purchaseDate ?? product.createdAt
                let daysOld = calendar.dateComponents([.day], from: purchaseDate, to: now).day ?? 0
                return daysOld <= timeRange.days
            }.sorted { ($0.purchaseDate ?? $0.createdAt) > ($1.purchaseDate ?? $1.createdAt) }
        }
    }

    private var sortedProducts: [Product] {
        switch sortOption {
        case .name:
            return filteredProducts.sorted { $0.name < $1.name }
        case .expiryDate:
            return filteredProducts.sorted { $0.expiryDate < $1.expiryDate }
        case .category:
            return filteredProducts.sorted { ($0.category ?? "") < ($1.category ?? "") }
        case .quantity:
            return filteredProducts.sorted { $0.currentQuantity > $1.currentQuantity }
        }
    }

    private var summaryMiddleTitle: String {
        switch reportType {
        case .expiry: return "Kritik"
        case .category: return "Kategori"
        case .waste: return "İsraf Miktarı"
        case .shopping: return "Yeni"
        }
    }

    private var summaryMiddleValue: String {
        switch reportType {
        case .expiry:
            return "\(filteredProducts.filter { $0.expiryStatus == .expiringSoon }.count)"
        case .category:
            let categories = Set(filteredProducts.compactMap { $0.category })
            return "\(categories.count)"
        case .waste:
            let totalWastedQuantity = consumptionViewModel.consumptions
                .filter { consumption in
                    filteredProducts.contains { $0.id == consumption.productId }
                }
                .reduce(0) { $0 + $1.wastedQuantity }
            return "\(totalWastedQuantity)"
        case .shopping:
            let recentProducts = filteredProducts.filter { product in
                let daysOld = Calendar.current.dateComponents([.day], from: product.purchaseDate ?? product.createdAt, to: Date()).day ?? 0
                return daysOld <= 7
            }
            return "\(recentProducts.count)"
        }
    }

    private var summaryRightTitle: String {
        switch reportType {
        case .expiry: return "Taze"
        case .category: return "En Çok"
        case .waste: return "En Çok İsraf Edilen"
        case .shopping: return "Bu Hafta"
        }
    }

    private var summaryRightValue: String {
        switch reportType {
        case .expiry:
            return "\(filteredProducts.filter { $0.expiryStatus == .fresh }.count)"
        case .category:
            let categoryGroups = Dictionary(grouping: filteredProducts) { $0.category ?? "other" }
            let maxCount = categoryGroups.values.map { $0.count }.max() ?? 0
            return "\(maxCount)"
        case .waste:
            // En çok israf edilen ürünü hesapla
            let productWasteMap = Dictionary(grouping: consumptionViewModel.consumptions.filter { $0.wastedQuantity > 0 }) { consumption in
                filteredProducts.first { $0.id == consumption.productId }?.name ?? "Bilinmeyen"
            }

            let productTotals = productWasteMap.mapValues { consumptions in
                consumptions.reduce(0) { $0 + $1.wastedQuantity }
            }

            if let mostWastedProduct = productTotals.max(by: { $0.value < $1.value }) {
                return mostWastedProduct.key
            }
            return "Yok"
        case .shopping:
            let thisWeekProducts = filteredProducts.filter { product in
                let daysOld = Calendar.current.dateComponents([.day], from: product.purchaseDate ?? product.createdAt, to: Date()).day ?? 0
                return daysOld <= 7
            }
            return "\(thisWeekProducts.count)"
        }
    }
}

// MARK: - Sort Option Enum
enum SortOption: String, CaseIterable {
    case name = "İsim"
    case expiryDate = "Son Kullanma"
    case category = "Kategori"
    case quantity = "Miktar"
}

#Preview {
    DetailedReportView(reportType: .expiry, timeRange: .thisWeek)
        .environmentObject(HouseholdViewModel())
        .environmentObject(ProductViewModel())
}
