import SwiftUI

struct ShoppingListItemRowView: View {
    let item: ShoppingListItem
    let onToggle: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void
    let onAddToHouse: (() -> Void)?

    @State private var showingMenu = false

    var body: some View {
        HStack(spacing: 12) {
            // Checkbox
            Button(action: onToggle) {
                Image(systemName: item.isPurchased ? "checkmark.circle.fill" : "circle")
                    .font(.title3)
                    .foregroundColor(item.isPurchased ? .green : .textSecondary)
                    .animation(.easeInOut(duration: 0.2), value: item.isPurchased)
            }

            // Item Content
            VStack(alignment: .leading, spacing: 4) {
                // Name and Quantity
                HStack {
                    Text(item.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(item.isPurchased ? .textSecondary : .textPrimary)
                        .strikethrough(item.isPurchased)

                    Spacer()

                    // Quantity
                    HStack(spacing: 2) {
                        Text("\(formattedQuantity)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(item.isPurchased ? .textSecondary : .textPrimary)

                        if let unit = item.unit, !unit.isEmpty {
                            Text(ProductUnit(rawValue: unit)?.displayName ?? unit)
                                .font(.caption)
                                .foregroundColor(item.isPurchased ? .textSecondary : .textSecondary)
                        }
                    }
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(item.isPurchased ? Color.gray.opacity(0.1) : Color.tazeBitPrimary.opacity(0.1))
                    )
                }

                // Category and Notes
                HStack {
                    if let category = item.category, !category.isEmpty {
                        Text(ProductCategory(rawValue: category)?.displayName ?? category)
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.blue.opacity(0.1))
                            )
                    }

                    if let notes = item.notes, !notes.isEmpty {
                        Text(notes)
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                            .lineLimit(1)
                    }

                    Spacer()
                }
            }

            // Action Buttons
            HStack(spacing: 8) {
                // Add to House Button (only show if purchased, not added to house, and callback provided)
                if item.isPurchased && !item.isAddedToHouse, let onAddToHouse = onAddToHouse {
                    Button(action: onAddToHouse) {
                        Image(systemName: "house.fill")
                            .font(.caption)
                            .foregroundColor(.white)
                            .frame(width: 28, height: 28)
                            .background(
                                Circle()
                                    .fill(Color.tazeBitPrimary)
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                // Added to House Indicator
                if item.isAddedToHouse {
                    Image(systemName: "house.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                        .frame(width: 28, height: 28)
                }

                // Menu Button
                Button(action: {
                    showingMenu = true
                }) {
                    Image(systemName: "ellipsis")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)
                        .frame(width: 24, height: 24)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .shadow(color: Color.cardShadow, radius: 2, x: 0, y: 1)
        .opacity(item.isPurchased ? 0.7 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: item.isPurchased)
        .confirmationDialog("Ürün İşlemleri", isPresented: $showingMenu) {
            Button(item.isPurchased ? "Alınmadı Olarak İşaretle" : "Alındı Olarak İşaretle") {
                onToggle()
            }

            if item.isPurchased && !item.isAddedToHouse, let onAddToHouse = onAddToHouse {
                Button("Eve Ekle") {
                    onAddToHouse()
                }
            }

            Button("Düzenle") {
                onEdit()
            }

            Button("Sil", role: .destructive) {
                onDelete()
            }

            Button("İptal", role: .cancel) { }
        }
    }

    // MARK: - Computed Properties
    private var formattedQuantity: String {
        if item.quantity == floor(item.quantity) {
            return String(Int(item.quantity))
        } else {
            return String(format: "%.1f", item.quantity)
        }
    }
}

#Preview {
    VStack(spacing: 12) {
        ShoppingListItemRowView(
            item: ShoppingListItem(
                id: UUID(),
                shoppingListId: UUID(),
                name: "Süt",
                quantity: 2,
                unit: "litre",
                category: "Süt Ürünleri",
                notes: "Az yağlı tercih et",
                isPurchased: false,
                isAddedToHouse: false,
                addedToHouseAt: nil,
                createdAt: Date(),
                updatedAt: Date()
            ),
            onToggle: { },
            onEdit: { },
            onDelete: { },
            onAddToHouse: nil
        )

        ShoppingListItemRowView(
            item: ShoppingListItem(
                id: UUID(),
                shoppingListId: UUID(),
                name: "Ekmek",
                quantity: 1,
                unit: "adet",
                category: "Fırın",
                notes: nil,
                isPurchased: true,
                isAddedToHouse: false,
                addedToHouseAt: nil,
                createdAt: Date(),
                updatedAt: Date()
            ),
            onToggle: { },
            onEdit: { },
            onDelete: { },
            onAddToHouse: { }
        )

        ShoppingListItemRowView(
            item: ShoppingListItem(
                id: UUID(),
                shoppingListId: UUID(),
                name: "Domates",
                quantity: 0.5,
                unit: "kg",
                category: "Sebze",
                notes: "Taze olanlardan seç",
                isPurchased: true,
                isAddedToHouse: true,
                addedToHouseAt: Date(),
                createdAt: Date(),
                updatedAt: Date()
            ),
            onToggle: { },
            onEdit: { },
            onDelete: { },
            onAddToHouse: nil
        )
    }
    .padding()
    .background(Color.cardBackground)
}
