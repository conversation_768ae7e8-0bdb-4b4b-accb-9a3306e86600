import SwiftUI

struct AddShoppingListView: View {
    @EnvironmentObject var shoppingListViewModel: ShoppingListViewModel
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss

    @State private var name = ""
    @State private var description = ""
    @State private var isLoading = false

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 24) {
                            // Form Content
                            VStack(spacing: 20) {
                                // Form Fields
                                VStack(spacing: 16) {
                                    // List Name Field
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("Liste Adı")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                            .foregroundColor(.textSecondary)

                                        TextField("Örn: Haftalık Alışveriş", text: $name)
                                            .textFieldStyle(CustomTextFieldStyle())
                                    }

                                    // Description Field
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("A<PERSON>ıklama (İsteğe Bağlı)")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                            .foregroundColor(.textSecondary)

                                        ZStack(alignment: .topLeading) {
                                            RoundedRectangle(cornerRadius: 12)
                                                .fill(Color.cardBackground)
                                                .stroke(Color.borderColor, lineWidth: 1)
                                                .frame(height: 100)

                                            TextEditor(text: $description)
                                                .padding(16)
                                                .background(Color.clear)
                                                .font(.body)
                                                .foregroundColor(.textPrimary)
                                                .scrollContentBackground(.hidden)

                                            if description.isEmpty {
                                                Text("Liste hakkında kısa bir açıklama...")
                                                    .font(.body)
                                                    .foregroundColor(.textSecondary)
                                                    .padding(.horizontal, 20)
                                                    .padding(.vertical, 24)
                                                    .allowsHitTesting(false)
                                            }
                                        }
                                    }
                                }

                                // Action Buttons
                                VStack(spacing: 12) {
                                    // Create Button
                                    Button(action: {
                                        saveShoppingList()
                                    }) {
                                        HStack(spacing: 8) {
                                            if isLoading {
                                                ProgressView()
                                                    .scaleEffect(0.8)
                                                    .tint(.white)
                                            } else {
                                                Image(systemName: "plus.circle.fill")
                                                    .font(.headline)
                                            }

                                            Text(isLoading ? "Oluşturuluyor..." : "Liste Oluştur")
                                                .font(.headline)
                                                .fontWeight(.semibold)
                                        }
                                        .foregroundColor(.white)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 16)
                                        .background(
                                            LinearGradient.tazeBitGradient
                                                .cornerRadius(12)
                                        )
                                    }
                                    .disabled(name.isEmpty || isLoading)
                                    .buttonStyle(PlainButtonStyle())

                                    // Cancel Button
                                    Button("İptal") {
                                        dismiss()
                                    }
                                    .font(.headline)
                                    .foregroundColor(.textSecondary)
                                    .padding(.vertical, 8)
                                }
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    Text("Yeni Alışveriş Listesi")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    // Save Button
                    Button(action: {
                        saveShoppingList()
                    }) {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                                .frame(width: 32, height: 32)
                        } else {
                            Image(systemName: "checkmark")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(Color.white.opacity(0.2))
                                )
                        }
                    }
                    .disabled(name.isEmpty || isLoading)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }



    // MARK: - Actions
    private func saveShoppingList() {
        guard let household = householdViewModel.selectedHousehold else { return }

        isLoading = true

        Task {
            await shoppingListViewModel.createShoppingList(
                name: name,
                description: description.isEmpty ? nil : description,
                householdId: household.id
            )

            isLoading = false

            if !shoppingListViewModel.showingAddList {
                dismiss()
            }
        }
    }
}



#Preview {
    AddShoppingListView()
        .environmentObject(ShoppingListViewModel())
        .environmentObject(HouseholdViewModel())
}
