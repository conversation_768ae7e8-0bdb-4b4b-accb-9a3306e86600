//
//  EditHouseholdView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct EditHouseholdView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isNameFieldFocused: Bool

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 24) {
                            // Current Household Info
                            if let household = householdViewModel.selectedHousehold {
                                currentHouseholdCard(household)
                            }

                            // Edit Form
                            editForm

                            // Action Buttons
                            actionButtons
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
        .onAppear {
            // Focus on name field when view appears
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isNameFieldFocused = true
            }
        }
    }

    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    Text("Evi Düzenle")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    // Update Button
                    Button(action: {
                        Task {
                            await householdViewModel.updateHousehold()
                            if householdViewModel.errorMessage.isEmpty {
                                dismiss()
                            }
                        }
                    }) {
                        if householdViewModel.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                                .frame(width: 32, height: 32)
                        } else {
                            Image(systemName: "checkmark")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(Color.white.opacity(0.2))
                                )
                        }
                    }
                    .disabled(householdViewModel.isLoading || householdViewModel.editHouseholdName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    private func currentHouseholdCard(_ household: Household) -> some View {
        VStack(spacing: 16) {
            HStack {
                // House Icon
                ZStack {
                    Circle()
                        .fill(Color.tazeBitPrimary.opacity(0.1))
                        .frame(width: 60, height: 60)

                    Image(systemName: "house.fill")
                        .font(.title2)
                        .foregroundColor(.tazeBitPrimary)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text("Mevcut Bilgiler")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                        .textCase(.uppercase)

                    Text(household.name)
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)

                    if let description = household.description, !description.isEmpty {
                        Text(description)
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                    } else {
                        Text("Açıklama yok")
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                            .italic()
                    }
                }

                Spacer()

                // Edit Icon
                Image(systemName: "pencil.circle.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)
            }
            .padding(20)
            .background(Color.cardBackground)
            .cornerRadius(16)
            .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
        }
    }

    private var editForm: some View {
        VStack(spacing: 20) {
            // Section Header
            HStack {
                Text("Yeni Bilgiler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 16) {
                // Household Name Field
                VStack(alignment: .leading, spacing: 8) {
                    Text("Ev Adı")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    TextField("Ev adını girin", text: $householdViewModel.editHouseholdName)
                        .textFieldStyle(CustomTextFieldStyle())
                        .focused($isNameFieldFocused)
                        .submitLabel(.next)
                }

                // Household Description Field
                VStack(alignment: .leading, spacing: 8) {
                    Text("Açıklama (İsteğe bağlı)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    TextField("Ev açıklamasını girin", text: $householdViewModel.editHouseholdDescription, axis: .vertical)
                        .textFieldStyle(CustomTextFieldStyle())
                        .lineLimit(3...6)
                        .submitLabel(.done)
                }
            }
            .padding(20)
            .background(Color.cardBackground)
            .cornerRadius(16)
            .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
        }
    }

    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Update Button
            Button(action: {
                Task {
                    await householdViewModel.updateHousehold()
                    if householdViewModel.errorMessage.isEmpty {
                        dismiss()
                    }
                }
            }) {
                HStack {
                    if householdViewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text(householdViewModel.isLoading ? "Güncelleniyor..." : "Evi Güncelle")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.tazeBitPrimary,
                                    Color.tazeBitPrimary.opacity(0.8)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
            }
            .disabled(householdViewModel.isLoading || householdViewModel.editHouseholdName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)

            // Cancel Button
            Button(action: {
                dismiss()
            }) {
                Text("İptal")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
            .disabled(householdViewModel.isLoading)

            // Error Message
            if !householdViewModel.errorMessage.isEmpty {
                Text(householdViewModel.errorMessage)
                    .font(.subheadline)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding(.top, 8)
            }
        }
        .padding(.bottom, 20)
    }
}

#Preview {
    EditHouseholdView()
        .environmentObject(HouseholdViewModel())
}
