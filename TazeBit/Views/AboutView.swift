import SwiftUI

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header Section
                    headerSection

                    // App Info Section
                    appInfoSection

                    // Features Section
                    featuresSection

                    // Developer Section
                    developerSection

                    // Version Section
                    versionSection
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
            .background(Color.tazeBitBackground.ignoresSafeArea())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Kapat") {
                        dismiss()
                    }
                    .foregroundColor(.tazeBitPrimary)
                }
            }
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // App Icon
            RoundedRectangle(cornerRadius: 20)
                .fill(LinearGradient.tazeBitGradient)
                .frame(width: 80, height: 80)
                .overlay(
                    Image(systemName: "leaf.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.white)
                )

            VStack(spacing: 8) {
                Text("TazeBit")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)

                Text("Gıda Son Kullanma Tarihi Takip Uygulaması")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(24)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - App Info Section
    private var appInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Uygulama Hakkında")
                .font(.headline)
                .foregroundColor(.textPrimary)

            VStack(alignment: .leading, spacing: 12) {
                Text("TazeBit, gıda israfını önlemek ve sağlıklı beslenmeyi desteklemek amacıyla geliştirilmiş modern bir uygulamadır.")
                    .font(.body)
                    .foregroundColor(.textPrimary)

                Text("Evinizdeki gıdaların son kullanma tarihlerini takip ederek, zamanında tüketmenizi sağlar ve israfı minimize eder.")
                    .font(.body)
                    .foregroundColor(.textPrimary)

                Text("Aile üyeleriyle birlikte kullanabileceğiniz ev yönetimi sistemi sayesinde herkes gıda durumundan haberdar olur.")
                    .font(.body)
                    .foregroundColor(.textPrimary)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Features Section
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Özellikler")
                .font(.headline)
                .foregroundColor(.textPrimary)

            LazyVStack(spacing: 12) {
                FeatureRow(
                    icon: "calendar.badge.clock",
                    title: "Son Kullanma Tarihi Takibi",
                    description: "Ürünlerinizin son kullanma tarihlerini kolayca takip edin"
                )

                FeatureRow(
                    icon: "bell.badge",
                    title: "Akıllı Bildirimler",
                    description: "Son kullanma tarihi yaklaşan ürünler için otomatik hatırlatmalar"
                )

                FeatureRow(
                    icon: "house.fill",
                    title: "Ev Yönetimi",
                    description: "Aile üyeleriyle birlikte ev gıdalarını yönetin"
                )

                FeatureRow(
                    icon: "chart.bar.fill",
                    title: "Tüketim Takibi",
                    description: "Gıda tüketim alışkanlıklarınızı analiz edin"
                )

                FeatureRow(
                    icon: "doc.text.fill",
                    title: "Detaylı Raporlar",
                    description: "İsraf ve tüketim raporlarıyla bilinçli kararlar alın"
                )

                FeatureRow(
                    icon: "location.fill",
                    title: "Depolama Yönetimi",
                    description: "Ürünleri depolama yerlerine göre organize edin"
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Developer Section
    private var developerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Geliştirici")
                .font(.headline)
                .foregroundColor(.textPrimary)

            VStack(spacing: 12) {
                HStack(spacing: 16) {
                    Image(systemName: "person.circle.fill")
                        .font(.title)
                        .foregroundColor(.tazeBitPrimary)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("DijiKit")
                            .font(.headline)
                            .foregroundColor(.textPrimary)

                        Text("Mobil Uygulama Geliştirme")
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                    }

                    Spacer()
                }

                Divider()

                VStack(alignment: .leading, spacing: 8) {
                    Text("Misyon")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)

                    Text("Teknoloji ile günlük hayatı kolaylaştıran, sürdürülebilir ve kullanıcı dostu uygulamalar geliştirmek.")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Version Section
    private var versionSection: some View {
        VStack(spacing: 12) {
            Text("Sürüm Bilgileri")
                .font(.headline)
                .foregroundColor(.textPrimary)

            VStack(spacing: 8) {
                VersionInfoRow(title: "Sürüm", value: "1.0.0")
                VersionInfoRow(title: "Build", value: "2025.1")
                VersionInfoRow(title: "Platform", value: "iOS 18.4+")
                VersionInfoRow(title: "Çıkış Tarihi", value: "Mayıs 2025")
            }
        }
        .frame(maxWidth: .infinity)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
}

// MARK: - Supporting Views

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.tazeBitPrimary)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }

            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct VersionInfoRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.textSecondary)

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.textPrimary)
        }
        .padding(.vertical, 2)
    }
}

#Preview {
    AboutView()
}
