//
//  MainTabView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct MainTabView: View {
    @StateObject private var householdViewModel = HouseholdViewModel()
    @StateObject private var productViewModel = ProductViewModel()
    @StateObject private var authViewModel = AuthViewModel()
    @StateObject private var consumptionViewModel = ConsumptionViewModel()
    @StateObject private var shoppingListViewModel = ShoppingListViewModel()
    @StateObject private var notificationManager = NotificationManager.shared
    @State private var selectedTab = 0

    private let tabItems = [
        TabBarItem(icon: "house.fill", title: "<PERSON> Sayfa", tag: 0),
        TabBarItem(icon: "list.bullet", title: "<PERSON><PERSON><PERSON><PERSON><PERSON>", tag: 1),
        TabBarItem(icon: "cart.fill", title: "Alışveriş", tag: 2),
        TabBarItem(icon: "chart.bar.fill", title: "<PERSON><PERSON><PERSON>", tag: 3),
        TabBarItem(icon: "person.3.fill", title: "<PERSON>v<PERSON>", tag: 4),
        TabBarItem(icon: "person.circle.fill", title: "Profil", tag: 5)
    ]

    var body: some View {
        ZStack {
            // Background
            Color.tazeBitBackground
                .ignoresSafeArea()

            // Content
            VStack(spacing: 0) {
                // Main content area
                Group {
                    switch selectedTab {
                    case 0:
                        HomeView(onTabChange: { tabIndex in
                            selectedTab = tabIndex
                        })
                            .environmentObject(householdViewModel)
                            .environmentObject(productViewModel)
                            .environmentObject(authViewModel)
                            .environmentObject(consumptionViewModel)
                            .environmentObject(notificationManager)
                            .transition(.asymmetric(
                                insertion: .move(edge: .trailing).combined(with: .opacity),
                                removal: .move(edge: .leading).combined(with: .opacity)
                            ))
                    case 1:
                        ProductListView()
                            .environmentObject(householdViewModel)
                            .environmentObject(productViewModel)
                            .environmentObject(consumptionViewModel)
                            .transition(.asymmetric(
                                insertion: .move(edge: .trailing).combined(with: .opacity),
                                removal: .move(edge: .leading).combined(with: .opacity)
                            ))
                    case 2:
                        ShoppingListsView()
                            .environmentObject(householdViewModel)
                            .environmentObject(productViewModel)
                            .environmentObject(shoppingListViewModel)
                            .transition(.asymmetric(
                                insertion: .move(edge: .trailing).combined(with: .opacity),
                                removal: .move(edge: .leading).combined(with: .opacity)
                            ))
                    case 3:
                        ReportsView()
                            .environmentObject(householdViewModel)
                            .environmentObject(productViewModel)
                            .environmentObject(consumptionViewModel)
                            .transition(.asymmetric(
                                insertion: .move(edge: .trailing).combined(with: .opacity),
                                removal: .move(edge: .leading).combined(with: .opacity)
                            ))
                    case 4:
                        HouseholdView()
                            .environmentObject(householdViewModel)
                            .transition(.asymmetric(
                                insertion: .move(edge: .trailing).combined(with: .opacity),
                                removal: .move(edge: .leading).combined(with: .opacity)
                            ))
                    case 5:
                        ProfileView()
                            .environmentObject(authViewModel)
                            .environmentObject(notificationManager)
                            .environmentObject(productViewModel)
                            .transition(.asymmetric(
                                insertion: .move(edge: .trailing).combined(with: .opacity),
                                removal: .move(edge: .leading).combined(with: .opacity)
                            ))
                    default:
                        HomeView(onTabChange: { tabIndex in
                            selectedTab = tabIndex
                        })
                            .environmentObject(householdViewModel)
                            .environmentObject(productViewModel)
                            .environmentObject(authViewModel)
                            .environmentObject(consumptionViewModel)
                            .environmentObject(notificationManager)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .animation(.easeInOut(duration: 0.3), value: selectedTab)

                // Custom Tab Bar
                ModernTabBar(selectedTab: $selectedTab, items: tabItems)
            }
        }
        .task {
            await householdViewModel.fetchHouseholds()
        }
        .onChange(of: householdViewModel.selectedHousehold) { _, household in
            if let household = household {
                Task {
                    await productViewModel.fetchProducts(for: household.id)
                    await consumptionViewModel.loadConsumptions(for: household.id)
                }
            }
        }
    }
}

#Preview {
    MainTabView()
}
