import SwiftUI

struct ShoppingListCardView: View {
    let shoppingList: ShoppingList
    let items: [ShoppingListItem]
    let onEdit: () -> Void
    let onDelete: () -> Void
    let onToggleCompleted: () -> Void

    @State private var showingMenu = false

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(shoppingList.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)
                        .lineLimit(2)

                    if let description = shoppingList.description, !description.isEmpty {
                        Text(description)
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                            .lineLimit(2)
                    }
                }

                Spacer()

                // Status Badge
                statusBadge

                // Menu <PERSON>ton
                <PERSON>ton(action: {
                    showingMenu = true
                }) {
                    Image(systemName: "ellipsis")
                        .font(.title3)
                        .foregroundColor(.textSecondary)
                        .frame(width: 32, height: 32)
                        .background(Color.cardBackground)
                        .cornerRadius(8)
                }
            }

            // Progress Section (if list has items)
            progressSection

            // Footer
            HStack {
                // Created date
                Text("Oluşturulma: \(formattedDate(shoppingList.createdAt))")
                    .font(.caption)
                    .foregroundColor(.textSecondary)

                Spacer()

                // Completion toggle
                Button(action: onToggleCompleted) {
                    HStack(spacing: 4) {
                        Image(systemName: shoppingList.isCompleted ? "checkmark.circle.fill" : "circle")
                            .font(.subheadline)
                            .foregroundColor(shoppingList.isCompleted ? .green : .textSecondary)

                        Text(shoppingList.isCompleted ? "Tamamlandı" : "Tamamla")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(shoppingList.isCompleted ? .green : .textSecondary)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
        .confirmationDialog("Liste İşlemleri", isPresented: $showingMenu) {
            Button("Düzenle") {
                onEdit()
            }

            Button("Sil", role: .destructive) {
                onDelete()
            }

            Button("İptal", role: .cancel) { }
        }
    }

    // MARK: - Status Badge
    private var statusBadge: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(statusColor)
                .frame(width: 8, height: 8)

            Text(statusText)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(statusColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(statusColor.opacity(0.1))
        .cornerRadius(12)
    }

    // MARK: - Progress Section
    private var progressSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("İlerleme")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.textSecondary)

                Spacer()

                Text("\(completedItemsCount)/\(totalItemsCount) ürün")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)
            }

            // Progress Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 6)
                        .cornerRadius(3)

                    Rectangle()
                        .fill(progressColor)
                        .frame(width: geometry.size.width * progressPercentage, height: 6)
                        .cornerRadius(3)
                        .animation(.easeInOut(duration: 0.3), value: progressPercentage)
                }
            }
            .frame(height: 6)
        }
    }

    // MARK: - Computed Properties
    private var statusColor: Color {
        if shoppingList.isCompleted {
            return .green
        } else {
            return .orange
        }
    }

    private var statusText: String {
        if shoppingList.isCompleted {
            return "Tamamlandı"
        } else {
            return "Devam Ediyor"
        }
    }

    private var progressColor: Color {
        if progressPercentage >= 1.0 {
            return .green
        } else if progressPercentage >= 0.5 {
            return .orange
        } else {
            return .tazeBitPrimary
        }
    }

    private var progressPercentage: Double {
        guard totalItemsCount > 0 else { return 0 }
        return Double(completedItemsCount) / Double(totalItemsCount)
    }

    private var totalItemsCount: Int {
        return items.count
    }

    private var completedItemsCount: Int {
        return items.filter { $0.isPurchased }.count
    }

    private func formattedDate(_ date: Date) -> String {
        return date.turkishShortString()
    }
}

#Preview {
    VStack(spacing: 16) {
        ShoppingListCardView(
            shoppingList: ShoppingList(
                id: UUID(),
                name: "Haftalık Alışveriş",
                description: "Bu hafta için gerekli olan tüm ürünler",
                householdId: UUID(),
                createdBy: UUID(),
                isCompleted: false,
                createdAt: Date(),
                updatedAt: Date()
            ),
            items: [
                ShoppingListItem(
                    id: UUID(),
                    shoppingListId: UUID(),
                    name: "Süt",
                    quantity: 2,
                    unit: "litre",
                    category: "Süt Ürünleri",
                    notes: nil,
                    isPurchased: true,
                    isAddedToHouse: false,
                    addedToHouseAt: nil,
                    createdAt: Date(),
                    updatedAt: Date()
                ),
                ShoppingListItem(
                    id: UUID(),
                    shoppingListId: UUID(),
                    name: "Ekmek",
                    quantity: 1,
                    unit: "adet",
                    category: "Fırın",
                    notes: nil,
                    isPurchased: false,
                    isAddedToHouse: false,
                    addedToHouseAt: nil,
                    createdAt: Date(),
                    updatedAt: Date()
                ),
                ShoppingListItem(
                    id: UUID(),
                    shoppingListId: UUID(),
                    name: "Domates",
                    quantity: 0.5,
                    unit: "kg",
                    category: "Sebze",
                    notes: nil,
                    isPurchased: false,
                    isAddedToHouse: false,
                    addedToHouseAt: nil,
                    createdAt: Date(),
                    updatedAt: Date()
                )
            ],
            onEdit: { },
            onDelete: { },
            onToggleCompleted: { }
        )

        ShoppingListCardView(
            shoppingList: ShoppingList(
                id: UUID(),
                name: "Acil İhtiyaçlar",
                description: nil,
                householdId: UUID(),
                createdBy: UUID(),
                isCompleted: true,
                createdAt: Date().addingTimeInterval(-86400),
                updatedAt: Date()
            ),
            items: [
                ShoppingListItem(
                    id: UUID(),
                    shoppingListId: UUID(),
                    name: "Şeker",
                    quantity: 1,
                    unit: "kg",
                    category: "Temel Gıda",
                    notes: nil,
                    isPurchased: true,
                    isAddedToHouse: true,
                    addedToHouseAt: Date(),
                    createdAt: Date(),
                    updatedAt: Date()
                ),
                ShoppingListItem(
                    id: UUID(),
                    shoppingListId: UUID(),
                    name: "Tuz",
                    quantity: 1,
                    unit: "paket",
                    category: "Temel Gıda",
                    notes: nil,
                    isPurchased: true,
                    isAddedToHouse: false,
                    addedToHouseAt: nil,
                    createdAt: Date(),
                    updatedAt: Date()
                )
            ],
            onEdit: { },
            onDelete: { },
            onToggleCompleted: { }
        )
    }
    .padding()
    .background(Color.cardBackground)
}
