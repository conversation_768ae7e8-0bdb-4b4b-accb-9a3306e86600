//
//  HomeView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct HomeView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var notificationManager: NotificationManager
    @StateObject private var recipeViewModel = RecipeViewModel()
    @State private var showingProfile = false
    @State private var showingHouseholdSelection = false
    @State private var showingNotificationSettings = false
    @State private var showingPhotoDetection = false
    @State private var selectedTab = 1 // Products tab

    // Callback to switch tabs
    var onTabChange: ((Int) -> Void)?

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 0) {
                        // Header with gradient
                        headerSection

                        // Content
                        VStack(spacing: 24) {
                            // Recipe Suggestion Card
                            recipeSuggestionSection

                            // Quick Stats Cards
                            quickStatsSection

                            // Expiring Soon Section
                            expiringSoonSection

                            // Recent Products
                            recentProductsSection
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
                .refreshable {
                    await refreshData()
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
            .sheet(isPresented: $productViewModel.showingAddProduct) {
                AddProductView()
                    .environmentObject(householdViewModel)
                    .environmentObject(productViewModel)
            }
            .sheet(isPresented: $showingPhotoDetection) {
                if let household = householdViewModel.selectedHousehold {
                    PhotoDetectionFlow(householdId: household.id)
                        .environmentObject(productViewModel)
                }
            }
            .sheet(isPresented: $showingProfile) {
                ProfileView()
                    .environmentObject(authViewModel)
            }
            .sheet(isPresented: $showingHouseholdSelection) {
                HouseholdSelectionView(onDismiss: {
                    showingHouseholdSelection = false
                })
                .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $showingNotificationSettings) {
                NotificationSettingsView()
                    .environmentObject(notificationManager)
                    .environmentObject(productViewModel)
            }
            .onAppear {
                Task {
                    await refreshData()
                    await requestNotificationPermissionIfNeeded()
                }
            }
            .onChange(of: productViewModel.products) {
                // Products array changed, UI will automatically update
            }
            .onChange(of: householdViewModel.selectedHousehold) {
                Task {
                    await refreshData()
                }
            }
        }
    }

    private var headerSection: some View {
        ZStack {
            // Primary Color Background
            Color.tazeBitPrimary
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .clipShape(
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 30,
                        bottomTrailingRadius: 30,
                        topTrailingRadius: 0
                    )
                )
                .ignoresSafeArea(.all)

            VStack(spacing: 0) {
                // Top Section
                HStack {
                    // Left Action Button
                    Button(action: {
                        showingHouseholdSelection = true
                    }) {
                        Image(systemName: "house.circle.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .frame(width: 44, height: 44)
                    }

                    Spacer()

                    // Center Title
                    VStack(spacing: 4) {
                        Text("Merhaba! 👋")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        if let household = householdViewModel.selectedHousehold {
                            Text(household.name)
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.9))
                        } else {
                            Text("Ev seçin")
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.9))
                        }
                    }

                    Spacer()

                    // Right Action Button
                    Button(action: {
                        showingProfile = true
                    }) {
                        Image(systemName: "person.circle.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .frame(width: 44, height: 44)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)
                .padding(.bottom, 30)

                // Quick Action Buttons
                HStack(spacing: 16) {
                    QuickActionButton(
                        icon: "plus.circle.fill",
                        title: "Ürün Ekle",
                        action: {
                            productViewModel.showAddProduct()
                        }
                    )

                    QuickActionButton(
                        icon: "camera.circle.fill",
                        title: "AI ile Ekle",
                        action: {
                            showingPhotoDetection = true
                        }
                    )

                    QuickActionButton(
                        icon: notificationManager.isAuthorized ? "bell.circle.fill" : "bell.slash.circle.fill",
                        title: "Bildirimler",
                        action: {
                            showingNotificationSettings = true
                        }
                    )
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .frame(height: 250)
    }

    private var recipeSuggestionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("AI Yemek Önerileri")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.textPrimary)

            RecipeSuggestionCard(
                products: productViewModel.products,
                recipeViewModel: recipeViewModel,
                householdId: householdViewModel.selectedHousehold?.id
            )
        }
    }

    private var quickStatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Ürün Durumu")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.textPrimary)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ModernStatCard(
                    title: "Süresi Geçmiş",
                    count: productViewModel.expiredProducts.count,
                    color: .statusExpired,
                    icon: "exclamationmark.triangle.fill"
                )

                ModernStatCard(
                    title: "Yakında Bitecek",
                    count: productViewModel.expiringSoonProducts.count,
                    color: .statusExpiringSoon,
                    icon: "clock.fill"
                )

                ModernStatCard(
                    title: "Bu Hafta",
                    count: productViewModel.expiringThisWeekProducts.count,
                    color: .statusExpiringWeek,
                    icon: "calendar"
                )

                ModernStatCard(
                    title: "Taze",
                    count: productViewModel.freshProducts.count,
                    color: .statusFresh,
                    icon: "leaf.fill"
                )
            }
        }
    }

    private var expiringSoonSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Acil Durumlar")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                Button("Tümünü Gör") {
                    onTabChange?(1) // Switch to Products tab
                }
                .font(.caption)
                .foregroundColor(.tazeBitPrimary)
            }

            let urgentProducts = productViewModel.expiredProducts + productViewModel.expiringSoonProducts

            if urgentProducts.isEmpty {
                EmptyStateCard(
                    icon: "checkmark.circle.fill",
                    title: "Harika!",
                    subtitle: "Acil durum yok 🎉",
                    color: .statusFresh
                )
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(urgentProducts.prefix(3)) { product in
                        ModernProductCard(product: product)
                    }

                    if urgentProducts.count > 3 {
                        Button("ve \(urgentProducts.count - 3) ürün daha...") {
                            onTabChange?(1) // Switch to Products tab
                        }
                        .font(.caption)
                        .foregroundColor(.tazeBitSecondary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                    }
                }
            }
        }
    }

    private var recentProductsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Son Eklenenler")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                Button("Tümünü Gör") {
                    onTabChange?(1) // Switch to Products tab
                }
                .font(.caption)
                .foregroundColor(.tazeBitPrimary)
            }

            let recentProducts = Array(productViewModel.products.suffix(3).reversed())

            if recentProducts.isEmpty {
                EmptyStateCard(
                    icon: "plus.circle.fill",
                    title: "Henüz ürün yok",
                    subtitle: "İlk ürününüzü ekleyin",
                    color: .tazeBitSecondary
                )
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(recentProducts) { product in
                        ModernProductCard(product: product)
                    }
                }
            }
        }
    }

    // MARK: - Helper Methods
    private func refreshData() async {
        await householdViewModel.fetchHouseholds()
        if let household = householdViewModel.selectedHousehold {
            await productViewModel.fetchProducts(for: household.id)
        }
    }

    private func requestNotificationPermissionIfNeeded() async {
        if !notificationManager.isAuthorized {
            await productViewModel.requestNotificationPermission()
        }
    }
}

// MARK: - Supporting Views

struct QuickActionButton: View {
    let icon: String
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.white)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.white.opacity(0.2))
            .cornerRadius(12)
        }
    }
}

struct ModernStatCard: View {
    let title: String
    let count: Int
    let color: Color
    let icon: String

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Spacer()

                Text("\(count)")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)
            }

            Text(title)
                .font(.caption)
                .foregroundColor(.textSecondary)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
}

struct ModernProductCard: View {
    let product: Product

    var body: some View {
        HStack(spacing: 12) {
            // Category Icon
            Text(categoryIcon)
                .font(.title2)
                .frame(width: 40, height: 40)
                .background(Color.statusColor(for: product.expiryStatus).opacity(0.1))
                .cornerRadius(10)

            // Product Info
            VStack(alignment: .leading, spacing: 4) {
                Text(product.name)
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                    .lineLimit(1)

                Text(expiryText)
                    .font(.caption)
                    .foregroundColor(Color.statusColor(for: product.expiryStatus))
            }

            Spacer()

            // Status Badge
            VStack(alignment: .trailing, spacing: 4) {
                Text("×\(product.currentQuantity)")
                    .font(.caption2)
                    .foregroundColor(.textSecondary)

                Text(product.expiryStatus.displayName)
                    .font(.caption2)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.statusColor(for: product.expiryStatus).opacity(0.2))
                    .foregroundColor(Color.statusColor(for: product.expiryStatus))
                    .cornerRadius(8)
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    private var categoryIcon: String {
        if let category = ProductCategory(rawValue: product.category ?? "other") {
            return category.icon
        }
        return "📦"
    }

    private var expiryText: String {
        let days = product.daysUntilExpiry

        if days < 0 {
            return "\(-days) gün önce süresi geçti"
        } else if days == 0 {
            return "Bugün süresi bitiyor"
        } else if days == 1 {
            return "Yarın süresi bitiyor"
        } else {
            return "\(days) gün kaldı"
        }
    }
}

struct EmptyStateCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 40))
                .foregroundColor(color)

            VStack(spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.textPrimary)

                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(24)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
}

#Preview {
    HomeView()
        .environmentObject(HouseholdViewModel())
        .environmentObject(ProductViewModel())
        .environmentObject(AuthViewModel())
}
