//
//  ConsumptionView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

struct ConsumptionView: View {
    let product: Product
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var consumptionViewModel: ConsumptionViewModel
    @EnvironmentObject var householdViewModel: HouseholdViewModel

    @State private var consumedAmount: Int = 0
    @State private var wastedAmount: Int = 0
    @State private var notes: String = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // Computed property for remaining quantity
    private var remainingQuantity: Int {
        product.currentQuantity
    }

    // Computed property for unit display name
    private var unitDisplayName: String {
        if let unit = product.unit, let productUnit = ProductUnit(rawValue: unit) {
            return productUnit.shortName
        } else {
            return "adet"
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header Section
                headerSection

                // Content
                ScrollView {
                    VStack(spacing: 24) {
                        // Product Info Card
                        productInfoCard

                        // Consumption Input Section
                        consumptionInputSection

                        // Action Buttons
                        actionButtons
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
            .background(Color.tazeBitBackground)
        }
        .alert("Uyarı", isPresented: $showingAlert) {
            Button("Tamam", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }

    private var headerSection: some View {
        ZStack {
            // Background
            Color.tazeBitPrimary

            HStack {
                // Close Button
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(Color.white.opacity(0.2))
                        )
                }

                Spacer()

                // Title
                Text("Tüketim İşlemi")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Spacer()

                // Save Button
                Button(action: {
                    saveConsumption()
                }) {
                    if consumptionViewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                            .frame(width: 32, height: 32)
                    } else {
                        Image(systemName: "checkmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }
                }
                .disabled(consumptionViewModel.isLoading || (consumedAmount == 0 && wastedAmount == 0))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .frame(height: 80)
    }

    private var productInfoCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.textPrimary)

                    Text("Toplam: \(product.originalQuantityWithUnit)")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("Kalan")
                        .font(.caption)
                        .foregroundColor(.textSecondary)

                    Text("\(remainingQuantity) \(unitDisplayName)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.tazeBitPrimary)
                }
            }

            // Expiry Status
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.textSecondary)

                Text("Son kullanma: \(product.expiryDate.turkishShortString())")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)

                Spacer()

                Text(product.expiryStatus.displayName)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(product.expiryStatus.colorName).opacity(0.2))
                    .foregroundColor(Color(product.expiryStatus.colorName))
                    .cornerRadius(8)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    private var consumptionInputSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("Tüketim Miktarları")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.textPrimary)

            VStack(spacing: 16) {
                // Consumed Amount
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text("Tüketilen Miktar")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.textPrimary)
                    }

                    HStack {
                        TextField("0", value: $consumedAmount, format: .number)
                            .keyboardType(.numberPad)
                            .padding()
                            .background(Color.cardBackground)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )

                        Text(unitDisplayName)
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                            .frame(width: 50, alignment: .leading)
                    }
                }

                // Wasted Amount
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "trash.circle.fill")
                            .foregroundColor(.red)
                        Text("İsraf Edilen Miktar")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.textPrimary)
                    }

                    HStack {
                        TextField("0", value: $wastedAmount, format: .number)
                            .keyboardType(.numberPad)
                            .padding()
                            .background(Color.cardBackground)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )

                        Text(unitDisplayName)
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                            .frame(width: 50, alignment: .leading)
                    }
                }

                // Notes Section
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "note.text")
                            .foregroundColor(.textSecondary)
                        Text("Not (İsteğe bağlı)")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.textPrimary)
                    }

                    TextField("Tüketim hakkında not ekleyin...", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                        .padding()
                        .background(Color.cardBackground)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }

                // Total Check
                if consumedAmount + wastedAmount > remainingQuantity {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                        Text("Toplam miktar kalan miktardan fazla olamaz!")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Save Button
            Button(action: {
                saveConsumption()
            }) {
                HStack(spacing: 8) {
                    if productViewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.headline)
                    }

                    Text(productViewModel.isLoading ? "Kaydediliyor..." : "Tüketimi Kaydet")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    LinearGradient.tazeBitGradient
                        .cornerRadius(12)
                )
            }
            .disabled(productViewModel.isLoading || (consumedAmount == 0 && wastedAmount == 0) || consumedAmount + wastedAmount > remainingQuantity)
            .buttonStyle(PlainButtonStyle())

            // Cancel Button
            Button("İptal") {
                dismiss()
            }
            .font(.headline)
            .foregroundColor(.textSecondary)
            .padding(.vertical, 8)
        }
    }

    private func saveConsumption() {
        guard consumedAmount + wastedAmount <= remainingQuantity else {
            alertMessage = "Toplam miktar kalan miktardan fazla olamaz!"
            showingAlert = true
            return
        }

        guard consumedAmount > 0 || wastedAmount > 0 else {
            alertMessage = "En az bir miktar girmelisiniz!"
            showingAlert = true
            return
        }

        guard let household = householdViewModel.selectedHousehold else {
            alertMessage = "Ev seçilmedi!"
            showingAlert = true
            return
        }

        Task {
            let success = await consumptionViewModel.addConsumption(
                productId: product.id,
                householdId: household.id,
                consumedQuantity: consumedAmount,
                wastedQuantity: wastedAmount,
                notes: notes.isEmpty ? nil : notes
            )

            if success {
                // Consumption was successful, just dismiss
                // ProductDetailView will handle refresh via onDisappear
                dismiss()
            } else {
                alertMessage = consumptionViewModel.errorMessage
                showingAlert = true
            }
        }
    }
}

extension DateFormatter {
    static let shortDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "tr_TR")
        return formatter
    }()
}

#Preview {
    ConsumptionView(product: Product(
        id: UUID(),
        householdId: UUID(),
        name: "Domates",
        category: "vegetables",
        storageLocation: "refrigerator",
        expiryDate: Date().addingTimeInterval(86400 * 3),
        purchaseDate: Date(),
        originalQuantity: 3,
        currentQuantity: 3,
        unit: "kg",
        notes: nil,
        caloriesPer100g: nil,
        addedBy: UUID(),
        shoppingListItemId: nil,
        createdAt: Date(),
        updatedAt: Date()
    ))
    .environmentObject(ProductViewModel())
    .environmentObject(ConsumptionViewModel())
    .environmentObject(HouseholdViewModel())
}
