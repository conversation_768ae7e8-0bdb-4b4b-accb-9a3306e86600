import SwiftUI

struct ProductDetailView: View {
    let product: Product
    @StateObject private var consumptionViewModel = ConsumptionViewModel()
    @StateObject private var householdViewModel = HouseholdViewModel()
    @StateObject private var productViewModel = ProductViewModel()
    @Environment(\.dismiss) private var dismiss
    @State private var showingConsumptionSheet = false
    @State private var showingEditSheet = false

    @State private var currentProduct: Product

    init(product: Product) {
        self.product = product
        self._currentProduct = State(initialValue: product)
    }

    // Computed property for consumed quantity with unit
    private var consumedQuantityWithUnit: String {
        let consumedQuantity = currentProduct.originalQuantity - currentProduct.currentQuantity
        if let unit = currentProduct.unit, let productUnit = ProductUnit(rawValue: unit) {
            return "\(consumedQuantity) \(productUnit.shortName)"
        } else {
            return "\(consumedQuantity) adet"
        }
    }

    var body: some View {
        ZStack {
            // Background
            Color.tazeBitBackground
                .ignoresSafeArea()

            ScrollView {
                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content sections
                    VStack(spacing: 20) {
                        // Product Header
                        productHeaderSection

                        // Quantity Status
                        quantityStatusSection

                        // Consumption History
                        consumptionHistorySection
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
        }
        .navigationBarHidden(true)
        .ignoresSafeArea(.all, edges: .top)
        .sheet(isPresented: $showingConsumptionSheet) {
            ConsumptionView(product: currentProduct)
                .onDisappear {
                    Task {
                        await refreshProduct()
                    }
                }
        }
        .sheet(isPresented: $showingEditSheet) {
            NavigationView {
                AddProductView()
                    .environmentObject(productViewModel)
                    .environmentObject(householdViewModel)
            }
            .onAppear {
                // Set household first
                Task {
                    await householdViewModel.fetchHouseholds()
                    if let household = householdViewModel.households.first(where: { $0.id == currentProduct.householdId }) {
                        householdViewModel.selectedHousehold = household
                    }

                    // Then set product data
                    DispatchQueue.main.async {
                        productViewModel.editProduct(currentProduct)
                        print("DEBUG: Product name set to: '\(productViewModel.productName)'")
                        print("DEBUG: Selected household: \(householdViewModel.selectedHousehold?.name ?? "nil")")
                    }
                }
            }
            .onDisappear {
                Task {
                    await refreshProduct()
                }
            }
        }

        .alert("Hata", isPresented: .constant(!productViewModel.errorMessage.isEmpty)) {
            Button("Tamam") {
                productViewModel.errorMessage = ""
            }
        } message: {
            Text(productViewModel.errorMessage)
        }
        .task {
            await loadData()
            await refreshProduct()
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    VStack(spacing: 4) {
                        Text("Ürün Detayı")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text(currentProduct.name)
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                    }

                    Spacer()

                    // Add Consumption Button (sadece aktif ürünler için)
                    if currentProduct.currentQuantity > 0 {
                        Button(action: {
                            showingConsumptionSheet = true
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(Color.white.opacity(0.2))
                                )
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Product Header Section
    private var productHeaderSection: some View {
        VStack(spacing: 16) {
            // Product Icon and Name
            HStack {
                // Category Icon
                Text(categoryIcon)
                    .font(.system(size: 40))
                    .frame(width: 60, height: 60)
                    .background(Color.tazeBitPrimary.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 12))

                VStack(alignment: .leading, spacing: 4) {
                    Text(currentProduct.name)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.textPrimary)

                    if let category = currentProduct.category {
                        Text(ProductCategory(rawValue: category)?.displayName ?? category)
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                    }

                    if let storageLocation = currentProduct.storageLocation {
                        HStack {
                            Text(storageLocationIcon)
                                .font(.caption)
                            Text(StorageLocation(rawValue: storageLocation)?.displayName ?? storageLocation)
                                .font(.caption)
                                .foregroundColor(.textSecondary)
                        }
                    }
                }

                Spacer()
            }

            // Action Buttons (sadece aktif ürünler için)
            if currentProduct.currentQuantity > 0 {
                HStack(spacing: 12) {
                    // Edit Button
                    Button(action: {
                        showingEditSheet = true
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "pencil")
                                .font(.system(size: 14, weight: .medium))
                            Text("Düzenle")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, minHeight: 44)
                        .background(Color.tazeBitPrimary)
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                    }

                    // Consumption Button
                    Button(action: {
                        showingConsumptionSheet = true
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "plus.circle")
                                .font(.system(size: 14, weight: .medium))
                            Text("Tüketim Ekle")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, minHeight: 44)
                        .background(Color.orange)
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                }
            } else {
                // Tüketilen ürün için bilgi mesajı
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text("Bu ürün tamamen tüketilmiş")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.textPrimary)
                        Spacer()
                    }

                    Text("Tüketim geçmişini aşağıdan inceleyebilirsiniz")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            }

            // Expiry Date
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.textSecondary)
                Text("Son Kullanma:")
                    .foregroundColor(.textSecondary)
                Spacer()
                Text(currentProduct.expiryDate.turkishMediumString())
                    .fontWeight(.medium)
                    .foregroundColor(expiryColor)
            }
            .font(.subheadline)

            // Purchase Date
            if let purchaseDate = currentProduct.purchaseDate {
                HStack {
                    Image(systemName: "cart")
                        .foregroundColor(.textSecondary)
                    Text("Satın Alma:")
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text(purchaseDate.turkishMediumString())
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)
                }
                .font(.subheadline)
            }

            // Notes
            if let notes = currentProduct.notes, !notes.isEmpty {
                HStack(alignment: .top) {
                    Image(systemName: "note.text")
                        .foregroundColor(.textSecondary)
                    Text("Not:")
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text(notes)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)
                        .multilineTextAlignment(.trailing)
                }
                .font(.subheadline)
            }
        }
        .padding()
        .background(Color.cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }

    // MARK: - Quantity Status Section
    private var quantityStatusSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Miktar Durumu")
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                Spacer()
            }

            HStack(spacing: 20) {
                // Original Quantity
                VStack {
                    Text("Orijinal")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                    Text(currentProduct.originalQuantityWithUnit)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.textPrimary)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))

                // Current Quantity
                VStack {
                    Text("Kalan")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                    Text(currentProduct.quantityWithUnit)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(currentProduct.currentQuantity > 0 ? .tazeBitPrimary : .red)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.tazeBitPrimary.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))

                // Consumed Quantity
                VStack {
                    Text("Tüketilen")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                    Text(consumedQuantityWithUnit)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.green.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }

            // Progress Bar
            ProgressView(value: Double(currentProduct.originalQuantity - currentProduct.currentQuantity), total: Double(currentProduct.originalQuantity))
                .progressViewStyle(LinearProgressViewStyle(tint: .tazeBitPrimary))
                .scaleEffect(x: 1, y: 2, anchor: .center)
        }
        .padding()
        .background(Color.cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }

    // MARK: - Consumption History Section
    private var consumptionHistorySection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Tüketim Geçmişi")
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                Spacer()
                Text("\(consumptionViewModel.consumptions.count) kayıt")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }

            if consumptionViewModel.consumptions.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "clock.arrow.circlepath")
                        .font(.system(size: 40))
                        .foregroundColor(.textSecondary)

                    Text("Henüz tüketim kaydı yok")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)

                    Text("İlk tüketimi kaydetmek için + butonuna dokunun")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(consumptionViewModel.consumptions.sorted(by: { $0.consumedAt > $1.consumedAt })) { consumption in
                        ConsumptionRowView(consumption: consumption)
                    }
                }
            }
        }
        .padding()
        .background(Color.cardBackground)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }

    // MARK: - Helper Properties
    private var categoryIcon: String {
        guard let category = currentProduct.category,
              let productCategory = ProductCategory(rawValue: category) else {
            return "questionmark.circle"
        }
        return productCategory.icon
    }

    private var storageLocationIcon: String {
        guard let storageLocation = currentProduct.storageLocation,
              let location = StorageLocation(rawValue: storageLocation) else {
            return "location"
        }
        return location.icon
    }

    private var expiryColor: Color {
        let daysUntilExpiry = Calendar.current.dateComponents([.day], from: Date(), to: currentProduct.expiryDate).day ?? 0

        if daysUntilExpiry < 0 {
            return .red
        } else if daysUntilExpiry <= 3 {
            return .orange
        } else {
            return .textPrimary
        }
    }

    // MARK: - Helper Methods
    private func loadData() async {
        await consumptionViewModel.loadConsumptionsForProduct(currentProduct.id)
        await householdViewModel.fetchHouseholds()
    }

    private func refreshProduct() async {
        // First ensure we have households loaded
        await householdViewModel.fetchHouseholds()

        // Find the household for this product
        let household = householdViewModel.households.first(where: { $0.id == currentProduct.householdId })
        guard let household = household else {
            print("Error: Could not find household for product")
            return
        }

        // Set selected household if not already set
        if householdViewModel.selectedHousehold?.id != household.id {
            householdViewModel.selectedHousehold = household
        }

        do {
            // Fetch updated product from database
            let products = try await SupabaseService.shared.fetchProducts(for: household.id)
            if let updatedProduct = products.first(where: { $0.id == currentProduct.id }) {
                currentProduct = updatedProduct
            }

            // Refresh consumption history
            await consumptionViewModel.loadConsumptionsForProduct(currentProduct.id)
        } catch {
            print("Error refreshing product: \(error)")
        }
    }
}

// MARK: - Consumption Row View
struct ConsumptionRowView: View {
    let consumption: Consumption
    @State private var userProfile: UserProfile?

    var body: some View {
        HStack(spacing: 12) {
            // Date and Time
            VStack(alignment: .leading, spacing: 2) {
                Text(consumption.consumedAt.turkishShortString())
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Text(consumption.consumedAt, style: .time)
                    .font(.caption2)
                    .foregroundColor(.textSecondary)
            }
            .frame(width: 70, alignment: .leading)

            // Consumption Details
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    if consumption.consumedQuantity > 0 {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.green)
                            Text("\(consumption.consumedQuantity)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.green)
                        }
                    }

                    if consumption.wastedQuantity > 0 {
                        HStack(spacing: 4) {
                            Image(systemName: "trash.circle.fill")
                                .font(.caption)
                                .foregroundColor(.red)
                            Text("\(consumption.wastedQuantity)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.red)
                        }
                    }

                    Spacer()
                }

                if let notes = consumption.notes, !notes.isEmpty {
                    Text(notes)
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                        .lineLimit(2)
                }
            }

            Spacer()

            // User Info
            VStack(alignment: .trailing, spacing: 2) {
                if let userProfile = userProfile {
                    // User Avatar
                    Text(userProfile.initials)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(width: 20, height: 20)
                        .background(Color.tazeBitPrimary)
                        .clipShape(Circle())

                    Text(userProfile.displayName)
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                        .lineLimit(1)
                } else {
                    Image(systemName: "person.circle.fill")
                        .font(.caption)
                        .foregroundColor(.textSecondary)

                    Text("Yükleniyor...")
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .task {
            await loadUserProfile()
        }
    }

    private func loadUserProfile() async {
        do {
            userProfile = try await SupabaseService.shared.getUserProfile(userId: consumption.userId)
        } catch {
            print("Error loading user profile: \(error)")
        }
    }
}

// MARK: - Preview
#Preview {
    ProductDetailView(product: Product(
        id: UUID(),
        householdId: UUID(),
        name: "Domates",
        category: "vegetables",
        storageLocation: "refrigerator",
        expiryDate: Calendar.current.date(byAdding: .day, value: 5, to: Date())!,
        purchaseDate: Date(),
        originalQuantity: 3,
        currentQuantity: 1,
        unit: "kg",
        notes: "Organik domates, çok taze",
        addedBy: UUID(),
        shoppingListItemId: nil,
        createdAt: Date(),
        updatedAt: Date()
    ))
}
