//
//  ReportsView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 27.05.2025.
//

import SwiftUI

struct ReportsView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject private var consumptionViewModel: ConsumptionViewModel
    @State private var selectedTimeRange: TimeRange = .thisWeek

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 20) {
                            // Time Range Selector
                            timeRangeSelector

                            // Quick Stats Cards
                            quickStatsSection

                            // Charts Section
                            chartsSection

                            // Consumption Analysis
                            consumptionAnalysisSection

                            // Waste Analysis
                            wasteAnalysisSection
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        ZStack {
            // Primary Color Background
            Color.tazeBitPrimary
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .clipShape(
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 30,
                        bottomTrailingRadius: 30,
                        topTrailingRadius: 0
                    )
                )
                .ignoresSafeArea(.all)

            VStack(spacing: 0) {
                // Top Section
                VStack(spacing: 4) {
                    Text("Raporlar 📊")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    if let household = householdViewModel.selectedHousehold {
                        Text(household.name)
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                    } else {
                        Text("Ev seçin")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)
                .padding(.bottom, 40)
            }
        }
        .frame(height: 180)
    }

    // MARK: - Time Range Selector
    private var timeRangeSelector: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "calendar.circle.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Zaman Aralığı")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(TimeRange.allCases, id: \.self) { range in
                        TimeRangeButton(
                            timeRange: range,
                            isSelected: selectedTimeRange == range
                        ) {
                            selectedTimeRange = range
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Quick Stats Section
    private var quickStatsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Hızlı İstatistikler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                QuickStatCard(
                    title: "Toplam Ürün",
                    value: "\(productViewModel.products.count)",
                    icon: "cube.box.fill",
                    color: .blue
                )

                QuickStatCard(
                    title: "İsraf Edilen",
                    value: "\(wastedProductsCount)",
                    icon: "trash.fill",
                    color: .red
                )

                QuickStatCard(
                    title: "Yakında Bitecek",
                    value: "\(expiringSoonCount)",
                    icon: "clock.fill",
                    color: .orange
                )

                QuickStatCard(
                    title: "Taze Ürünler",
                    value: "\(freshProductsCount)",
                    icon: "checkmark.circle.fill",
                    color: .green
                )
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Charts Section
    private var chartsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "chart.pie.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Grafikler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 12) {
                // Category Distribution Chart
                CategoryDistributionChartView(products: getFilteredProducts())

                // Waste Analysis Chart
                WasteAnalysisChartView(
                    products: productViewModel.products,
                    consumptions: getFilteredConsumptions(consumptions: consumptionViewModel.consumptions)
                )
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Consumption Analysis Section
    private var consumptionAnalysisSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Tüketim Analizi")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 12) {
                // En Çok Tüketilen Kategoriler
                mostConsumedCategoriesCard

                // En Çok Tüketilen Ürünler
                mostConsumedProductsCard
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Waste Analysis Section
    private var wasteAnalysisSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "trash.fill")
                    .font(.title2)
                    .foregroundColor(.red)

                Text("İsraf Analizi")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 12) {
                // En Çok İsraf Edilen Kategoriler
                mostWastedCategoriesCard

                // En Çok İsraf Edilen Ürünler
                mostWastedProductsCard
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
}

// MARK: - Computed Properties
extension ReportsView {
    private var wastedProductsCount: Int {
        let filteredWastedConsumptions = getFilteredConsumptions(
            consumptions: consumptionViewModel.consumptions.filter { $0.wastedQuantity > 0 }
        )
        let wastedProductIds = Set(filteredWastedConsumptions.map { $0.productId })

        return productViewModel.products.filter { wastedProductIds.contains($0.id) }.count
    }

    private var expiringSoonCount: Int {
        productViewModel.products.filter { $0.expiryStatus == .expiringSoon }.count
    }

    private var freshProductsCount: Int {
        productViewModel.products.filter { $0.expiryStatus == .fresh }.count
    }

    // MARK: - Consumption Analysis Data
    private var mostConsumedCategories: [(category: String, quantity: Int)] {
        let filteredConsumptions = getFilteredConsumptions(consumptions: consumptionViewModel.consumptions.filter { $0.consumedQuantity > 0 })
        let categoryConsumptions = Dictionary(grouping: filteredConsumptions) { consumption in
            let product = productViewModel.products.first { $0.id == consumption.productId }
            return product?.category ?? "other"
        }

        return categoryConsumptions.map { category, consumptions in
            let totalQuantity = consumptions.reduce(0) { $0 + $1.consumedQuantity }
            let categoryName = ProductCategory(rawValue: category)?.displayName ?? "Diğer"
            return (category: categoryName, quantity: totalQuantity)
        }
        .sorted { $0.quantity > $1.quantity }
        .prefix(3)
        .map { $0 }
    }

    private var mostConsumedProducts: [(product: String, quantity: Int)] {
        let filteredConsumptions = getFilteredConsumptions(consumptions: consumptionViewModel.consumptions.filter { $0.consumedQuantity > 0 })
        let productConsumptions = Dictionary(grouping: filteredConsumptions) { consumption in
            productViewModel.products.first { $0.id == consumption.productId }?.name ?? "Bilinmeyen"
        }

        return productConsumptions.map { productName, consumptions in
            let totalQuantity = consumptions.reduce(0) { $0 + $1.consumedQuantity }
            return (product: productName, quantity: totalQuantity)
        }
        .sorted { $0.quantity > $1.quantity }
        .prefix(3)
        .map { $0 }
    }

    // MARK: - Waste Analysis Data
    private var mostWastedCategories: [(category: String, quantity: Int)] {
        let filteredConsumptions = getFilteredConsumptions(consumptions: consumptionViewModel.consumptions.filter { $0.wastedQuantity > 0 })
        let categoryWastes = Dictionary(grouping: filteredConsumptions) { consumption in
            let product = productViewModel.products.first { $0.id == consumption.productId }
            return product?.category ?? "other"
        }

        return categoryWastes.map { category, consumptions in
            let totalQuantity = consumptions.reduce(0) { $0 + $1.wastedQuantity }
            let categoryName = ProductCategory(rawValue: category)?.displayName ?? "Diğer"
            return (category: categoryName, quantity: totalQuantity)
        }
        .sorted { $0.quantity > $1.quantity }
        .prefix(3)
        .map { $0 }
    }

    private var mostWastedProducts: [(product: String, quantity: Int)] {
        let filteredConsumptions = getFilteredConsumptions(consumptions: consumptionViewModel.consumptions.filter { $0.wastedQuantity > 0 })
        let productWastes = Dictionary(grouping: filteredConsumptions) { consumption in
            productViewModel.products.first { $0.id == consumption.productId }?.name ?? "Bilinmeyen"
        }

        return productWastes.map { productName, consumptions in
            let totalQuantity = consumptions.reduce(0) { $0 + $1.wastedQuantity }
            return (product: productName, quantity: totalQuantity)
        }
        .sorted { $0.quantity > $1.quantity }
        .prefix(3)
        .map { $0 }
    }

    // MARK: - Helper Functions
    private func getFilteredConsumptions(consumptions: [Consumption]) -> [Consumption] {
        let calendar = Calendar.current
        let now = Date()

        return consumptions.filter { consumption in
            isDateInTimeRange(date: consumption.consumedAt, timeRange: selectedTimeRange, calendar: calendar, now: now)
        }
    }

    private func getFilteredProducts() -> [Product] {
        let calendar = Calendar.current
        let now = Date()

        return productViewModel.products.filter { product in
            let creationDate = product.purchaseDate ?? product.createdAt
            return isDateInTimeRange(date: creationDate, timeRange: selectedTimeRange, calendar: calendar, now: now)
        }
    }

    private func isDateInTimeRange(date: Date, timeRange: TimeRange, calendar: Calendar, now: Date) -> Bool {
        switch timeRange {
        case .thisWeek:
            // Bu hafta
            let weekInterval = calendar.dateInterval(of: .weekOfYear, for: now)
            return weekInterval?.contains(date) ?? false

        case .thisMonth:
            // Bu ay
            let monthInterval = calendar.dateInterval(of: .month, for: now)
            return monthInterval?.contains(date) ?? false

        case .lastMonth:
            // Geçen ay
            guard let lastMonth = calendar.date(byAdding: .month, value: -1, to: now) else { return false }
            let lastMonthInterval = calendar.dateInterval(of: .month, for: lastMonth)
            return lastMonthInterval?.contains(date) ?? false

        case .last3Months:
            // Son 3 ay
            guard let threeMonthsAgo = calendar.date(byAdding: .month, value: -3, to: now) else { return false }
            return date >= threeMonthsAgo && date <= now

        case .thisYear:
            // Bu yıl
            let yearInterval = calendar.dateInterval(of: .year, for: now)
            return yearInterval?.contains(date) ?? false
        }
    }

    // MARK: - Analysis Cards
    private var mostConsumedCategoriesCard: some View {
        VStack(spacing: 12) {
            HStack {
                Text("En Çok Tüketilen Kategoriler")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if mostConsumedCategories.isEmpty {
                Text("Henüz tüketim verisi yok")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
                    .frame(height: 60)
            } else {
                VStack(spacing: 8) {
                    ForEach(Array(mostConsumedCategories.enumerated()), id: \.offset) { index, item in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.textSecondary)
                                .frame(width: 20)

                            Text(item.category)
                                .font(.caption)
                                .foregroundColor(.textPrimary)

                            Spacer()

                            Text("\(item.quantity) adet")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.tazeBitPrimary)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }

    private var mostConsumedProductsCard: some View {
        VStack(spacing: 12) {
            HStack {
                Text("En Çok Tüketilen Ürünler")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if mostConsumedProducts.isEmpty {
                Text("Henüz tüketim verisi yok")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
                    .frame(height: 60)
            } else {
                VStack(spacing: 8) {
                    ForEach(Array(mostConsumedProducts.enumerated()), id: \.offset) { index, item in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.textSecondary)
                                .frame(width: 20)

                            Text(item.product)
                                .font(.caption)
                                .foregroundColor(.textPrimary)
                                .lineLimit(1)

                            Spacer()

                            Text("\(item.quantity) adet")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.tazeBitPrimary)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }

    private var mostWastedCategoriesCard: some View {
        VStack(spacing: 12) {
            HStack {
                Text("En Çok İsraf Edilen Kategoriler")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if mostWastedCategories.isEmpty {
                VStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.green)

                    Text("Hiç israf yok! 🎉")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                .frame(height: 60)
            } else {
                VStack(spacing: 8) {
                    ForEach(Array(mostWastedCategories.enumerated()), id: \.offset) { index, item in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.textSecondary)
                                .frame(width: 20)

                            Text(item.category)
                                .font(.caption)
                                .foregroundColor(.textPrimary)

                            Spacer()

                            Text("\(item.quantity) adet")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.red)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }

    private var mostWastedProductsCard: some View {
        VStack(spacing: 12) {
            HStack {
                Text("En Çok İsraf Edilen Ürünler")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            if mostWastedProducts.isEmpty {
                VStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.green)

                    Text("Hiç israf yok! 🎉")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                .frame(height: 60)
            } else {
                VStack(spacing: 8) {
                    ForEach(Array(mostWastedProducts.enumerated()), id: \.offset) { index, item in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.textSecondary)
                                .frame(width: 20)

                            Text(item.product)
                                .font(.caption)
                                .foregroundColor(.textPrimary)
                                .lineLimit(1)

                            Spacer()

                            Text("\(item.quantity) adet")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.red)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }
}

// MARK: - Supporting Enums
enum TimeRange: String, CaseIterable {
    case thisWeek = "Bu Hafta"
    case thisMonth = "Bu Ay"
    case lastMonth = "Geçen Ay"
    case last3Months = "Son 3 Ay"
    case thisYear = "Bu Yıl"

    var days: Int {
        switch self {
        case .thisWeek: return 7
        case .thisMonth: return 30
        case .lastMonth: return 30
        case .last3Months: return 90
        case .thisYear: return 365
        }
    }
}

enum ReportType: String, CaseIterable {
    case expiry = "Son Kullanma"
    case category = "Kategori"
    case waste = "İsraf"
    case shopping = "Alışveriş"

    var icon: String {
        switch self {
        case .expiry: return "calendar.badge.exclamationmark"
        case .category: return "square.grid.2x2"
        case .waste: return "trash.fill"
        case .shopping: return "cart.fill"
        }
    }
}

#Preview {
    ReportsView()
        .environmentObject(HouseholdViewModel())
        .environmentObject(ProductViewModel())
}
