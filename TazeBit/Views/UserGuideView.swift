import SwiftUI

struct UserGuideView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedSection = 0

    private let sections = GuideSection.allSections

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(spacing: 16) {
                    HStack {
                        Button("Kapat") {
                            dismiss()
                        }
                        .foregroundColor(.white.opacity(0.9))

                        Spacer()

                        Text("Kullanım Klavuzu")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Spacer()

                        // Placeholder for symmetry
                        Text("Kapat")
                            .foregroundColor(.clear)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 10)

                    // Section Picker
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(0..<sections.count, id: \.self) { index in
                                Button(action: {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        selectedSection = index
                                    }
                                }) {
                                    VStack(spacing: 8) {
                                        Image(systemName: sections[index].icon)
                                            .font(.title2)
                                            .foregroundColor(selectedSection == index ? .white : .white.opacity(0.6))

                                        Text(sections[index].title)
                                            .font(.caption)
                                            .fontWeight(.medium)
                                            .foregroundColor(selectedSection == index ? .white : .white.opacity(0.6))
                                    }
                                    .frame(width: 80, height: 60)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(selectedSection == index ? Color.white.opacity(0.2) : Color.clear)
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                }
                .padding(.bottom, 20)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.tazeBitPrimary,
                            Color.tazeBitPrimary.opacity(0.8)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )

                // Content
                ScrollView {
                    LazyVStack(spacing: 0) {
                        GuideSectionView(section: sections[selectedSection])
                    }
                }
                .background(Color(.systemGroupedBackground))
            }
        }
        .navigationBarHidden(true)
    }
}

struct GuideSectionView: View {
    let section: GuideSection

    var body: some View {
        VStack(spacing: 20) {
            // Section Header
            VStack(spacing: 12) {
                Image(systemName: section.icon)
                    .font(.system(size: 40))
                    .foregroundColor(.tazeBitPrimary)

                Text(section.title)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(section.description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            .padding(.top, 30)
            .padding(.bottom, 20)

            // Steps
            LazyVStack(spacing: 16) {
                ForEach(Array(section.steps.enumerated()), id: \.offset) { index, step in
                    GuideStepView(step: step, stepNumber: index + 1)
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 30)
        }
    }
}

struct GuideStepView: View {
    let step: GuideStep
    let stepNumber: Int

    var body: some View {
        VStack(spacing: 16) {
            HStack(alignment: .top, spacing: 16) {
                // Step Number
                ZStack {
                    Circle()
                        .fill(Color.tazeBitPrimary)
                        .frame(width: 32, height: 32)

                    Text("\(stepNumber)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }

                // Step Content
                VStack(alignment: .leading, spacing: 8) {
                    Text(step.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(step.description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .fixedSize(horizontal: false, vertical: true)

                    if !step.tips.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            ForEach(step.tips, id: \.self) { tip in
                                HStack(alignment: .top, spacing: 8) {
                                    Image(systemName: "lightbulb.fill")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                        .padding(.top, 2)

                                    Text(tip)
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                        .fixedSize(horizontal: false, vertical: true)
                                }
                            }
                        }
                        .padding(.top, 4)
                    }
                }

                Spacer()
            }

            if stepNumber < 10 { // Assuming max 10 steps
                Divider()
                    .padding(.horizontal, 24)
            }
        }
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Data Models
struct GuideSection {
    let title: String
    let icon: String
    let description: String
    let steps: [GuideStep]

    static let allSections: [GuideSection] = [
        GuideSection(
            title: "Başlangıç",
            icon: "house.fill",
            description: "TazeBit'e hoş geldiniz! İlk adımlarınızı atın ve uygulamayı keşfetmeye başlayın.",
            steps: [
                GuideStep(
                    title: "Hesap Oluşturma",
                    description: "E-posta adresiniz ve güvenli bir şifre ile hesabınızı oluşturun.",
                    tips: ["Güçlü bir şifre seçin", "E-posta adresinizi doğrulamayı unutmayın"]
                ),
                GuideStep(
                    title: "İlk Ev Oluşturma",
                    description: "Ürünlerinizi organize etmek için ilk evinizi oluşturun. Ev adı ve açıklama ekleyebilirsiniz.",
                    tips: ["Anlamlı bir ev adı seçin", "Aile üyelerini davet etmeyi planlayın"]
                ),
                GuideStep(
                    title: "Profil Ayarları",
                    description: "Profil bilgilerinizi tamamlayın ve bildirim tercihlerinizi ayarlayın.",
                    tips: ["Profil fotoğrafı ekleyin", "Bildirim saatlerini ayarlayın"]
                )
            ]
        ),

        GuideSection(
            title: "AI Ürün Tespiti",
            icon: "brain.head.profile",
            description: "Yapay zeka ile fotoğraftan otomatik ürün tespiti yapın. En hızlı ürün ekleme yöntemi!",
            steps: [
                GuideStep(
                    title: "Fotoğraf Çekme",
                    description: "Ürünler sayfasında 'AI' butonuna tıklayın ve buzdolabınızın veya mutfağınızın fotoğrafını çekin.",
                    tips: ["İyi ışıklandırma kullanın", "Ürünlerin net görünmesini sağlayın", "Birden fazla açıdan fotoğraf çekebilirsiniz"]
                ),
                GuideStep(
                    title: "AI Analizi",
                    description: "Yapay zeka fotoğrafınızı analiz eder ve görünen tüm yiyecek ürünlerini tespit eder.",
                    tips: ["Analiz 10-30 saniye sürer", "İnternet bağlantınızın stabil olduğundan emin olun"]
                ),
                GuideStep(
                    title: "Ürün Seçimi",
                    description: "Tespit edilen ürünlerden istediğinizi seçin. Güven skorları %60'ın üzerinde olan ürünler gösterilir.",
                    tips: ["Tüm ürünler varsayılan olarak seçilidir", "İstemediğiniz ürünlerin seçimini kaldırabilirsiniz"]
                ),
                GuideStep(
                    title: "Otomatik Ekleme",
                    description: "Seçilen ürünler otomatik olarak evinizin ürün listesine eklenir.",
                    tips: ["Varsayılan 7 gün son kullanma tarihi atanır", "Daha sonra düzenleyebilirsiniz"]
                )
            ]
        ),

        GuideSection(
            title: "Ürün Yönetimi",
            icon: "cube.box.fill",
            description: "Ürünlerinizi manuel olarak ekleyin, düzenleyin ve takip edin.",
            steps: [
                GuideStep(
                    title: "Manuel Ürün Ekleme",
                    description: "Ürünler sayfasında '+' butonuna tıklayarak manuel olarak ürün ekleyebilirsiniz.",
                    tips: ["Ürün adını Türkçe girin", "Doğru kategori seçin", "Son kullanma tarihini dikkatli girin"]
                ),
                GuideStep(
                    title: "Ürün Düzenleme",
                    description: "Mevcut ürünlere tıklayarak bilgilerini düzenleyebilir, miktar güncelleyebilirsiniz.",
                    tips: ["Miktar değişikliklerini kaydetmeyi unutmayın", "Depolama yerini güncelleyebilirsiniz"]
                ),
                GuideStep(
                    title: "Tüketim Kaydı",
                    description: "Ürünleri tükettiğinizde miktarını azaltın veya tamamen tüketildi olarak işaretleyin.",
                    tips: ["Kısmi tüketim kaydedebilirsiniz", "Tüketim geçmişi tutulur"]
                )
            ]
        ),

        GuideSection(
            title: "Alışveriş Listeleri",
            icon: "cart.fill",
            description: "Alışveriş listelerinizi oluşturun ve yönetin. Satın aldıklarınızı eve ekleyin.",
            steps: [
                GuideStep(
                    title: "Liste Oluşturma",
                    description: "Alışveriş sayfasında yeni liste oluşturun ve ürün ekleyin.",
                    tips: ["Liste adını anlamlı seçin", "Kategorilere göre organize edin"]
                ),
                GuideStep(
                    title: "Ürün Ekleme",
                    description: "Listelerinize ürün ekleyin, miktar ve birim belirtin.",
                    tips: ["Tahmini fiyat ekleyebilirsiniz", "Öncelik seviyesi belirleyebilirsiniz"]
                ),
                GuideStep(
                    title: "Satın Alma",
                    description: "Alışveriş yaparken ürünleri satın alındı olarak işaretleyin.",
                    tips: ["Satın alınan ürünler eve otomatik eklenebilir"]
                )
            ]
        ),

        GuideSection(
            title: "Aile Paylaşımı",
            icon: "person.3.fill",
            description: "Ev halkınızla ürünlerinizi paylaşın ve birlikte yönetin.",
            steps: [
                GuideStep(
                    title: "Davet Kodu Oluşturma",
                    description: "Ev ayarlarından davet kodu oluşturun ve aile üyelerinizle paylaşın.",
                    tips: ["Davet kodu 6 haneli ve güvenlidir", "İstediğiniz zaman yenileyebilirsiniz"]
                ),
                GuideStep(
                    title: "Eve Katılma",
                    description: "Davet kodunu kullanarak başka bir eve katılabilirsiniz.",
                    tips: ["Birden fazla eve üye olabilirsiniz", "Ev değiştirmek için ana sayfayı kullanın"]
                ),
                GuideStep(
                    title: "Üye Yönetimi",
                    description: "Ev sahibi olarak üyeleri görüntüleyebilir ve yönetebilirsiniz.",
                    tips: ["Üye rollerini görebilirsiniz", "Gerekirse üyeleri çıkarabilirsiniz"]
                )
            ]
        ),

        GuideSection(
            title: "Bildirimler",
            icon: "bell.fill",
            description: "Son kullanma tarihi yaklaşan ürünler için akıllı bildirimler alın.",
            steps: [
                GuideStep(
                    title: "Bildirim Ayarları",
                    description: "Profil sayfasından bildirim tercihlerinizi ayarlayın.",
                    tips: ["Bildirim saatlerini belirleyin", "Hangi günlerde bildirim almak istediğinizi seçin"]
                ),
                GuideStep(
                    title: "Son Kullanma Uyarıları",
                    description: "Ürünlerin son kullanma tarihi yaklaştığında otomatik bildirim alırsınız.",
                    tips: ["1, 3 ve 7 gün önceden uyarı alabilirsiniz", "Bildirimleri özelleştirebilirsiniz"]
                ),
                GuideStep(
                    title: "Akıllı Öneriler",
                    description: "Bildirimlerde ürünleri değerlendirme önerileri alırsınız.",
                    tips: ["Tarif önerileri dahil edilir", "Tüketim önerileri verilir"]
                )
            ]
        ),

        GuideSection(
            title: "AI Tarifler",
            icon: "sparkles",
            description: "Son kullanma tarihi yaklaşan ürünlerinize göre AI destekli tarif önerileri alın.",
            steps: [
                GuideStep(
                    title: "Tarif Önerileri",
                    description: "Ana sayfada mevcut ürünlerinize göre tarif önerileri görüntülenir.",
                    tips: ["Öneriler günlük güncellenir", "Ürün durumuna göre öncelik verilir"]
                ),
                GuideStep(
                    title: "Tarif Detayları",
                    description: "Tarif kartlarına tıklayarak malzemeler, yapılış ve süre bilgilerini görün.",
                    tips: ["Eksik malzemeler vurgulanır", "Alışveriş listesine ekleyebilirsiniz"]
                ),
                GuideStep(
                    title: "Favoriler",
                    description: "Beğendiğiniz tarifleri favorilerinize ekleyerek daha sonra kolayca erişin.",
                    tips: ["Favori tarifler profil sayfasında listelenir", "Hızlı erişim için kullanın"]
                )
            ]
        ),

        GuideSection(
            title: "Raporlar",
            icon: "chart.bar.fill",
            description: "Tüketim alışkanlıklarınızı analiz edin ve gıda israfını azaltın.",
            steps: [
                GuideStep(
                    title: "Tüketim Raporları",
                    description: "Hangi ürünleri ne kadar tükettiğinizi ve ne kadar israf ettiğinizi görün.",
                    tips: ["Haftalık ve aylık raporlar mevcuttur", "Kategori bazında analiz yapabilirsiniz"]
                ),
                GuideStep(
                    title: "İsraf Analizi",
                    description: "En çok israf edilen ürünleri tespit edin ve alışkanlıklarınızı iyileştirin.",
                    tips: ["İsraf nedenlerini analiz edin", "Gelecek alışverişlerinizi planlayın"]
                ),
                GuideStep(
                    title: "Tasarruf Hesaplama",
                    description: "TazeBit kullanarak ne kadar para ve gıda tasarrufu yaptığınızı görün.",
                    tips: ["Çevresel etkilerinizi de takip edin", "Hedefler belirleyebilirsiniz"]
                )
            ]
        )
    ]
}

struct GuideStep {
    let title: String
    let description: String
    let tips: [String]

    init(title: String, description: String, tips: [String] = []) {
        self.title = title
        self.description = description
        self.tips = tips
    }
}

#Preview {
    UserGuideView()
}
