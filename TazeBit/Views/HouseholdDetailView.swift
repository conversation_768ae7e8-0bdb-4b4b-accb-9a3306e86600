//
//  HouseholdDetailView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> Ko<PERSON>ma<PERSON> on 28.05.2025.
//

import SwiftUI

struct HouseholdDetailView: View {
    let household: Household
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @EnvironmentObject var productViewModel: ProductViewModel
    @StateObject private var consumptionViewModel = ConsumptionViewModel()
    @Environment(\.dismiss) private var dismiss

    @State private var householdMembers: [HouseholdMember] = []
    @State private var isLoadingMembers = false
    @State private var isLoadingStats = false
    @State private var totalProducts = 0
    @State private var expiredProducts = 0
    @State private var expiringProducts = 0
    @State private var totalConsumptions = 0

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 0) {
                        // Header Section
                        headerSection

                        // Content Section
                        VStack(spacing: 20) {
                            // Household Info Card
                            householdInfoCard

                            // Statistics Cards
                            statisticsSection

                            // Members Section
                            membersSection

                            // Actions Section
                            actionsSection
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                    }
                }
                .refreshable {
                    await refreshData()
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
            .onAppear {
                Task {
                    await loadData()
                }
            }
            .sheet(isPresented: $householdViewModel.showingEditHousehold) {
                EditHouseholdView()
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $householdViewModel.showingInviteCode) {
                InviteCodeView()
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $householdViewModel.showingLeaveConfirmation) {
                LeaveHouseholdView()
                    .environmentObject(householdViewModel)
            }
        }
    }

    private var headerSection: some View {
        ZStack {
            // Primary Color Background
            Color.tazeBitPrimary
                .frame(height: 200)
                .clipShape(
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 30,
                        bottomTrailingRadius: 30,
                        topTrailingRadius: 0
                    )
                )
                .ignoresSafeArea(.all)

            VStack(spacing: 16) {
                // Top Bar
                HStack {
                    // Action Buttons - Left Side
                    HStack(spacing: 16) {
                        // Edit Button
                        Button(action: {
                            // Set the current household as selected before showing edit
                            householdViewModel.selectedHousehold = household
                            householdViewModel.showEditHousehold()
                        }) {
                            ZStack {
                                // Outer transparent circle
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                                    .frame(width: 44, height: 44)

                                // Inner solid circle
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: 32, height: 32)

                                // Icon
                                Image(systemName: "pencil")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.tazeBitPrimary)
                            }
                        }

                        // Share Invite Button
                        Button(action: {
                            // Set the current household as selected before showing invite code
                            householdViewModel.selectedHousehold = household
                            householdViewModel.showInviteCode()
                        }) {
                            ZStack {
                                // Outer transparent circle
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                                    .frame(width: 44, height: 44)

                                // Inner solid circle
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: 32, height: 32)

                                // Icon
                                Image(systemName: "square.and.arrow.up")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.tazeBitPrimary)
                            }
                        }
                    }

                    Spacer()

                    // Centered Title
                    VStack(spacing: 4) {
                        Text("Ev Detayı 🏠")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)

                        Text(household.name)
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                    }

                    Spacer()

                    // Close Button - Right Side
                    Button(action: {
                        dismiss()
                    }) {
                        ZStack {
                            // Outer transparent circle
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .frame(width: 44, height: 44)

                            // Inner solid circle
                            Circle()
                                .fill(Color.white)
                                .frame(width: 32, height: 32)

                            // Icon
                            Image(systemName: "xmark")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.tazeBitPrimary)
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)

                Spacer()
            }
        }
        .ignoresSafeArea(.all)
    }

    private var householdInfoCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Title
            HStack {
                Image(systemName: "house.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Ev Bilgileri")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            // Info Items
            VStack(spacing: 12) {
                InfoRow(
                    icon: "textformat",
                    title: "Ev Adı",
                    value: household.name
                )

                if let description = household.description, !description.isEmpty {
                    InfoRow(
                        icon: "text.alignleft",
                        title: "Açıklama",
                        value: description
                    )
                }

                InfoRow(
                    icon: "key.fill",
                    title: "Davet Kodu",
                    value: household.inviteCode
                )

                InfoRow(
                    icon: "calendar",
                    title: "Oluşturulma Tarihi",
                    value: DateFormatter.displayDate.string(from: household.createdAt)
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.cardBackground)
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }

    private var statisticsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Title
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("İstatistikler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                if isLoadingStats {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(.tazeBitPrimary)
                }
            }

            // Stats Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                StatCard(
                    icon: "cube.box.fill",
                    title: "Toplam Ürün",
                    value: "\(totalProducts)",
                    color: .blue
                )

                StatCard(
                    icon: "exclamationmark.triangle.fill",
                    title: "Süresi Geçen",
                    value: "\(expiredProducts)",
                    color: .red
                )

                StatCard(
                    icon: "clock.fill",
                    title: "Yakında Bitecek",
                    value: "\(expiringProducts)",
                    color: .orange
                )

                StatCard(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Tüketim",
                    value: "\(totalConsumptions)",
                    color: .green
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.cardBackground)
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }

    private var membersSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Title
            HStack {
                Image(systemName: "person.2.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Ev Üyeleri")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("\(householdMembers.count) üye")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)

                if isLoadingMembers {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(.tazeBitPrimary)
                }
            }

            // Members List
            if householdMembers.isEmpty && !isLoadingMembers {
                Text("Henüz üye yok")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            } else {
                VStack(spacing: 12) {
                    ForEach(householdMembers) { member in
                        MemberRow(member: member)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.cardBackground)
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }

    private var actionsSection: some View {
        VStack(spacing: 12) {
            // Leave Household Button
            Button(action: {
                householdViewModel.showLeaveConfirmation()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "door.right.hand.open")
                        .font(.headline)

                    Text("Evden Ayrıl")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.red)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red, lineWidth: 1)
                        .fill(Color.red.opacity(0.05))
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 20)
    }

    private func loadData() async {
        await withTaskGroup(of: Void.self) { group in
            group.addTask {
                await loadMembers()
            }
            group.addTask {
                await loadStatistics()
            }
        }
    }

    private func refreshData() async {
        await loadData()
    }

    private func loadMembers() async {
        isLoadingMembers = true
        defer { isLoadingMembers = false }

        do {
            let members = try await SupabaseService.shared.fetchHouseholdMembers(household.id)
            await MainActor.run {
                self.householdMembers = members
            }
        } catch {
            print("Error loading household members: \(error)")
        }
    }

    private func loadStatistics() async {
        isLoadingStats = true
        defer { isLoadingStats = false }

        do {
            // Load products for statistics
            let products = try await SupabaseService.shared.fetchProducts(for: household.id)

            // Load consumptions for statistics
            await consumptionViewModel.loadConsumptions(for: household.id)

            await MainActor.run {
                self.totalProducts = products.count

                let now = Date()
                let calendar = Calendar.current
                let threeDaysFromNow = calendar.date(byAdding: .day, value: 3, to: now) ?? now

                self.expiredProducts = products.filter { $0.expiryDate < now }.count
                self.expiringProducts = products.filter {
                    $0.expiryDate >= now && $0.expiryDate <= threeDaysFromNow
                }.count
                self.totalConsumptions = consumptionViewModel.consumptions.count
            }
        } catch {
            print("Error loading statistics: \(error)")
        }
    }
}

// MARK: - Supporting Views

struct InfoRow: View {
    let icon: String
    let title: String
    let value: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(.tazeBitPrimary)
                .frame(width: 20)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.textSecondary)

                Text(value)
                    .font(.subheadline)
                    .foregroundColor(.textPrimary)
                    .fontWeight(.medium)
            }

            Spacer()
        }
    }
}

struct StatCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct MemberRow: View {
    let member: HouseholdMember

    var body: some View {
        HStack(spacing: 12) {
            // Avatar
            ZStack {
                Circle()
                    .fill(Color.tazeBitPrimary)
                    .frame(width: 40, height: 40)

                Text(member.user.fullName.prefix(1).uppercased())
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }

            // Member Info
            VStack(alignment: .leading, spacing: 2) {
                Text(member.user.fullName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)

                HStack(spacing: 8) {
                    Text(member.role.displayName)
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(member.role == .owner ? Color.tazeBitPrimary : Color.tazeBitSecondary)
                        )

                    Text("Katılma: \(DateFormatter.shortDate.string(from: member.joinedAt))")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }

            Spacer()

            // Role Icon
            Image(systemName: member.role == .owner ? "crown.fill" : "person.fill")
                .font(.subheadline)
                .foregroundColor(member.role == .owner ? .yellow : .textSecondary)
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Date Formatters Extension

extension DateFormatter {
    static let displayDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "tr_TR")
        return formatter
    }()
}
