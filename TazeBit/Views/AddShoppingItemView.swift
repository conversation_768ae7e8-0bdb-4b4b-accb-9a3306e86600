import SwiftUI

struct AddShoppingItemView: View {
    let shoppingListId: UUID
    @EnvironmentObject var shoppingListViewModel: ShoppingListViewModel
    @Environment(\.dismiss) private var dismiss

    @State private var name = ""
    @State private var quantity = 1.0
    @State private var selectedUnit = ProductUnit.piece
    @State private var selectedCategory = ProductCategory.other
    @State private var notes = ""
    @State private var isLoading = false

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 20) {
                            // Product Info Card
                            productInfoCard

                            // Notes Card
                            notesCard

                            // Action Buttons
                            actionButtons

                            // Error Message
                            if let errorMessage = shoppingListViewModel.errorMessage, !errorMessage.isEmpty {
                                errorMessageView
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    Text("Ürün Ekle")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    // Save Button
                    Button(action: {
                        saveItem()
                    }) {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        } else {
                            Image(systemName: "checkmark")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.2))
                    )
                    .disabled(isLoading || name.isEmpty)
                    .opacity(isLoading || name.isEmpty ? 0.5 : 1.0)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Product Info Card
    private var productInfoCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "cart.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Ürün Bilgileri")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 16) {
                // Product Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("Ürün Adı")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    TextField("Örn: Süt, Ekmek, Domates", text: $name)
                        .textFieldStyle(CustomTextFieldStyle())
                }

                // Category
                VStack(alignment: .leading, spacing: 8) {
                    Text("Kategori")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    Picker("Kategori Seçin", selection: $selectedCategory) {
                        ForEach(ProductCategory.allCases, id: \.self) { category in
                            Text("\(category.icon) \(category.displayName)")
                                .tag(category)
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
                    .background(Color.cardBackground)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
                }

                // Quantity and Unit
                VStack(alignment: .leading, spacing: 8) {
                    Text("Miktar ve Birim")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    HStack(spacing: 12) {
                        // Quantity Input
                        VStack(spacing: 4) {
                            Text("Miktar")
                                .font(.caption)
                                .foregroundColor(.textSecondary)

                            TextField("1", value: $quantity, format: .number)
                                .textFieldStyle(CustomTextFieldStyle())
                                .keyboardType(.decimalPad)
                        }

                        Spacer()

                        // Unit Picker
                        VStack(spacing: 4) {
                            Text("Birim")
                                .font(.caption)
                                .foregroundColor(.textSecondary)

                            Picker("Birim Seçin", selection: $selectedUnit) {
                                ForEach(ProductUnit.allCases, id: \.self) { unit in
                                    Text(unit.displayName)
                                        .tag(unit)
                                }
                            }
                            .pickerStyle(.menu)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.cardBackground)
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.borderColor, lineWidth: 1)
                            )
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Notes Card
    private var notesCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "note.text")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Notlar")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("İsteğe Bağlı")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.tazeBitPrimary.opacity(0.1))
                    .cornerRadius(8)
            }

            TextField("Örn: Az yağlı tercih et, taze olanlardan seç", text: $notes, axis: .vertical)
                .lineLimit(3...6)
                .textFieldStyle(CustomTextFieldStyle())
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Action Buttons
    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Save Button
            Button(action: {
                saveItem()
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text(isLoading ? "Ekleniyor..." : "Ürün Ekle")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.tazeBitPrimary)
                )
            }
            .disabled(isLoading || name.isEmpty)
            .opacity(isLoading || name.isEmpty ? 0.7 : 1.0)

            // Cancel Button
            Button(action: {
                dismiss()
            }) {
                Text("İptal")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
            .disabled(isLoading)
        }
    }

    // MARK: - Error Message
    private var errorMessageView: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)

            Text(shoppingListViewModel.errorMessage ?? "")
                .font(.subheadline)
                .foregroundColor(.red)
                .multilineTextAlignment(.leading)

            Spacer()
        }
        .padding(16)
        .background(Color.red.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
    }

    // MARK: - Actions
    private func saveItem() {
        isLoading = true

        Task {
            await shoppingListViewModel.createShoppingListItem(
                shoppingListId: shoppingListId,
                name: name,
                quantity: quantity,
                unit: selectedUnit.rawValue,
                category: selectedCategory.rawValue,
                notes: notes.isEmpty ? nil : notes
            )

            isLoading = false

            if !shoppingListViewModel.showingAddItem {
                dismiss()
            }
        }
    }
}

#Preview {
    AddShoppingItemView(shoppingListId: UUID())
        .environmentObject(ShoppingListViewModel())
}
