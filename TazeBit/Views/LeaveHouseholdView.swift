//
//  LeaveHouseholdView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct LeaveHouseholdView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header Section
                    headerSection
                    
                    // Content Section
                    ScrollView {
                        VStack(spacing: 24) {
                            // Warning Icon
                            warningIcon
                            
                            // Household Info
                            if let household = householdViewModel.selectedHousehold {
                                householdInfoCard(household)
                            }
                            
                            // Warning Message
                            warningMessage
                            
                            // Action Buttons
                            actionButtons
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)
            
            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary
                
                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }
                    
                    Spacer()
                    
                    // Title
                    Text("Evden Ayrıl")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // Balance space
                    Color.clear
                        .frame(width: 32, height: 32)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }
    
    private var warningIcon: some View {
        ZStack {
            Circle()
                .fill(Color.red.opacity(0.1))
                .frame(width: 80, height: 80)
            
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 32, weight: .medium))
                .foregroundColor(.red)
        }
    }
    
    private func householdInfoCard(_ household: Household) -> some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "house.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(household.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)
                    
                    if let description = household.description, !description.isEmpty {
                        Text(description)
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                    }
                }
                
                Spacer()
            }
            .padding(16)
            .background(Color.cardBackground)
            .cornerRadius(12)
            .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
        }
    }
    
    private var warningMessage: some View {
        VStack(spacing: 16) {
            Text("Bu işlem geri alınamaz!")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.red)
            
            VStack(alignment: .leading, spacing: 8) {
                warningItem(
                    icon: "trash.fill",
                    text: "Bu evdeki tüm ürün verilerinize erişiminizi kaybedeceksiniz"
                )
                
                warningItem(
                    icon: "person.2.slash.fill",
                    text: "Tekrar katılmak için davet kodu gerekecek"
                )
                
                warningItem(
                    icon: "clock.fill",
                    text: "Ev sahibiyseniz ve başka üyeler varsa ayrılamazsınız"
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.red.opacity(0.05))
                .stroke(Color.red.opacity(0.2), lineWidth: 1)
        )
    }
    
    private func warningItem(icon: String, text: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.red)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.textPrimary)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
    }
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Leave Button
            Button(action: {
                Task {
                    await householdViewModel.leaveCurrentHousehold()
                }
            }) {
                HStack {
                    if householdViewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: "door.right.hand.open")
                            .font(.system(size: 16, weight: .medium))
                    }
                    
                    Text(householdViewModel.isLoading ? "Ayrılıyor..." : "Evden Ayrıl")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.red)
                )
            }
            .disabled(householdViewModel.isLoading)
            
            // Cancel Button
            Button(action: {
                dismiss()
            }) {
                Text("İptal")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
            .disabled(householdViewModel.isLoading)
        }
        .padding(.bottom, 20)
    }
}

#Preview {
    LeaveHouseholdView()
        .environmentObject(HouseholdViewModel())
}
