//
//  AddProductView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct AddProductView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @EnvironmentObject var productViewModel: ProductViewModel
    @Environment(\.dismiss) private var dismiss

    private var isEditing: Bool {
        productViewModel.editingProduct != nil
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 20) {
                            // Product Info Card
                            productInfoCard

                            // Dates Card
                            datesCard

                            // Notes Card
                            notesCard

                            // Delete Button (only for editing)
                            if isEditing {
                                deleteButton
                            }

                            // Action Buttons
                            actionButtons

                            // Error Message
                            if !productViewModel.errorMessage.isEmpty {
                                errorMessage
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                // Title
                Text(isEditing ? "Ürün Düzenle" : "Ürün Ekle")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Product Info Card
    private var productInfoCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "cube.box.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Ürün Bilgileri")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 16) {
                // Product Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("Ürün Adı")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    TextField("Ürün adını girin", text: $productViewModel.productName)
                        .textFieldStyle(CustomTextFieldStyle())
                }

                // Category
                VStack(alignment: .leading, spacing: 8) {
                    Text("Kategori")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    Picker("Kategori Seçin", selection: $productViewModel.selectedCategory) {
                        ForEach(ProductCategory.allCases, id: \.self) { category in
                            Text("\(category.icon) \(category.displayName)")
                                .tag(category)
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
                    .background(Color.cardBackground)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
                }

                // Storage Location
                VStack(alignment: .leading, spacing: 8) {
                    Text("Saklama Yeri")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    Picker("Saklama Yeri Seçin", selection: $productViewModel.selectedStorageLocation) {
                        ForEach(StorageLocation.allCases, id: \.self) { location in
                            Text("\(location.icon) \(location.displayName)")
                                .tag(location)
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
                    .background(Color.cardBackground)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
                }

                // Quantity and Unit
                VStack(alignment: .leading, spacing: 8) {
                    Text("Miktar ve Birim")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    HStack(spacing: 12) {
                        // Quantity Stepper
                        VStack(spacing: 4) {
                            Text("Miktar")
                                .font(.caption)
                                .foregroundColor(.textSecondary)

                            HStack {
                                Button(action: {
                                    if productViewModel.quantity > 1 {
                                        productViewModel.quantity -= 1
                                    }
                                }) {
                                    Image(systemName: "minus")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(.tazeBitPrimary)
                                        .frame(width: 32, height: 32)
                                        .background(Color.tazeBitPrimary.opacity(0.1))
                                        .cornerRadius(8)
                                }
                                .disabled(productViewModel.quantity <= 1)

                                Text("\(productViewModel.quantity)")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.textPrimary)
                                    .frame(minWidth: 40)

                                Button(action: {
                                    if productViewModel.quantity < 999 {
                                        productViewModel.quantity += 1
                                    }
                                }) {
                                    Image(systemName: "plus")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(.tazeBitPrimary)
                                        .frame(width: 32, height: 32)
                                        .background(Color.tazeBitPrimary.opacity(0.1))
                                        .cornerRadius(8)
                                }
                                .disabled(productViewModel.quantity >= 999)
                            }
                        }

                        Spacer()

                        // Unit Picker
                        VStack(spacing: 4) {
                            Text("Birim")
                                .font(.caption)
                                .foregroundColor(.textSecondary)

                            Picker("Birim Seçin", selection: $productViewModel.selectedUnit) {
                                ForEach(ProductUnit.allCases, id: \.self) { unit in
                                    Text(unit.displayName)
                                        .tag(unit)
                                }
                            }
                            .pickerStyle(.menu)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.cardBackground)
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.borderColor, lineWidth: 1)
                            )
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Dates Card
    private var datesCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "calendar.circle.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Tarihler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 16) {
                // Expiry Date
                VStack(alignment: .leading, spacing: 8) {
                    Text("Son Kullanma Tarihi")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    DatePicker("", selection: $productViewModel.expiryDate, displayedComponents: .date)
                        .datePickerStyle(.compact)
                        .labelsHidden()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(12)
                        .background(Color.cardBackground)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.borderColor, lineWidth: 1)
                        )
                }

                // Purchase Date Toggle
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Toggle("Satın Alma Tarihi Ekle", isOn: $productViewModel.includePurchaseDate)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.textSecondary)
                            .toggleStyle(SwitchToggleStyle(tint: .tazeBitPrimary))
                    }

                    if productViewModel.includePurchaseDate {
                        DatePicker("", selection: $productViewModel.purchaseDate, displayedComponents: .date)
                            .datePickerStyle(.compact)
                            .labelsHidden()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(12)
                            .background(Color.cardBackground)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.borderColor, lineWidth: 1)
                            )
                            .transition(.opacity.combined(with: .scale))
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Notes Card
    private var notesCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "note.text")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Notlar")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("İsteğe Bağlı")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.tazeBitPrimary.opacity(0.1))
                    .cornerRadius(8)
            }

            TextField("Ürün hakkında notlar ekleyin...", text: $productViewModel.notes, axis: .vertical)
                .lineLimit(3...6)
                .textFieldStyle(CustomTextFieldStyle())
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Delete Button
    private var deleteButton: some View {
        Button(action: {
            if let product = productViewModel.editingProduct {
                productViewModel.showDeleteConfirmation(for: product)
            }
        }) {
            HStack {
                Image(systemName: "trash.fill")
                    .font(.system(size: 16, weight: .medium))

                Text("Ürünü Sil")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.red)
            )
        }
        .sheet(isPresented: $productViewModel.showingDeleteConfirmation) {
            DeleteConfirmationView()
                .environmentObject(productViewModel)
        }
    }

    // MARK: - Action Buttons
    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Save Button
            Button(action: {
                Task {
                    if let household = householdViewModel.selectedHousehold {
                        if isEditing {
                            await productViewModel.updateProduct()
                        } else {
                            await productViewModel.addProduct(to: household.id)
                        }

                        if productViewModel.errorMessage.isEmpty {
                            dismiss()
                        }
                    }
                }
            }) {
                HStack {
                    if productViewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: isEditing ? "checkmark.circle.fill" : "plus.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text(productViewModel.isLoading ? (isEditing ? "Güncelleniyor..." : "Ekleniyor...") : (isEditing ? "Değişiklikleri Kaydet" : "Ürünü Ekle"))
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.tazeBitPrimary)
                )
            }
            .disabled(productViewModel.isLoading || (!isEditing && productViewModel.productName.isEmpty))
            .opacity(productViewModel.isLoading || (!isEditing && productViewModel.productName.isEmpty) ? 0.7 : 1.0)

            // Cancel Button
            Button(action: {
                dismiss()
            }) {
                Text("İptal")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
            .disabled(productViewModel.isLoading)
        }
    }

    // MARK: - Error Message
    private var errorMessage: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)

            Text(productViewModel.errorMessage)
                .font(.subheadline)
                .foregroundColor(.red)
                .multilineTextAlignment(.leading)

            Spacer()
        }
        .padding(16)
        .background(Color.red.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
    }
}

#Preview {
    AddProductView()
        .environmentObject(HouseholdViewModel())
        .environmentObject(ProductViewModel())
}
