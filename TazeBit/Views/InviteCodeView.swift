//
//  InviteCodeView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 27.05.2025.
//

import SwiftUI

struct InviteCodeView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showCopySuccess = false

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 0) {
                        // Header Section
                        headerSection

                        // Content Section
                        VStack(spacing: 24) {
                            // Icon and Title
                            iconSection

                            // Invite Code Display
                            if let household = householdViewModel.selectedHousehold {
                                inviteCodeSection(household: household)
                            }

                            // Instructions
                            instructionsSection

                            // Action Buttons
                            actionButtons
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }

                // Copy Success Toast
                if showCopySuccess {
                    VStack {
                        Spacer()

                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.title2)

                            Text("Davet mesajı kopyalandı!")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.black.opacity(0.8))
                        )
                        .padding(.bottom, 100)
                    }
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .zIndex(1)
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }

    private var headerSection: some View {
        VStack(spacing: 0) {
            // Background with gradient
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [
                            Color.tazeBitPrimary,
                            Color.tazeBitPrimary.opacity(0.8)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(height: 120)
                .overlay(
                    VStack {
                        Spacer()

                        HStack {
                            // Close Button
                            Button(action: { dismiss() }) {
                                Image(systemName: "xmark")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                                    .frame(width: 44, height: 44)
                                    .background(
                                        Circle()
                                            .fill(.white.opacity(0.2))
                                    )
                            }

                            Spacer()

                            Text("Davet Kodu")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            Spacer()

                            // Invisible button for balance
                            Color.clear
                                .frame(width: 44, height: 44)
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 16)
                    }
                )
        }
        .ignoresSafeArea(.all)
    }

    private var iconSection: some View {
        VStack(spacing: 16) {
            // Icon
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.tazeBitPrimary.opacity(0.1),
                                Color.tazeBitPrimary.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)

                Image(systemName: "person.badge.plus")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.tazeBitPrimary)
            }

            // Title and Description
            VStack(spacing: 8) {
                Text("Kullanıcı Davet Et")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)

                Text("Bu kodu paylaşarak başkalarını evinize davet edebilirsiniz")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
    }

    private func inviteCodeSection(household: Household) -> some View {
        VStack(spacing: 16) {
            // Household Name
            Text(household.name)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.textPrimary)

            // Invite Code Display
            VStack(spacing: 12) {
                Text("Davet Kodu")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.textSecondary)
                    .textCase(.uppercase)
                    .tracking(1)

                // Code Display
                HStack(spacing: 4) {
                    ForEach(Array(household.inviteCode.enumerated()), id: \.offset) { index, character in
                        Text(String(character))
                            .font(.system(size: 24, weight: .bold, design: .monospaced))
                            .foregroundColor(.tazeBitPrimary)
                            .frame(width: 40, height: 50)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.tazeBitPrimary.opacity(0.1))
                                    .stroke(Color.tazeBitPrimary.opacity(0.3), lineWidth: 1)
                            )
                    }
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.cardBackground)
                    .stroke(Color.borderColor, lineWidth: 1)
            )
        }
    }

    private var instructionsSection: some View {
        VStack(spacing: 16) {
            Text("Nasıl Kullanılır?")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.textPrimary)

            VStack(spacing: 12) {
                InstructionRow(
                    icon: "1.circle.fill",
                    title: "Kodu Paylaş",
                    description: "Yukarıdaki 6 haneli kodu davet etmek istediğiniz kişiyle paylaşın"
                )

                InstructionRow(
                    icon: "2.circle.fill",
                    title: "Uygulamayı İndirsin",
                    description: "Davet ettiğiniz kişi TazeBit uygulamasını indirsin ve hesap oluştursun"
                )

                InstructionRow(
                    icon: "3.circle.fill",
                    title: "Kodu Girsin",
                    description: "\"Eve Katıl\" seçeneğinden bu kodu girerek evinize katılabilir"
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.cardBackground)
                .stroke(Color.borderColor, lineWidth: 1)
        )
    }

    private var actionButtons: some View {
        VStack(spacing: 16) {
            // Action Buttons
            if let household = householdViewModel.selectedHousehold {
                // Copy Button
                Button(action: {
                    copyInviteCode(household.inviteCode, householdName: household.name)
                }) {
                    HStack {
                        Image(systemName: "doc.on.doc")
                            .font(.system(size: 16, weight: .medium))

                        Text("Kopyala")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color.tazeBitPrimary,
                                        Color.tazeBitPrimary.opacity(0.8)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
                }
            }

            // Close Button
            Button(action: { dismiss() }) {
                Text("Kapat")
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
        }
    }

    private func copyInviteCode(_ code: String, householdName: String) {
        let shareText = """
        🏠 \(householdName) evine davetlisiniz!

        TazeBit uygulamasını indirin ve aşağıdaki davet kodunu kullanarak katılın:

        Davet Kodu: \(code)

        📱 App Store'dan TazeBit'i indirin
        ✅ Hesap oluşturun
        🏠 "Eve Katıl" seçeneğinden bu kodu girin
        """

        UIPasteboard.general.string = shareText

        // Show success feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // Show toast message
        withAnimation(.easeInOut(duration: 0.3)) {
            showCopySuccess = true
        }

        // Hide toast after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.3)) {
                showCopySuccess = false
            }
        }
    }


}

struct InstructionRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.tazeBitPrimary)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
                    .fixedSize(horizontal: false, vertical: true)
            }

            Spacer()
        }
    }
}

#Preview {
    InviteCodeView()
        .environmentObject(HouseholdViewModel())
}
