import SwiftUI

struct PrivacyView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header Section
                    headerSection
                    
                    // Privacy Overview Section
                    privacyOverviewSection
                    
                    // Data Collection Section
                    dataCollectionSection
                    
                    // Data Usage Section
                    dataUsageSection
                    
                    // Data Security Section
                    dataSecuritySection
                    
                    // User Rights Section
                    userRightsSection
                    
                    // Contact Section
                    contactSection
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
            .background(Color.tazeBitBackground.ignoresSafeArea())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Kapat") {
                        dismiss()
                    }
                    .foregroundColor(.tazeBitPrimary)
                }
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "lock.shield.fill")
                .font(.system(size: 60))
                .foregroundColor(.tazeBitPrimary)
            
            VStack(spacing: 8) {
                Text("Gizlilik Politikası")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.textPrimary)
                
                Text("Verilerinizin güvenliği bizim için önceliklidir")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(24)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Privacy Overview Section
    private var privacyOverviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Gizlilik Yaklaşımımız")
                .font(.headline)
                .foregroundColor(.textPrimary)
            
            VStack(alignment: .leading, spacing: 12) {
                Text("TazeBit olarak, kullanıcılarımızın gizliliğini korumayı ve kişisel verilerini güvenli bir şekilde işlemeyi taahhüt ediyoruz.")
                    .font(.body)
                    .foregroundColor(.textPrimary)
                
                Text("Bu gizlilik politikası, uygulamayı kullanırken hangi bilgilerin toplandığını, nasıl kullanıldığını ve korunduğunu açıklar.")
                    .font(.body)
                    .foregroundColor(.textPrimary)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Data Collection Section
    private var dataCollectionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Toplanan Veriler")
                .font(.headline)
                .foregroundColor(.textPrimary)
            
            LazyVStack(spacing: 12) {
                DataCategoryRow(
                    icon: "person.fill",
                    title: "Hesap Bilgileri",
                    description: "Ad, soyad, e-posta adresi ve şifre bilgileri"
                )
                
                DataCategoryRow(
                    icon: "house.fill",
                    title: "Ev Bilgileri",
                    description: "Ev adı, davet kodları ve üye bilgileri"
                )
                
                DataCategoryRow(
                    icon: "cart.fill",
                    title: "Ürün Bilgileri",
                    description: "Ürün adları, kategoriler, son kullanma tarihleri ve notlar"
                )
                
                DataCategoryRow(
                    icon: "chart.bar.fill",
                    title: "Kullanım Verileri",
                    description: "Tüketim alışkanlıkları ve uygulama kullanım istatistikleri"
                )
                
                DataCategoryRow(
                    icon: "bell.fill",
                    title: "Bildirim Tercihleri",
                    description: "Bildirim ayarları ve tercihleriniz"
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Data Usage Section
    private var dataUsageSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Verilerin Kullanımı")
                .font(.headline)
                .foregroundColor(.textPrimary)
            
            VStack(alignment: .leading, spacing: 12) {
                UsagePurposeRow(
                    icon: "checkmark.circle.fill",
                    purpose: "Uygulama hizmetlerini sağlamak ve geliştirmek"
                )
                
                UsagePurposeRow(
                    icon: "checkmark.circle.fill",
                    purpose: "Kişiselleştirilmiş deneyim sunmak"
                )
                
                UsagePurposeRow(
                    icon: "checkmark.circle.fill",
                    purpose: "Güvenlik ve dolandırıcılık önleme"
                )
                
                UsagePurposeRow(
                    icon: "checkmark.circle.fill",
                    purpose: "Yasal yükümlülükleri yerine getirmek"
                )
                
                UsagePurposeRow(
                    icon: "checkmark.circle.fill",
                    purpose: "Teknik destek ve müşteri hizmetleri"
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Data Security Section
    private var dataSecuritySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Veri Güvenliği")
                .font(.headline)
                .foregroundColor(.textPrimary)
            
            LazyVStack(spacing: 12) {
                SecurityFeatureRow(
                    icon: "lock.fill",
                    title: "Şifreleme",
                    description: "Tüm veriler SSL/TLS ile şifrelenir"
                )
                
                SecurityFeatureRow(
                    icon: "server.rack",
                    title: "Güvenli Sunucular",
                    description: "Veriler güvenli bulut sunucularında saklanır"
                )
                
                SecurityFeatureRow(
                    icon: "key.fill",
                    title: "Erişim Kontrolü",
                    description: "Sıkı erişim kontrolleri ve kimlik doğrulama"
                )
                
                SecurityFeatureRow(
                    icon: "shield.checkered",
                    title: "Düzenli Denetim",
                    description: "Güvenlik sistemleri düzenli olarak denetlenir"
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - User Rights Section
    private var userRightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Kullanıcı Hakları")
                .font(.headline)
                .foregroundColor(.textPrimary)
            
            VStack(alignment: .leading, spacing: 12) {
                UserRightRow(
                    icon: "eye.fill",
                    right: "Verilerinizi görme hakkı"
                )
                
                UserRightRow(
                    icon: "pencil.circle.fill",
                    right: "Verilerinizi düzeltme hakkı"
                )
                
                UserRightRow(
                    icon: "trash.circle.fill",
                    right: "Verilerinizi silme hakkı"
                )
                
                UserRightRow(
                    icon: "arrow.down.circle.fill",
                    right: "Verilerinizi indirme hakkı"
                )
                
                UserRightRow(
                    icon: "hand.raised.circle.fill",
                    right: "İşlemeye itiraz etme hakkı"
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Contact Section
    private var contactSection: some View {
        VStack(spacing: 16) {
            Text("İletişim")
                .font(.headline)
                .foregroundColor(.textPrimary)
            
            VStack(spacing: 12) {
                Text("Gizlilik ile ilgili sorularınız için:")
                    .font(.body)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                
                VStack(spacing: 8) {
                    ContactRow(icon: "envelope.fill", info: "<EMAIL>")
                    ContactRow(icon: "calendar.circle.fill", info: "Son Güncelleme: Mayıs 2025")
                }
            }
        }
        .frame(maxWidth: .infinity)
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
}

// MARK: - Supporting Views

struct DataCategoryRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.tazeBitPrimary)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct UsagePurposeRow: View {
    let icon: String
    let purpose: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(.green)
                .frame(width: 16, height: 16)
            
            Text(purpose)
                .font(.subheadline)
                .foregroundColor(.textPrimary)
            
            Spacer()
        }
        .padding(.vertical, 2)
    }
}

struct SecurityFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct UserRightRow: View {
    let icon: String
    let right: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(.orange)
                .frame(width: 16, height: 16)
            
            Text(right)
                .font(.subheadline)
                .foregroundColor(.textPrimary)
            
            Spacer()
        }
        .padding(.vertical, 2)
    }
}

struct ContactRow: View {
    let icon: String
    let info: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.tazeBitPrimary)
            
            Text(info)
                .font(.caption)
                .foregroundColor(.textSecondary)
        }
    }
}

#Preview {
    PrivacyView()
}
