//
//  AddToHouseView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

struct AddToHouseView: View {
    let shoppingItem: ShoppingListItem
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @EnvironmentObject var shoppingListViewModel: ShoppingListViewModel
    @Environment(\.dismiss) private var dismiss

    @State private var expiryDate = Date().addingTimeInterval(7 * 24 * 60 * 60) // 1 week from now
    @State private var purchaseDate = Date()
    @State private var selectedStorageLocation: StorageLocation = .refrigerator
    @State private var notes = ""
    @State private var includePurchaseDate = true
    @State private var isLoading = false

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 20) {
                            // Product Info Card
                            productInfoCard

                            // Date Settings Card
                            dateSettingsCard

                            // Storage Card
                            storageCard

                            // Notes Card
                            notesCard

                            // Action Buttons
                            actionButtons

                            // Error Message
                            if !productViewModel.errorMessage.isEmpty {
                                errorMessageView
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
        .onAppear {
            setupInitialValues()
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    Text("Eve Ekle")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    // Save Button
                    Button(action: {
                        addToHouse()
                    }) {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        } else {
                            Image(systemName: "checkmark")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.2))
                    )
                    .disabled(isLoading)
                    .opacity(isLoading ? 0.5 : 1.0)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Product Info Card
    private var productInfoCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "cart.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Ürün Bilgileri")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 12) {
                // Product Name
                HStack {
                    Text("Ürün Adı:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    Spacer()

                    Text(shoppingItem.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)
                }

                // Quantity
                HStack {
                    Text("Miktar:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    Spacer()

                    HStack(spacing: 4) {
                        Text(formattedQuantity)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.textPrimary)

                        if let unit = shoppingItem.unit, !unit.isEmpty {
                            Text(unit)
                                .font(.subheadline)
                                .foregroundColor(.textSecondary)
                        }
                    }
                }

                // Category
                if let category = shoppingItem.category, !category.isEmpty {
                    HStack {
                        Text("Kategori:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.textSecondary)

                        Spacer()

                        Text(category)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.textPrimary)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Date Settings Card
    private var dateSettingsCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "calendar")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Tarih Ayarları")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 16) {
                // Expiry Date
                VStack(alignment: .leading, spacing: 8) {
                    Text("Son Kullanma Tarihi")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    DatePicker("", selection: $expiryDate, displayedComponents: .date)
                        .datePickerStyle(.compact)
                        .labelsHidden()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(12)
                        .background(Color.cardBackground)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.borderColor, lineWidth: 1)
                        )
                }

                // Purchase Date Toggle
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Toggle("Satın Alma Tarihi Ekle", isOn: $includePurchaseDate)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.textSecondary)
                            .toggleStyle(SwitchToggleStyle(tint: .tazeBitPrimary))
                    }

                    if includePurchaseDate {
                        DatePicker("", selection: $purchaseDate, displayedComponents: .date)
                            .datePickerStyle(.compact)
                            .labelsHidden()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(12)
                            .background(Color.cardBackground)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.borderColor, lineWidth: 1)
                            )
                            .transition(.opacity.combined(with: .scale))
                    }
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
        .animation(.easeInOut(duration: 0.3), value: includePurchaseDate)
    }

    // MARK: - Storage Card
    private var storageCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "archivebox")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Depolama")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 8) {
                Text("Depolama Yeri")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textSecondary)

                Picker("Depolama Yeri Seçin", selection: $selectedStorageLocation) {
                    ForEach(StorageLocation.allCases, id: \.self) { location in
                        Text("\(location.icon) \(location.displayName)")
                            .tag(location)
                    }
                }
                .pickerStyle(.menu)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(16)
                .background(Color.cardBackground)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.borderColor, lineWidth: 1)
                )
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Notes Card
    private var notesCard: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "note.text")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Notlar")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("İsteğe Bağlı")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.tazeBitPrimary.opacity(0.1))
                    .cornerRadius(8)
            }

            TextField("Ek notlar ekleyin", text: $notes, axis: .vertical)
                .lineLimit(3...6)
                .textFieldStyle(CustomTextFieldStyle())
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Action Buttons
    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Add Button
            Button(action: {
                addToHouse()
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: "house.fill")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text(isLoading ? "Ekleniyor..." : "Eve Ekle")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.tazeBitPrimary)
                )
            }
            .disabled(isLoading)
            .opacity(isLoading ? 0.7 : 1.0)

            // Cancel Button
            Button(action: {
                dismiss()
            }) {
                Text("İptal")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
            .disabled(isLoading)
        }
    }

    // MARK: - Error Message
    private var errorMessageView: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)

            Text(productViewModel.errorMessage)
                .font(.subheadline)
                .foregroundColor(.red)
                .multilineTextAlignment(.leading)

            Spacer()
        }
        .padding(16)
        .background(Color.red.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
    }

    // MARK: - Helper Methods
    private func setupInitialValues() {
        // Set notes from shopping item if available
        if let shoppingNotes = shoppingItem.notes, !shoppingNotes.isEmpty {
            notes = shoppingNotes
        }
    }

    private var formattedQuantity: String {
        if shoppingItem.quantity == floor(shoppingItem.quantity) {
            return String(Int(shoppingItem.quantity))
        } else {
            return String(format: "%.1f", shoppingItem.quantity)
        }
    }

    private func addToHouse() {
        guard let household = householdViewModel.selectedHousehold else {
            return
        }

        isLoading = true

        // Clear any previous error
        productViewModel.errorMessage = ""

        // Set up ProductViewModel with the shopping item data
        productViewModel.productName = shoppingItem.name
        productViewModel.selectedCategory = ProductCategory.fromString(shoppingItem.category ?? "")
        productViewModel.selectedStorageLocation = selectedStorageLocation
        productViewModel.expiryDate = expiryDate
        productViewModel.purchaseDate = purchaseDate
        productViewModel.quantity = Int(shoppingItem.quantity)
        productViewModel.selectedUnit = ProductUnit.fromString(shoppingItem.unit ?? "adet")
        productViewModel.notes = notes
        productViewModel.includePurchaseDate = includePurchaseDate

        Task {
            // Alışveriş listesinden ürün ekleme - ilişkiyi kur
            await productViewModel.addProductFromShoppingList(to: household.id, shoppingListItemId: shoppingItem.id)

            await MainActor.run {
                isLoading = false

                if productViewModel.errorMessage.isEmpty {
                    // Mark shopping item as added to house
                    Task {
                        await shoppingListViewModel.markItemAsAddedToHouse(shoppingItem)
                    }
                    dismiss()
                }
            }
        }
    }
}



#Preview {
    AddToHouseView(
        shoppingItem: ShoppingListItem(
            id: UUID(),
            shoppingListId: UUID(),
            name: "Süt",
            quantity: 2,
            unit: "litre",
            category: "Süt Ürünleri",
            notes: "Az yağlı tercih et",
            isPurchased: true,
            isAddedToHouse: false,
            addedToHouseAt: nil,
            createdAt: Date(),
            updatedAt: Date()
        )
    )
    .environmentObject(ProductViewModel())
    .environmentObject(HouseholdViewModel())
    .environmentObject(ShoppingListViewModel())
}