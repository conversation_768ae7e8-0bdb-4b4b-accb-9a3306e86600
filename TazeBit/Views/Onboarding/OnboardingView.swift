import SwiftUI

struct OnboardingView: View {
    @State private var currentPage = 0
    @State private var showingLogin = false
    @AppStorage("hasSeenOnboarding") private var hasSeenOnboarding = false
    @Environment(\.dismiss) private var dismiss

    private let pages = OnboardingPage.allPages

    var body: some View {
        ZStack {
            // Background Gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.tazeBitPrimary.opacity(0.8),
                    Color.tazeBitPrimary.opacity(0.6),
                    Color.white
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            VStack(spacing: 0) {
                // Skip Button
                HStack {
                    Spacer()

                    Button("Atla") {
                        completeOnboarding()
                    }
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .padding(.horizontal, 20)
                    .padding(.top, 10)
                }

                // Page Content
                TabView(selection: $currentPage) {
                    ForEach(0..<pages.count, id: \.self) { index in
                        OnboardingPageView(page: pages[index])
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut, value: currentPage)

                // Bottom Section
                VStack(spacing: 24) {
                    // Page Indicator
                    HStack(spacing: 8) {
                        ForEach(0..<pages.count, id: \.self) { index in
                            Circle()
                                .fill(currentPage == index ? Color.white : Color.white.opacity(0.4))
                                .frame(width: 8, height: 8)
                                .scaleEffect(currentPage == index ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 0.3), value: currentPage)
                        }
                    }

                    // Action Buttons
                    VStack(spacing: 16) {
                        if currentPage == pages.count - 1 {
                            // Get Started Button
                            Button(action: {
                                completeOnboarding()
                            }) {
                                HStack {
                                    Text("Başlayalım")
                                        .font(.headline)
                                        .fontWeight(.semibold)

                                    Image(systemName: "arrow.right")
                                        .font(.headline)
                                }
                                .foregroundColor(.tazeBitPrimary)
                                .frame(maxWidth: .infinity)
                                .frame(height: 56)
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(Color.white)
                                        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        } else {
                            // Next Button
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.5)) {
                                    currentPage += 1
                                }
                            }) {
                                HStack {
                                    Text("Devam Et")
                                        .font(.headline)
                                        .fontWeight(.semibold)

                                    Image(systemName: "arrow.right")
                                        .font(.headline)
                                }
                                .foregroundColor(.tazeBitPrimary)
                                .frame(maxWidth: .infinity)
                                .frame(height: 56)
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(Color.white)
                                        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                .padding(.horizontal, 32)
                .padding(.bottom, 40)
            }
        }
        .fullScreenCover(isPresented: $showingLogin) {
            AuthView()
        }
    }

    private func completeOnboarding() {
        hasSeenOnboarding = true

        // Check if we're being shown from profile (user is already authenticated)
        // In that case, just dismiss the onboarding
        if SupabaseService.shared.isAuthenticated {
            dismiss()
        } else {
            // If user is not authenticated, show login
            showingLogin = true
        }
    }
}

struct OnboardingPageView: View {
    let page: OnboardingPage
    @State private var animationOffset: CGFloat = 0
    @State private var pulseScale: CGFloat = 1.0
    @State private var rotationAngle: Double = 0

    var body: some View {
        VStack(spacing: 40) {
            Spacer()

            // Animated Icon Section
            ZStack {
                // Background Circles with Animation
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .stroke(Color.white.opacity(0.2), lineWidth: 2)
                        .frame(width: CGFloat(180 + index * 40), height: CGFloat(180 + index * 40))
                        .scaleEffect(pulseScale)
                        .animation(
                            .easeInOut(duration: 2.0 + Double(index) * 0.5)
                            .repeatForever(autoreverses: true),
                            value: pulseScale
                        )
                }

                // Main Icon Background
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.3),
                                Color.white.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 160, height: 160)
                    .shadow(color: Color.white.opacity(0.3), radius: 20, x: 0, y: 0)

                // Icon with Special Animation for AI Page
                if page.title.contains("Yapay Zeka") {
                    // AI Brain Animation
                    ZStack {
                        page.icon
                            .font(.system(size: 70))
                            .foregroundColor(.white)
                            .rotationEffect(.degrees(rotationAngle))

                        // Pulsing dots around brain
                        ForEach(0..<8, id: \.self) { index in
                            Circle()
                                .fill(Color.white.opacity(0.8))
                                .frame(width: 6, height: 6)
                                .offset(x: 50)
                                .rotationEffect(.degrees(Double(index) * 45 + rotationAngle))
                                .scaleEffect(pulseScale)
                        }
                    }
                } else {
                    page.icon
                        .font(.system(size: 70))
                        .foregroundColor(.white)
                        .offset(y: animationOffset)
                }
            }

            // Content Section
            VStack(spacing: 20) {
                Text(page.title)
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)

                Text(page.description)
                    .font(.system(size: 16, weight: .medium, design: .rounded))
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .padding(.horizontal, 20)
                    .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)

                // Special Features for AI Page
                if page.title.contains("Yapay Zeka") {
                    VStack(spacing: 12) {
                        OnboardingFeatureRow(icon: "camera.fill", text: "Fotoğraf çek")
                        OnboardingFeatureRow(icon: "brain.head.profile", text: "AI analiz etsin")
                        OnboardingFeatureRow(icon: "plus.circle.fill", text: "Otomatik eklesin")
                    }
                    .padding(.top, 16)
                }
            }

            Spacer()
        }
        .padding(.horizontal, 32)
        .onAppear {
            startAnimations()
        }
    }

    private func startAnimations() {
        // Floating animation
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            animationOffset = -10
        }

        // Pulse animation
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            pulseScale = 1.1
        }

        // Rotation for AI brain
        if page.title.contains("Yapay Zeka") {
            withAnimation(.linear(duration: 8.0).repeatForever(autoreverses: false)) {
                rotationAngle = 360
            }
        }
    }
}

struct OnboardingFeatureRow: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 24, height: 24)

            Text(text)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.9))

            Spacer()
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - Onboarding Data Model
struct OnboardingPage {
    let title: String
    let description: String
    let icon: Image

    static let allPages: [OnboardingPage] = [
        OnboardingPage(
            title: "TazeBit'e Hoş Geldiniz! 🌱",
            description: "Gıda israfını önleyin, son kullanma tarihlerini takip edin ve akıllı önerilerle mutfağınızı verimli kullanın. Çevre dostu yaşamın ilk adımı!",
            icon: Image(systemName: "leaf.fill")
        ),
        OnboardingPage(
            title: "🤖 Yapay Zeka Destekli",
            description: "Buzdolabınızın fotoğrafını çekin, yapay zeka otomatik olarak ürünleri tespit etsin ve listelerinize eklesin. Artık tek tek ürün girmeye gerek yok!",
            icon: Image(systemName: "brain.head.profile")
        ),
        OnboardingPage(
            title: "📱 Akıllı Bildirimler",
            description: "Son kullanma tarihleri yaklaşan ürünler için zamanında bildirim alın. Hangi ürünlerin ne kadar tüketildiğini detaylı takip edin.",
            icon: Image(systemName: "bell.badge.fill")
        ),
        OnboardingPage(
            title: "👨‍👩‍👧‍👦 Aile Paylaşımı",
            description: "Ev halkınızla ürünlerinizi paylaşın. Herkes ürün ekleyebilir, tüketim kaydedebilir ve ortak alışveriş listesi oluşturabilir.",
            icon: Image(systemName: "house.fill")
        ),
        OnboardingPage(
            title: "🍳 AI Tarif Önerileri",
            description: "Son kullanma tarihi yaklaşan ürünlerinize göre yapay zeka destekli tarif önerileri alın. Hiçbir şey ziyan olmasın, lezzetli yemekler yapın!",
            icon: Image(systemName: "sparkles")
        )
    ]
}

#Preview {
    OnboardingView()
}
