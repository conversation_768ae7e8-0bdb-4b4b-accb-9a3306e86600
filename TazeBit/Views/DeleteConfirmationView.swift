//
//  DeleteConfirmationView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 27.05.2025.
//

import SwiftUI

struct DeleteConfirmationView: View {
    @EnvironmentObject var productViewModel: ProductViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 24) {
                            if let product = productViewModel.productToDelete {
                                // Product Info Card
                                productInfoCard(product)

                                // Warning Section
                                warningSection

                                // Action Buttons
                                actionButtons
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }

    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.red)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.red

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    Text("Ürünü Sil")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    // Balance space
                    Color.clear
                        .frame(width: 32, height: 32)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    private func productInfoCard(_ product: Product) -> some View {
        VStack(spacing: 16) {
            // Warning Icon
            ZStack {
                Circle()
                    .fill(Color.red.opacity(0.1))
                    .frame(width: 80, height: 80)

                Image(systemName: "trash.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.red)
            }

            VStack(spacing: 8) {
                Text("Bu ürünü silmek istediğinizden emin misiniz?")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                    .multilineTextAlignment(.center)

                Text("Bu işlem geri alınamaz.")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }

            // Product Details
            VStack(spacing: 12) {
                HStack {
                    // Category Icon
                    Text(categoryIcon(for: product))
                        .font(.title2)

                    VStack(alignment: .leading, spacing: 4) {
                        Text(product.name)
                            .font(.headline)
                            .fontWeight(.medium)
                            .foregroundColor(.textPrimary)

                        if let category = ProductCategory(rawValue: product.category ?? "other") {
                            Text(category.displayName)
                                .font(.caption)
                                .foregroundColor(.textSecondary)
                        }

                        Text(product.quantityWithUnit)
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                    }

                    Spacer()

                    // Expiry Status
                    VStack(alignment: .trailing, spacing: 4) {
                        Text(expiryText(for: product))
                            .font(.caption)
                            .foregroundColor(expiryColor(for: product))
                            .fontWeight(.medium)

                        Text(product.expiryStatus.displayName)
                            .font(.caption2)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(expiryColor(for: product).opacity(0.2))
                            .foregroundColor(expiryColor(for: product))
                            .cornerRadius(8)
                    }
                }
            }
            .padding(16)
            .background(Color.cardBackground)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.red.opacity(0.3), lineWidth: 1)
            )
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    private var warningSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.title3)
                    .foregroundColor(.orange)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Dikkat!")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)

                    Text("Silinen ürün kalıcı olarak kaldırılacak ve geri getirilemeyecektir.")
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)
                }

                Spacer()
            }
            .padding(16)
            .background(Color.orange.opacity(0.1))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.orange.opacity(0.3), lineWidth: 1)
            )
        }
    }

    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Delete Button
            Button(action: {
                Task {
                    await productViewModel.confirmDelete()
                    // Only dismiss if deletion was successful (no error message)
                    if productViewModel.errorMessage.isEmpty {
                        dismiss()
                    }
                }
            }) {
                HStack {
                    if productViewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: "trash.fill")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text(productViewModel.isLoading ? "Siliniyor..." : "Evet, Sil")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.red)
                )
            }
            .disabled(productViewModel.isLoading)

            // Cancel Button
            Button(action: {
                dismiss()
            }) {
                Text("İptal")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
            .disabled(productViewModel.isLoading)

            // Error Message
            if !productViewModel.errorMessage.isEmpty {
                Text(productViewModel.errorMessage)
                    .font(.subheadline)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding(.top, 8)
            }
        }
        .padding(.bottom, 20)
    }

    // Helper functions
    private func categoryIcon(for product: Product) -> String {
        if let category = ProductCategory(rawValue: product.category ?? "other") {
            return category.icon
        }
        return "📦"
    }

    private func expiryText(for product: Product) -> String {
        let days = product.daysUntilExpiry

        if days < 0 {
            return "\(-days) gün önce süresi geçti"
        } else if days == 0 {
            return "Bugün süresi bitiyor"
        } else if days == 1 {
            return "Yarın süresi bitiyor"
        } else {
            return "\(days) gün kaldı"
        }
    }

    private func expiryColor(for product: Product) -> Color {
        switch product.expiryStatus {
        case .expired:
            return .red
        case .expiringSoon:
            return .orange
        case .expiringThisWeek:
            return .yellow
        case .fresh:
            return .green
        case .consumed:
            return .blue
        }
    }
}

#Preview {
    DeleteConfirmationView()
        .environmentObject(ProductViewModel())
}
