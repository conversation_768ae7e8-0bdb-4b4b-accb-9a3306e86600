//
//  SplashView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 27.05.2025.
//

import SwiftUI

struct SplashView: View {
    @State private var isLoading = true
    @State private var logoScale: CGFloat = 0.5
    @State private var logoOpacity: Double = 0.0
    @State private var textOpacity: Double = 0.0
    @State private var backgroundOpacity: Double = 0.0
    @State private var logoRotation: Double = 0.0
    @State private var accentOpacity: Double = 0.0
    @State private var glowIntensity: Double = 0.0

    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.gradientStart,
                    Color.tazeBitPrimary,
                    Color.gradientEnd
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .opacity(backgroundOpacity)
            .ignoresSafeArea()

            VStack(spacing: 30) {
                Spacer()

                // Logo/Icon area
                VStack(spacing: 20) {
                    // Animated logo with multiple elements
                    ZStack {
                        // Background circle with glow effect
                        Circle()
                            .fill(Color.white.opacity(0.15 * glowIntensity))
                            .frame(width: 140, height: 140)
                            .blur(radius: 10)

                        Circle()
                            .fill(Color.white.opacity(0.2))
                            .frame(width: 120, height: 120)

                        // Main icon with multiple layers
                        ZStack {
                            // Background leaf
                            Image(systemName: "leaf.fill")
                                .font(.system(size: 45, weight: .bold))
                                .foregroundColor(.white.opacity(0.3))
                                .offset(x: 3, y: 3)

                            // Main leaf
                            Image(systemName: "leaf.fill")
                                .font(.system(size: 45, weight: .bold))
                                .foregroundColor(.white)

                            // Small accent elements
                            HStack(spacing: 8) {
                                Circle()
                                    .fill(Color.white.opacity(0.8 * accentOpacity))
                                    .frame(width: 4, height: 4)
                                Circle()
                                    .fill(Color.white.opacity(0.6 * accentOpacity))
                                    .frame(width: 3, height: 3)
                                Circle()
                                    .fill(Color.white.opacity(0.4 * accentOpacity))
                                    .frame(width: 2, height: 2)
                            }
                            .offset(x: 25, y: -25)
                            .rotationEffect(.degrees(logoRotation))
                        }
                    }
                    .scaleEffect(logoScale)
                    .opacity(logoOpacity)

                    // App name
                    Text("TazeBit")
                        .font(.system(size: 42, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .opacity(textOpacity)

                    // Tagline
                    Text("Tazelik Zamanında, TazeBit Yanında!")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.9))
                        .opacity(textOpacity)
                }

                Spacer()

                // Loading indicator
                VStack(spacing: 15) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)

                    Text("Yükleniyor...")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                .opacity(textOpacity)

                Spacer()
                    .frame(height: 80)
            }
            .padding(.horizontal, 40)
        }
        .onAppear {
            startAnimations()
        }
    }

    private func startAnimations() {
        // Background fade in
        withAnimation(.easeInOut(duration: 0.5)) {
            backgroundOpacity = 1.0
        }

        // Logo animation with bounce effect
        withAnimation(.spring(response: 0.8, dampingFraction: 0.6, blendDuration: 0).delay(0.3)) {
            logoOpacity = 1.0
            logoScale = 1.0
        }

        // Glow effect
        withAnimation(.easeInOut(duration: 0.8).delay(0.5)) {
            glowIntensity = 1.0
        }

        // Accent elements fade in
        withAnimation(.easeInOut(duration: 0.6).delay(0.9)) {
            accentOpacity = 1.0
        }

        // Text fade in
        withAnimation(.easeInOut(duration: 0.6).delay(1.1)) {
            textOpacity = 1.0
        }

        // Continuous animations
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            // Logo pulse effect
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                logoScale = 1.05
            }

            // Glow pulse
            withAnimation(.easeInOut(duration: 3.0).repeatForever(autoreverses: true)) {
                glowIntensity = 0.5
            }

            // Accent rotation
            withAnimation(.linear(duration: 8.0).repeatForever(autoreverses: false)) {
                logoRotation = 360
            }
        }
    }
}

#Preview {
    SplashView()
}
