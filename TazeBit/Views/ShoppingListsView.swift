import SwiftUI

struct ShoppingListsView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @EnvironmentObject var productViewModel: ProductViewModel
    @StateObject private var shoppingListViewModel = ShoppingListViewModel()
    @State private var showingDeleteAlert = false
    @State private var listToDelete: ShoppingList?
    @State private var searchText = ""
    @State private var selectedFilter: ShoppingListStatus?

    // MARK: - Computed Properties
    private var filteredShoppingLists: [ShoppingList] {
        var lists = shoppingListViewModel.shoppingLists

        // Apply status filter
        if let selectedFilter = selectedFilter {
            lists = lists.filter { $0.status == selectedFilter }
        }

        // Apply search filter
        if !searchText.isEmpty {
            lists = lists.filter { list in
                list.name.localizedCaseInsensitiveContains(searchText)
            }
        }

        return lists
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 0) {
                        // Header Section
                        headerSection

                        // Content Area
                        VStack(spacing: 20) {
                            if householdViewModel.selectedHousehold != nil {
                                // Filter Buttons
                                filterButtons

                                // Shopping Lists
                                if shoppingListViewModel.isLoading && shoppingListViewModel.shoppingLists.isEmpty {
                                    loadingView
                                } else if filteredShoppingLists.isEmpty {
                                    emptyStateView
                                } else {
                                    shoppingListsContent
                                }
                            } else {
                                noHouseholdSelectedView
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
            .task {
                if let household = householdViewModel.selectedHousehold {
                    await shoppingListViewModel.loadShoppingLists(for: household.id)
                }
            }
            .onChange(of: householdViewModel.selectedHousehold) { _, newHousehold in
                if let household = newHousehold {
                    Task {
                        await shoppingListViewModel.loadShoppingLists(for: household.id)
                    }
                }
            }
            .sheet(isPresented: $shoppingListViewModel.showingAddList) {
                AddShoppingListView()
                    .environmentObject(shoppingListViewModel)
                    .environmentObject(householdViewModel)
            }
            .sheet(isPresented: $shoppingListViewModel.showingEditList) {
                EditShoppingListView()
                    .environmentObject(shoppingListViewModel)
            }
            .alert("Listeyi Sil", isPresented: $showingDeleteAlert) {
                Button("İptal", role: .cancel) { }
                Button("Sil", role: .destructive) {
                    if let list = listToDelete {
                        Task {
                            await shoppingListViewModel.deleteShoppingList(list)
                        }
                    }
                }
            } message: {
                Text("Bu alışveriş listesini silmek istediğinizden emin misiniz?")
            }
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        ZStack {
            // Primary Color Background
            Color.tazeBitPrimary
                .frame(height: 200)
                .clipShape(
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 30,
                        bottomTrailingRadius: 30,
                        topTrailingRadius: 0
                    )
                )
                .ignoresSafeArea(.all)

            VStack(spacing: 16) {
                // Top Section
                ZStack {
                    // Center Title
                    VStack(spacing: 4) {
                        Text("Alışveriş Listeleri 🛒")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        if let household = householdViewModel.selectedHousehold {
                            Text("\(household.name) - \(filteredShoppingLists.count) liste")
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.9))
                        } else {
                            Text("Ev seçilmedi")
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.9))
                        }
                    }

                    // Right Action Button
                    HStack {
                        Spacer()

                        Button(action: {
                            shoppingListViewModel.showAddList()
                        }) {
                            ZStack {
                                // Outer transparent circle
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                                    .frame(width: 44, height: 44)

                                // Inner solid circle
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: 32, height: 32)

                                // Icon
                                Image(systemName: "plus")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.tazeBitPrimary)
                            }
                        }
                        .disabled(householdViewModel.selectedHousehold == nil)
                        .opacity(householdViewModel.selectedHousehold == nil ? 0.5 : 1.0)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)

                // Search Bar
                if householdViewModel.selectedHousehold != nil {
                    searchBar
                        .padding(.horizontal, 20)
                }
            }
            .padding(.bottom, 20)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Search Bar
    private var searchBar: some View {
        HStack(spacing: 12) {
            // Search Icon
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.7))

            // Text Field
            TextField("Liste adı ile ara...", text: $searchText)
                .font(.subheadline)
                .foregroundColor(.white)
                .accentColor(.white)
                .placeholder(when: searchText.isEmpty) {
                    Text("Liste adı ile ara...")
                        .font(.subheadline)
                        .foregroundColor(.white)
                }

            // Clear Button
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.2))
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
        )
    }

    // MARK: - Filter Buttons
    private var filterButtons: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "line.3.horizontal.decrease.circle.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Filtreler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    FilterButton(
                        title: "Tümü",
                        count: shoppingListViewModel.shoppingLists.count,
                        isSelected: selectedFilter == nil
                    ) {
                        selectedFilter = nil
                    }

                    ForEach(ShoppingListStatus.allCases, id: \.self) { status in
                        let count = shoppingListViewModel.shoppingLists.filter { $0.status == status }.count

                        FilterButton(
                            title: status.displayName,
                            count: count,
                            isSelected: selectedFilter == status
                        ) {
                            selectedFilter = status
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Shopping Lists Content
    private var shoppingListsContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section Header
            HStack {
                Text("Alışveriş Listeleri")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            // Shopping Lists
            LazyVStack(spacing: 12) {
                ForEach(filteredShoppingLists) { list in
                    NavigationLink(destination: ShoppingListDetailView(shoppingList: list)
                        .environmentObject(shoppingListViewModel)
                        .environmentObject(productViewModel)
                        .environmentObject(householdViewModel)) {
                        ShoppingListCardView(
                            shoppingList: list,
                            items: shoppingListViewModel.getItems(for: list.id),
                            onEdit: {
                                shoppingListViewModel.editList(list)
                            },
                            onDelete: {
                                listToDelete = list
                                showingDeleteAlert = true
                            },
                            onToggleCompleted: {
                                Task {
                                    await shoppingListViewModel.toggleListCompleted(list)
                                }
                            }
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }

    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.tazeBitPrimary)

            Text("Alışveriş listeleri yükleniyor...")
                .font(.subheadline)
                .foregroundColor(.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            // Icon
            ZStack {
                Circle()
                    .fill(Color.tazeBitPrimary.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: searchText.isEmpty ? "cart.fill" : "magnifyingglass")
                    .font(.system(size: 50))
                    .foregroundColor(.tazeBitPrimary)
            }

            // Text Content
            VStack(spacing: 8) {
                Text(searchText.isEmpty ? "Henüz alışveriş listesi yok" : "Arama sonucu bulunamadı")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text(searchText.isEmpty ? "İlk alışveriş listenizi oluşturarak başlayın" : "Farklı anahtar kelimeler deneyin")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }

            // Action Button
            if searchText.isEmpty {
                Button(action: {
                    shoppingListViewModel.showAddList()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "plus.circle.fill")
                            .font(.headline)

                        Text("İlk Listeyi Oluştur")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient.tazeBitGradient
                            .cornerRadius(25)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }

    // MARK: - No Household Selected View
    private var noHouseholdSelectedView: some View {
        VStack(spacing: 24) {
            // Icon
            ZStack {
                Circle()
                    .fill(Color.tazeBitPrimary.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: "house.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.tazeBitPrimary)
            }

            // Text Content
            VStack(spacing: 8) {
                Text("Ev Seçilmedi")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text("Alışveriş listelerini görüntülemek için önce bir ev seçin")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }
}

#Preview {
    ShoppingListsView()
        .environmentObject(HouseholdViewModel())
}
