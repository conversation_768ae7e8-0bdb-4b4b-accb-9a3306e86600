//
//  EditShoppingListView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

struct EditShoppingListView: View {
    @EnvironmentObject var shoppingListViewModel: ShoppingListViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isNameFieldFocused: Bool
    
    @State private var name: String = ""
    @State private var description: String = ""
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content Section
                    ScrollView {
                        VStack(spacing: 24) {
                            // Current Shopping List Info
                            if let list = shoppingListViewModel.selectedList {
                                currentListCard(list)
                            }

                            // Edit Form
                            editForm

                            // Action Buttons
                            actionButtons
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
        .onAppear {
            // Initialize form with current list data
            if let list = shoppingListViewModel.selectedList {
                name = list.name
                description = list.description ?? ""
            }
            
            // Focus on name field when view appears
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isNameFieldFocused = true
            }
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)

            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary

                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }

                    Spacer()

                    // Title
                    Text("Listeyi Düzenle")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    // Update Button
                    Button(action: {
                        updateShoppingList()
                    }) {
                        if shoppingListViewModel.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                                .frame(width: 32, height: 32)
                        } else {
                            Image(systemName: "checkmark")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(Color.white.opacity(0.2))
                                )
                        }
                    }
                    .disabled(shoppingListViewModel.isLoading || name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Current List Card
    private func currentListCard(_ list: ShoppingList) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "cart.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Mevcut Liste")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)

                    Text("Düzenlemek istediğiniz liste bilgileri")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }

                Spacer()
            }

            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Liste Adı:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    Spacer()

                    Text(list.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)
                }

                if let description = list.description, !description.isEmpty {
                    HStack {
                        Text("Açıklama:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.textSecondary)

                        Spacer()

                        Text(description)
                            .font(.subheadline)
                            .foregroundColor(.textPrimary)
                            .multilineTextAlignment(.trailing)
                    }
                }

                HStack {
                    Text("Durum:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    Spacer()

                    Text(list.isCompleted ? "Tamamlandı" : "Devam Ediyor")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(list.isCompleted ? .green : .orange)
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Edit Form
    private var editForm: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "pencil.circle.fill")
                    .font(.title2)
                    .foregroundColor(.tazeBitPrimary)

                Text("Liste Bilgilerini Düzenle")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()
            }

            VStack(spacing: 16) {
                // List Name Field
                VStack(alignment: .leading, spacing: 8) {
                    Text("Liste Adı")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    TextField("Liste adını girin", text: $name)
                        .textFieldStyle(CustomTextFieldStyle())
                        .focused($isNameFieldFocused)
                        .submitLabel(.next)
                }

                // List Description Field
                VStack(alignment: .leading, spacing: 8) {
                    Text("Açıklama (İsteğe bağlı)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textSecondary)

                    TextField("Liste açıklamasını girin", text: $description, axis: .vertical)
                        .textFieldStyle(CustomTextFieldStyle())
                        .lineLimit(3...6)
                        .submitLabel(.done)
                }
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - Action Buttons
    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Update Button
            Button(action: {
                updateShoppingList()
            }) {
                HStack {
                    if shoppingListViewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.white)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text(shoppingListViewModel.isLoading ? "Güncelleniyor..." : "Listeyi Güncelle")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.tazeBitPrimary,
                                    Color.tazeBitPrimary.opacity(0.8)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
            }
            .disabled(shoppingListViewModel.isLoading || name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)

            // Cancel Button
            Button(action: {
                dismiss()
            }) {
                Text("İptal")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.cardBackground)
                            .stroke(Color.borderColor, lineWidth: 1)
                    )
            }
        }
    }

    // MARK: - Actions
    private func updateShoppingList() {
        guard let list = shoppingListViewModel.selectedList else { return }
        
        Task {
            await shoppingListViewModel.updateShoppingList(
                list,
                name: name.trimmingCharacters(in: .whitespacesAndNewlines),
                description: description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : description.trimmingCharacters(in: .whitespacesAndNewlines)
            )
            
            if shoppingListViewModel.errorMessage == nil {
                dismiss()
            }
        }
    }
}

#Preview {
    EditShoppingListView()
        .environmentObject(ShoppingListViewModel())
}
