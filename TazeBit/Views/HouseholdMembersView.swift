//
//  HouseholdMembersView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct HouseholdMembersView: View {
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header Section
                    headerSection
                    
                    // Content Section
                    ScrollView {
                        VStack(spacing: 20) {
                            if let household = householdViewModel.selectedHousehold {
                                // Household Info Card
                                householdInfoCard(household)
                                
                                // Members Section
                                membersSection
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 0) {
            // Status bar space
            Rectangle()
                .fill(Color.tazeBitPrimary)
                .frame(height: 0)
                .ignoresSafeArea(.all, edges: .top)
            
            // Header content
            ZStack {
                // Background
                Color.tazeBitPrimary
                
                HStack {
                    // Close Button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                            )
                    }
                    
                    Spacer()
                    
                    // Title
                    Text("Ev Üyeleri")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // Balance space
                    Color.clear
                        .frame(width: 32, height: 32)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(height: 80)
        }
        .ignoresSafeArea(.all)
    }
    
    private func householdInfoCard(_ household: Household) -> some View {
        VStack(spacing: 16) {
            HStack {
                // House Icon
                ZStack {
                    Circle()
                        .fill(Color.tazeBitPrimary.opacity(0.1))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: "house.fill")
                        .font(.title2)
                        .foregroundColor(.tazeBitPrimary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(household.name)
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.textPrimary)
                    
                    if let description = household.description, !description.isEmpty {
                        Text(description)
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                    }
                    
                    Text("Davet Kodu: \(household.inviteCode)")
                        .font(.caption)
                        .foregroundColor(.tazeBitPrimary)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                // Member Count
                VStack(spacing: 4) {
                    Text("\(householdViewModel.householdMembers.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.tazeBitPrimary)
                    
                    Text("Üye")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }
            .padding(20)
            .background(Color.cardBackground)
            .cornerRadius(16)
            .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
        }
    }
    
    private var membersSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section Header
            HStack {
                Text("Üyeler")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                if householdViewModel.isLoadingMembers {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(.tazeBitPrimary)
                }
            }
            
            // Members List
            if householdViewModel.isLoadingMembers {
                loadingView
            } else if householdViewModel.householdMembers.isEmpty {
                emptyMembersView
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(householdViewModel.householdMembers) { member in
                        MemberCard(member: member)
                    }
                }
            }
        }
    }
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.tazeBitPrimary)
            
            Text("Üyeler yükleniyor...")
                .font(.subheadline)
                .foregroundColor(.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }
    
    private var emptyMembersView: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.2.slash")
                .font(.system(size: 40))
                .foregroundColor(.textSecondary)
            
            Text("Üye bulunamadı")
                .font(.subheadline)
                .foregroundColor(.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }
}

struct MemberCard: View {
    let member: HouseholdMember
    
    var body: some View {
        HStack(spacing: 16) {
            // Avatar
            ZStack {
                Circle()
                    .fill(member.role == .owner ? Color.tazeBitPrimary : Color.tazeBitSecondary.opacity(0.3))
                    .frame(width: 50, height: 50)
                
                Text(member.user.fullName.prefix(1).uppercased())
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(member.role == .owner ? .white : .tazeBitSecondary)
            }
            
            // User Info
            VStack(alignment: .leading, spacing: 4) {
                Text(member.user.fullName)
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)
                
                Text(member.user.email)
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                
                Text("Katılma: \(member.joinedAt, style: .date)")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            Spacer()
            
            // Role Badge
            HStack(spacing: 6) {
                Image(systemName: member.role == .owner ? "crown.fill" : "person.fill")
                    .font(.caption)
                    .foregroundColor(member.role == .owner ? .orange : .tazeBitSecondary)
                
                Text(member.role.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(member.role == .owner ? .orange : .tazeBitSecondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(member.role == .owner ? Color.orange.opacity(0.1) : Color.tazeBitSecondary.opacity(0.1))
            )
        }
        .padding(16)
        .background(Color.cardBackground)
        .cornerRadius(12)
        .shadow(color: Color.cardShadow, radius: 2, x: 0, y: 1)
    }
}

#Preview {
    HouseholdMembersView()
        .environmentObject(HouseholdViewModel())
}
