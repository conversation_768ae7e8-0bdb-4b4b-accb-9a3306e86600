import SwiftUI

struct ShoppingListDetailView: View {
    let shoppingList: ShoppingList
    @EnvironmentObject var shoppingListViewModel: ShoppingListViewModel
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var householdViewModel: HouseholdViewModel
    @Environment(\.dismiss) private var dismiss

    @State private var showingDeleteAlert = false
    @State private var itemToDelete: ShoppingListItem?
    @State private var showingAddToHouse = false
    @State private var itemToAddToHouse: ShoppingListItem?

    var body: some View {
        ZStack {
            // Background
            Color.appBackground
                .ignoresSafeArea()

            ScrollView {
                VStack(spacing: 0) {
                    // Header Section
                    headerSection

                    // Content sections
                    VStack(spacing: 20) {
                        if shoppingListViewModel.isLoading && shoppingListViewModel.currentItems.isEmpty {
                            loadingView
                        } else if shoppingListViewModel.currentItems.isEmpty {
                            emptyStateView
                        } else {
                            itemsSections
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
        }
        .navigationBarHidden(true)
        .ignoresSafeArea(.all, edges: .top)
        .task {
            await shoppingListViewModel.loadShoppingListItems(for: shoppingList.id)
        }
        .sheet(isPresented: $shoppingListViewModel.showingAddItem) {
            AddShoppingItemView(shoppingListId: shoppingList.id)
                .environmentObject(shoppingListViewModel)
        }
        .sheet(isPresented: $shoppingListViewModel.showingEditItem) {
            EditShoppingItemView()
                .environmentObject(shoppingListViewModel)
        }
        .sheet(isPresented: $showingAddToHouse) {
            if let item = itemToAddToHouse {
                AddToHouseView(shoppingItem: item)
                    .environmentObject(productViewModel)
                    .environmentObject(householdViewModel)
                    .environmentObject(shoppingListViewModel)
            }
        }
        .alert("Ürünü Sil", isPresented: $showingDeleteAlert) {
            Button("İptal", role: .cancel) { }
            Button("Sil", role: .destructive) {
                if let item = itemToDelete {
                    Task {
                        await shoppingListViewModel.deleteShoppingListItem(item)
                    }
                }
            }
        } message: {
            Text("Bu ürünü listeden silmek istediğinizden emin misiniz?")
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        ZStack {
            // Primary Color Background
            Color.tazeBitPrimary
                .frame(height: 200)
                .clipShape(
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 30,
                        bottomTrailingRadius: 30,
                        topTrailingRadius: 0
                    )
                )
                .ignoresSafeArea(.all)

            VStack(spacing: 16) {
                // Top Section
                ZStack {
                    // Back Button
                    HStack {
                        Button(action: {
                            dismiss()
                        }) {
                            ZStack {
                                // Outer transparent circle
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                                    .frame(width: 44, height: 44)

                                // Inner solid circle
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: 32, height: 32)

                                // Icon
                                Image(systemName: "chevron.left")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.tazeBitPrimary)
                            }
                        }

                        Spacer()
                    }

                    // Center Title
                    VStack(spacing: 4) {
                        Text(shoppingList.name)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .lineLimit(2)
                            .multilineTextAlignment(.center)

                        if let description = shoppingList.description, !description.isEmpty {
                            Text(description)
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.9))
                                .lineLimit(2)
                                .multilineTextAlignment(.center)
                        }
                    }

                    // Add Button
                    HStack {
                        Spacer()

                        Button(action: {
                            shoppingListViewModel.showAddItem()
                        }) {
                            ZStack {
                                // Outer transparent circle
                                Circle()
                                    .fill(Color.white.opacity(0.2))
                                    .frame(width: 44, height: 44)

                                // Inner solid circle
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: 32, height: 32)

                                // Icon
                                Image(systemName: "plus")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.tazeBitPrimary)
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)

                // Progress Section
                progressSection
            }
            .padding(.bottom, 20)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Progress Section
    private var progressSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("İlerleme")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Spacer()

                Text("\(shoppingListViewModel.purchasedItems.count)/\(shoppingListViewModel.currentItems.count) ürün")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.9))
            }

            // Progress Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.white.opacity(0.3))
                        .frame(height: 8)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(Color.white)
                        .frame(width: geometry.size.width * (shoppingListViewModel.completionPercentage / 100), height: 8)
                        .cornerRadius(4)
                        .animation(.easeInOut(duration: 0.3), value: shoppingListViewModel.completionPercentage)
                }
            }
            .frame(height: 8)

            // Percentage
            HStack {
                Spacer()
                Text("\(Int(shoppingListViewModel.completionPercentage))% tamamlandı")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Items Sections
    private var itemsSections: some View {
        VStack(spacing: 20) {
            // Pending Items
            if !shoppingListViewModel.pendingItems.isEmpty {
                itemSection(
                    title: "Alınacaklar",
                    items: shoppingListViewModel.pendingItems,
                    icon: "cart",
                    color: .orange
                )
            }

            // Purchased Items
            if !shoppingListViewModel.purchasedItems.isEmpty {
                itemSection(
                    title: "Alınanlar",
                    items: shoppingListViewModel.purchasedItems,
                    icon: "checkmark.circle.fill",
                    color: .green
                )
            }
        }
    }

    // MARK: - Item Section
    private func itemSection(title: String, items: [ShoppingListItem], icon: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // Section Header
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("\(items.count)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(color.opacity(0.1))
                    .cornerRadius(8)
            }

            // Items
            ForEach(items) { item in
                ShoppingListItemRowView(
                    item: item,
                    onToggle: {
                        Task {
                            await shoppingListViewModel.toggleItemPurchased(item)
                        }
                    },
                    onEdit: {
                        shoppingListViewModel.editItem(item)
                    },
                    onDelete: {
                        itemToDelete = item
                        showingDeleteAlert = true
                    },
                    onAddToHouse: item.isPurchased ? {
                        itemToAddToHouse = item
                        showingAddToHouse = true
                    } : nil
                )
                .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
            }
        }
    }

    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.tazeBitPrimary)

            Text("Ürünler yükleniyor...")
                .font(.subheadline)
                .foregroundColor(.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }

    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            // Icon
            ZStack {
                Circle()
                    .fill(Color.tazeBitPrimary.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: "list.bullet")
                    .font(.system(size: 50))
                    .foregroundColor(.tazeBitPrimary)
            }

            // Text Content
            VStack(spacing: 8) {
                Text("Liste boş")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)

                Text("İlk ürününüzü ekleyerek başlayın")
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }

            // Action Button
            Button(action: {
                shoppingListViewModel.showAddItem()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                        .font(.headline)

                    Text("İlk Ürünü Ekle")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    LinearGradient.tazeBitGradient
                        .cornerRadius(25)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }
}

#Preview {
    ShoppingListDetailView(
        shoppingList: ShoppingList(
            id: UUID(),
            name: "Haftalık Alışveriş",
            description: "Bu hafta için gerekli olan tüm ürünler",
            householdId: UUID(),
            createdBy: UUID(),
            isCompleted: false,
            createdAt: Date(),
            updatedAt: Date()
        )
    )
    .environmentObject(ShoppingListViewModel())
    .environmentObject(ProductViewModel())
    .environmentObject(HouseholdViewModel())
}
