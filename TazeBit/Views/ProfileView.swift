//
//  ProfileView.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var notificationManager: NotificationManager
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var trackingManager: TrackingManager
    @Environment(\.dismiss) private var dismiss
    @State private var showingNotificationSettings = false
    @State private var showingAbout = false
    @State private var showingPrivacy = false
    @State private var showingOnboarding = false
    @State private var showingUserGuide = false

    @State private var showingDeleteAccount = false
    @AppStorage("hasSeenOnboarding") private var hasSeenOnboarding = false

    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color.tazeBitBackground
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 0) {
                        // Header Section
                        headerSection

                        // Content sections
                        VStack(spacing: 20) {
                            // User Info Card
                            if let user = authViewModel.currentUser {
                                userInfoCard(user: user)
                            }

                            // App Info Section
                            appInfoSection

                            // Settings Section
                            settingsSection

                            // Sign Out Section
                            signOutSection
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
            .navigationBarHidden(true)
            .ignoresSafeArea(.all, edges: .top)
            .sheet(isPresented: $showingNotificationSettings) {
                NotificationSettingsView()
                    .environmentObject(notificationManager)
                    .environmentObject(productViewModel)
            }
            .sheet(isPresented: $showingAbout) {
                AboutView()
            }
            .sheet(isPresented: $showingPrivacy) {
                PrivacyView()
            }
            .fullScreenCover(isPresented: $showingOnboarding) {
                OnboardingView()
            }
            .sheet(isPresented: $showingUserGuide) {
                UserGuideView()
            }

            .sheet(isPresented: $showingDeleteAccount) {
                DeleteAccountView(isPresented: $showingDeleteAccount)
                    .environmentObject(authViewModel)
            }
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        ZStack {
            // Gradient Background
            LinearGradient.tazeBitGradient
                .frame(height: 200)
                .clipShape(
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 30,
                        bottomTrailingRadius: 30,
                        topTrailingRadius: 0
                    )
                )
                .ignoresSafeArea(.all)

            VStack(spacing: 0) {
                // Top Section
                HStack {
                    Spacer()

                    // Center Title
                    VStack(spacing: 4) {
                        Text("Profil 👤")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        if let user = authViewModel.currentUser {
                            Text(user.fullName)
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.9))
                        }
                    }

                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)
                .padding(.bottom, 30)
            }
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - User Info Card
    private func userInfoCard(user: User) -> some View {
        VStack(spacing: 16) {
            HStack(spacing: 16) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.tazeBitPrimary)

                VStack(alignment: .leading, spacing: 4) {
                    Text(user.fullName)
                        .font(.headline)
                        .foregroundColor(.textPrimary)

                    Text(user.email)
                        .font(.subheadline)
                        .foregroundColor(.textSecondary)

                    Text("Üyelik: \(user.createdAt, style: .date)")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }

                Spacer()
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
    }

    // MARK: - App Info Section
    private var appInfoSection: some View {
        VStack(spacing: 12) {
            ProfileMenuItem(
                icon: "leaf.circle.fill",
                title: "TazeBit",
                subtitle: "v1.0.0",
                iconColor: .tazeBitPrimary,
                action: {}
            )
        }
    }

    // MARK: - Settings Section
    private var settingsSection: some View {
        VStack(spacing: 12) {
            ProfileMenuItem(
                icon: notificationManager.isAuthorized ? "bell" : "bell.slash",
                title: "Bildirimler",
                subtitle: notificationManager.isAuthorized ? "Aktif" : "Kapalı",
                iconColor: notificationManager.isAuthorized ? .orange : .gray,
                showChevron: true,
                action: {
                    showingNotificationSettings = true
                }
            )

            if #available(iOS 14.5, *) {
                ProfileMenuItem(
                    icon: trackingManager.isTrackingAuthorized ? "person.crop.circle.badge.checkmark" : "person.crop.circle.badge.xmark",
                    title: "Kişiselleştirme",
                    subtitle: trackingManager.isTrackingAuthorized ? "İzin Verildi" : "İzin Verilmedi",
                    iconColor: trackingManager.isTrackingAuthorized ? Color("TazeBitPrimary") : .gray,
                    showChevron: true,
                    action: {
                        Task {
                            await trackingManager.showTrackingDialog()
                        }
                    }
                )
            }

            ProfileMenuItem(
                icon: "info.circle",
                title: "Hakkında",
                iconColor: .blue,
                showChevron: true,
                action: {
                    showingAbout = true
                }
            )

            ProfileMenuItem(
                icon: "lock",
                title: "Gizlilik",
                iconColor: .gray,
                showChevron: true,
                action: {
                    showingPrivacy = true
                }
            )

            ProfileMenuItem(
                icon: "book.fill",
                title: "Kullanım Klavuzu",
                subtitle: "Detaylı kullanım kılavuzu ve ipuçları",
                iconColor: .blue,
                showChevron: true,
                action: {
                    showingUserGuide = true
                }
            )

            ProfileMenuItem(
                icon: "play.circle",
                title: "Tanıtımı Tekrar Göster",
                subtitle: "Uygulamanın özelliklerini tekrar keşfet",
                iconColor: .tazeBitPrimary,
                showChevron: true,
                action: {
                    showingOnboarding = true
                }
            )
        }
    }

    // MARK: - Sign Out Section
    private var signOutSection: some View {
        VStack(spacing: 12) {
            // Delete Account Button
            Button(action: {
                showingDeleteAccount = true
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "trash")
                        .font(.title3)
                        .foregroundColor(.red)

                    Text("Hesabı Sil")
                        .font(.headline)
                        .foregroundColor(.red)

                    Spacer()
                }
                .padding(20)
                .background(Color.cardBackground)
                .cornerRadius(16)
                .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
            }
            .disabled(authViewModel.isLoading)

            // Sign Out Button
            Button(action: {
                Task {
                    await authViewModel.signOut()
                }
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .font(.title3)
                        .foregroundColor(.red)

                    Text("Çıkış Yap")
                        .font(.headline)
                        .foregroundColor(.red)

                    Spacer()
                }
                .padding(20)
                .background(Color.cardBackground)
                .cornerRadius(16)
                .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
            }
            .disabled(authViewModel.isLoading)
        }
    }
}

// MARK: - Supporting Views

struct ProfileMenuItem: View {
    let icon: String
    let title: String
    let subtitle: String?
    let iconColor: Color
    let showChevron: Bool
    let action: () -> Void

    init(
        icon: String,
        title: String,
        subtitle: String? = nil,
        iconColor: Color,
        showChevron: Bool = false,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.iconColor = iconColor
        self.showChevron = showChevron
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(iconColor)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.textPrimary)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }

                Spacer()

                if showChevron {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }
            .padding(20)
            .background(Color.cardBackground)
            .cornerRadius(16)
            .shadow(color: Color.cardShadow, radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ProfileView()
        .environmentObject(AuthViewModel())
        .environmentObject(NotificationManager.shared)
        .environmentObject(ProductViewModel())
}
