//
//  Recipe.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 29.05.2025.
//

import Foundation

// MARK: - Recipe Models

struct Recipe: Codable, Identifiable, Equatable {
    let id: UUID
    let name: String
    let difficulty: RecipeDifficulty
    let preparationTime: Int // minutes
    let cookingTime: Int // minutes
    let servings: Int
    let ingredients: [RecipeIngredient]
    let instructions: [String]
    let tips: String?
    let cuisine: CuisineType
    let tags: [String]
    let createdAt: Date
    let isFavorite: Bool

    init(
        id: UUID = UUID(),
        name: String,
        difficulty: RecipeDifficulty,
        preparationTime: Int,
        cookingTime: Int,
        servings: Int = 4,
        ingredients: [RecipeIngredient],
        instructions: [String],
        tips: String? = nil,
        cuisine: CuisineType = .turkish,
        tags: [String] = [],
        createdAt: Date = Date(),
        isFavorite: Bool = false
    ) {
        self.id = id
        self.name = name
        self.difficulty = difficulty
        self.preparationTime = preparationTime
        self.cookingTime = cookingTime
        self.servings = servings
        self.ingredients = ingredients
        self.instructions = instructions
        self.tips = tips
        self.cuisine = cuisine
        self.tags = tags
        self.createdAt = createdAt
        self.isFavorite = isFavorite
    }

    var totalTime: Int {
        return preparationTime + cookingTime
    }

    var totalTimeFormatted: String {
        let hours = totalTime / 60
        let minutes = totalTime % 60

        if hours > 0 {
            return "\(hours) saat \(minutes) dakika"
        } else {
            return "\(minutes) dakika"
        }
    }

    var missingIngredients: [RecipeIngredient] {
        return ingredients.filter { !$0.isAvailable }
    }

    var availableIngredients: [RecipeIngredient] {
        return ingredients.filter { $0.isAvailable }
    }

    var missingIngredientsCount: Int {
        return missingIngredients.count
    }

    var availableIngredientsCount: Int {
        return availableIngredients.count
    }

    var ingredientAvailabilityPercentage: Double {
        guard !ingredients.isEmpty else { return 0 }
        return Double(availableIngredientsCount) / Double(ingredients.count)
    }
}

struct RecipeIngredient: Codable, Identifiable, Equatable {
    let id: UUID
    let name: String
    let amount: String
    let unit: String?
    let isOptional: Bool
    let isAvailable: Bool // User'ın evinde var mı?

    init(
        id: UUID = UUID(),
        name: String,
        amount: String,
        unit: String? = nil,
        isOptional: Bool = false,
        isAvailable: Bool = false
    ) {
        self.id = id
        self.name = name
        self.amount = amount
        self.unit = unit
        self.isOptional = isOptional
        self.isAvailable = isAvailable
    }

    var displayText: String {
        if let unit = unit {
            return "\(amount) \(unit) \(name)"
        } else {
            return "\(amount) \(name)"
        }
    }
}

enum RecipeDifficulty: String, Codable, CaseIterable {
    case easy = "easy"
    case medium = "medium"
    case hard = "hard"

    var displayName: String {
        switch self {
        case .easy:
            return "Kolay"
        case .medium:
            return "Orta"
        case .hard:
            return "Zor"
        }
    }

    var emoji: String {
        switch self {
        case .easy:
            return "😊"
        case .medium:
            return "🤔"
        case .hard:
            return "😰"
        }
    }
}

enum CuisineType: String, Codable, CaseIterable {
    case turkish = "turkish"
    case mediterranean = "mediterranean"
    case asian = "asian"
    case european = "european"
    case american = "american"
    case vegetarian = "vegetarian"
    case vegan = "vegan"

    var displayName: String {
        switch self {
        case .turkish:
            return "Türk Mutfağı"
        case .mediterranean:
            return "Akdeniz Mutfağı"
        case .asian:
            return "Asya Mutfağı"
        case .european:
            return "Avrupa Mutfağı"
        case .american:
            return "Amerikan Mutfağı"
        case .vegetarian:
            return "Vejetaryen"
        case .vegan:
            return "Vegan"
        }
    }

    var emoji: String {
        switch self {
        case .turkish:
            return "🍽️"
        case .mediterranean:
            return "🫒"
        case .asian:
            return "🥢"
        case .european:
            return "🍝"
        case .american:
            return "🍔"
        case .vegetarian:
            return "🥗"
        case .vegan:
            return "🌱"
        }
    }
}

// MARK: - AI Recipe Request/Response Models

struct RecipeRequest: Codable {
    let availableIngredients: [String]
    let expiringIngredients: [String]
    let preferences: RecipePreferences
    let maxRecipes: Int

    init(
        availableIngredients: [String],
        expiringIngredients: [String],
        preferences: RecipePreferences = RecipePreferences(),
        maxRecipes: Int = 3
    ) {
        self.availableIngredients = availableIngredients
        self.expiringIngredients = expiringIngredients
        self.preferences = preferences
        self.maxRecipes = maxRecipes
    }
}

struct RecipePreferences: Codable {
    let difficulty: RecipeDifficulty?
    let maxTime: Int? // minutes
    let cuisine: CuisineType?
    let excludeIngredients: [String]
    let dietaryRestrictions: [String]

    init(
        difficulty: RecipeDifficulty? = nil,
        maxTime: Int? = nil,
        cuisine: CuisineType? = nil,
        excludeIngredients: [String] = [],
        dietaryRestrictions: [String] = []
    ) {
        self.difficulty = difficulty
        self.maxTime = maxTime
        self.cuisine = cuisine
        self.excludeIngredients = excludeIngredients
        self.dietaryRestrictions = dietaryRestrictions
    }
}

struct AIRecipeResponse: Codable {
    let recipes: [Recipe]
    let missingIngredients: [String]
    let suggestions: [String]
    let generatedAt: Date

    init(
        recipes: [Recipe],
        missingIngredients: [String] = [],
        suggestions: [String] = [],
        generatedAt: Date = Date()
    ) {
        self.recipes = recipes
        self.missingIngredients = missingIngredients
        self.suggestions = suggestions
        self.generatedAt = generatedAt
    }
}
