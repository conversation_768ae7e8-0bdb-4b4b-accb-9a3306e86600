//
//  Household.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import Foundation

struct Household: Codable, Identifiable, Equatable {
    let id: UUID
    let name: String
    let description: String?
    let inviteCode: String
    let createdAt: Date
    let createdBy: UUID

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case description
        case inviteCode = "invite_code"
        case createdAt = "created_at"
        case createdBy = "created_by"
    }
}

struct UserHousehold: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let householdId: UUID
    let role: HouseholdRole
    let joinedAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case householdId = "household_id"
        case role
        case joinedAt = "joined_at"
    }
}

enum HouseholdRole: String, Codable, CaseIterable {
    case owner = "owner"
    case member = "member"

    var displayName: String {
        switch self {
        case .owner:
            return "Ev Sahibi"
        case .member:
            return "Üye"
        }
    }
}

struct HouseholdMember: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let householdId: UUID
    let role: HouseholdRole
    let joinedAt: Date
    let user: User

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case householdId = "household_id"
        case role
        case joinedAt = "joined_at"
        case user = "users"
    }
}
