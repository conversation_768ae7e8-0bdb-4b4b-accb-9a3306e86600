//
//  User.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import Foundation

struct User: Codable, Identifiable {
    let id: UUID
    let email: String
    let fullName: String
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case fullName = "full_name"
        case createdAt = "created_at"
    }
}

struct UserProfile: Codable, Identifiable {
    let id: UUID
    let email: String
    let fullName: String?
    let avatarUrl: String?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case fullName = "full_name"
        case avatarUrl = "avatar_url"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    // Display name for UI
    var displayName: String {
        if let fullName = fullName, !fullName.isEmpty {
            return fullName
        }
        return email.components(separatedBy: "@").first ?? email
    }

    // Initials for avatar
    var initials: String {
        if let fullName = fullName, !fullName.isEmpty {
            let components = fullName.components(separatedBy: " ")
            let firstInitial = components.first?.first?.uppercased() ?? ""
            let lastInitial = components.count > 1 ? components.last?.first?.uppercased() ?? "" : ""
            return firstInitial + lastInitial
        }
        return email.first?.uppercased() ?? "U"
    }
}
