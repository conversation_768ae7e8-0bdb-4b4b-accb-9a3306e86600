//
//  ProductListEnums.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 11.06.2025.
//

import Foundation

// MARK: - Product Sort Options
enum ProductSortOption: String, CaseIterable {
    case expiryDateAsc = "expiry_date_asc"
    case expiryDateDesc = "expiry_date_desc"
    case nameAsc = "name_asc"
    case nameDesc = "name_desc"
    case quantityAsc = "quantity_asc"
    case quantityDesc = "quantity_desc"
    case addedDateAsc = "added_date_asc"
    case addedDateDesc = "added_date_desc"

    var displayName: String {
        switch self {
        case .expiryDateAsc:
            return "Son Kullanma ↑"
        case .expiryDateDesc:
            return "Son Kullanma ↓"
        case .nameAsc:
            return "İsim A-Z"
        case .nameDesc:
            return "İsim Z-A"
        case .quantityAsc:
            return "Miktar ↑"
        case .quantityDesc:
            return "Miktar ↓"
        case .addedDateAsc:
            return "<PERSON><PERSON> E<PERSON>"
        case .addedDateDesc:
            return "<PERSON><PERSON>"
        }
    }

    var icon: String {
        switch self {
        case .expiryDateAsc, .expiryDateDesc:
            return "calendar"
        case .nameAsc, .nameDesc:
            return "textformat.abc"
        case .quantityAsc, .quantityDesc:
            return "number"
        case .addedDateAsc, .addedDateDesc:
            return "clock"
        }
    }
}

// MARK: - View Mode
enum ViewMode: String, CaseIterable {
    case card = "card"
    case compact = "compact"
    case list = "list"
    
    var displayName: String {
        switch self {
        case .card:
            return "Kart"
        case .compact:
            return "Kompakt"
        case .list:
            return "Liste"
        }
    }
    
    var icon: String {
        switch self {
        case .card:
            return "rectangle.grid.1x2"
        case .compact:
            return "rectangle.grid.2x2"
        case .list:
            return "list.bullet"
        }
    }
}

// MARK: - Priority Level
enum PriorityLevel: Int, CaseIterable {
    case critical = 0   // 0-1 gün
    case urgent = 1     // 1-3 gün
    case normal = 2     // 3-7 gün
    case safe = 3       // 7+ gün
    
    var displayName: String {
        switch self {
        case .critical:
            return "Kritik"
        case .urgent:
            return "Acil"
        case .normal:
            return "Normal"
        case .safe:
            return "Güvenli"
        }
    }
    
    var color: String {
        switch self {
        case .critical:
            return "red"
        case .urgent:
            return "orange"
        case .normal:
            return "yellow"
        case .safe:
            return "green"
        }
    }
    
    var icon: String {
        switch self {
        case .critical:
            return "exclamationmark.triangle.fill"
        case .urgent:
            return "clock.fill"
        case .normal:
            return "calendar"
        case .safe:
            return "checkmark.circle.fill"
        }
    }
    
    static func from(daysUntilExpiry: Int) -> PriorityLevel {
        switch daysUntilExpiry {
        case ...1:
            return .critical
        case 2...3:
            return .urgent
        case 4...7:
            return .normal
        default:
            return .safe
        }
    }
}

// MARK: - Product Extensions
extension Product {
    var priorityLevel: PriorityLevel {
        return PriorityLevel.from(daysUntilExpiry: daysUntilExpiry)
    }

    var isExpiringSoon: Bool {
        daysUntilExpiry <= 3
    }

    var isCritical: Bool {
        daysUntilExpiry <= 1
    }
}
