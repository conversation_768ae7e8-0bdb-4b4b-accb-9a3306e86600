import Foundation

// MARK: - Shopping List Status Enum
enum ShoppingListStatus: String, CaseIterable {
    case active = "active"
    case completed = "completed"

    var displayName: String {
        switch self {
        case .active:
            return "Aktif"
        case .completed:
            return "Tamamlanan"
        }
    }

    var icon: String {
        switch self {
        case .active:
            return "cart"
        case .completed:
            return "checkmark.circle"
        }
    }
}

struct ShoppingList: Identifiable, Codable {
    let id: UUID
    let name: String
    let description: String?
    let householdId: UUID
    let createdBy: UUID
    let isCompleted: Bool
    let createdAt: Date
    let updatedAt: Date

    // Computed property for status
    var status: ShoppingListStatus {
        return isCompleted ? .completed : .active
    }

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case description
        case householdId = "household_id"
        case createdBy = "created_by"
        case isCompleted = "is_completed"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct ShoppingListItem: Identifiable, Codable, Equatable {
    let id: UUID
    let shoppingListId: UUID
    let name: String
    let quantity: Double
    let unit: String?
    let category: String?
    let notes: String?
    let isPurchased: Bool
    let isAddedToHouse: Bool
    let addedToHouseAt: Date?
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case shoppingListId = "shopping_list_id"
        case name
        case quantity
        case unit
        case category
        case notes
        case isPurchased = "is_purchased"
        case isAddedToHouse = "is_added_to_house"
        case addedToHouseAt = "added_to_house_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// Create Shopping List Request
struct CreateShoppingListRequest: Codable {
    let name: String
    let description: String?
    let householdId: UUID
    let createdBy: UUID

    enum CodingKeys: String, CodingKey {
        case name
        case description
        case householdId = "household_id"
        case createdBy = "created_by"
    }
}

// Create Shopping List Item Request
struct CreateShoppingListItemRequest: Codable {
    let shoppingListId: UUID
    let name: String
    let quantity: Double
    let unit: String?
    let category: String?
    let notes: String?

    enum CodingKeys: String, CodingKey {
        case shoppingListId = "shopping_list_id"
        case name
        case quantity
        case unit
        case category
        case notes
    }
}

// Update Shopping List Request
struct UpdateShoppingListRequest: Codable {
    let name: String?
    let description: String?
    let isCompleted: Bool?

    enum CodingKeys: String, CodingKey {
        case name
        case description
        case isCompleted = "is_completed"
    }
}

// Update Shopping List Item Request
struct UpdateShoppingListItemRequest: Codable {
    let name: String?
    let quantity: Double?
    let unit: String?
    let category: String?
    let notes: String?
    let isPurchased: Bool?
    let isAddedToHouse: Bool?
    let addedToHouseAt: Date?

    enum CodingKeys: String, CodingKey {
        case name
        case quantity
        case unit
        case category
        case notes
        case isPurchased = "is_purchased"
        case isAddedToHouse = "is_added_to_house"
        case addedToHouseAt = "added_to_house_at"
    }
}
