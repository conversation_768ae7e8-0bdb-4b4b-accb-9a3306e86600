//
//  Product.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import Foundation

struct Product: Codable, Identifiable, Equatable {
    let id: UUID
    let householdId: UUID
    let name: String
    let category: String?
    let storageLocation: String?
    let expiryDate: Date
    let purchaseDate: Date?
    let originalQuantity: Int
    let currentQuantity: Int
    let unit: String?
    let notes: String?
    let caloriesPer100g: Int? // Kalori bilgisi (100g başına)
    let addedBy: UUID
    let shoppingListItemId: UUID?
    let createdAt: Date
    let updatedAt: Date

    // Explicit init to ensure parameter order
    init(id: UUID, householdId: UUID, name: String, category: String?, storageLocation: String?, expiryDate: Date, purchaseDate: Date?, originalQuantity: Int, currentQuantity: Int, unit: String?, notes: String?, caloriesPer100g calories: Int?, addedBy: UUID, shoppingListItemId: UUID?, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.householdId = householdId
        self.name = name
        self.category = category
        self.storageLocation = storageLocation
        self.expiryDate = expiryDate
        self.purchaseDate = purchaseDate
        self.originalQuantity = originalQuantity
        self.currentQuantity = currentQuantity
        self.unit = unit
        self.notes = notes
        self.caloriesPer100g = caloriesPer100g
        self.addedBy = addedBy
        self.shoppingListItemId = shoppingListItemId
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    enum CodingKeys: String, CodingKey {
        case id
        case householdId = "household_id"
        case name
        case category
        case storageLocation = "storage_location"
        case expiryDate = "expiry_date"
        case purchaseDate = "purchase_date"
        case originalQuantity = "original_quantity"
        case currentQuantity = "current_quantity"
        case unit
        case notes
        case caloriesPer100g = "calories_per_100g"
        case addedBy = "added_by"
        case shoppingListItemId = "shopping_list_item_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    // Custom date formatters
    private static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone(abbreviation: "UTC")
        return formatter
    }()

    private static let timestampFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"
        formatter.timeZone = TimeZone(abbreviation: "UTC")
        return formatter
    }()

    private static let iso8601Formatter: ISO8601DateFormatter = {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter
    }()

    init(id: UUID, householdId: UUID, name: String, category: String?, storageLocation: String?, expiryDate: Date, purchaseDate: Date?, originalQuantity: Int, currentQuantity: Int, unit: String?, notes: String?, addedBy: UUID, shoppingListItemId: UUID?, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.householdId = householdId
        self.name = name
        self.category = category
        self.storageLocation = storageLocation
        self.expiryDate = expiryDate
        self.purchaseDate = purchaseDate
        self.originalQuantity = originalQuantity
        self.currentQuantity = currentQuantity
        self.unit = unit
        self.notes = notes
        self.caloriesPer100g = caloriesPer100g
        self.addedBy = addedBy
        self.shoppingListItemId = shoppingListItemId
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(UUID.self, forKey: .id)
        householdId = try container.decode(UUID.self, forKey: .householdId)
        name = try container.decode(String.self, forKey: .name)
        category = try container.decodeIfPresent(String.self, forKey: .category)
        storageLocation = try container.decodeIfPresent(String.self, forKey: .storageLocation)
        originalQuantity = try container.decode(Int.self, forKey: .originalQuantity)
        currentQuantity = try container.decode(Int.self, forKey: .currentQuantity)
        unit = try container.decodeIfPresent(String.self, forKey: .unit)
        notes = try container.decodeIfPresent(String.self, forKey: .notes)
        caloriesPer100g = try container.decodeIfPresent(Int.self, forKey: .caloriesPer100g)
        addedBy = try container.decode(UUID.self, forKey: .addedBy)
        shoppingListItemId = try container.decodeIfPresent(UUID.self, forKey: .shoppingListItemId)

        // Decode expiry_date (date format)
        let expiryDateString = try container.decode(String.self, forKey: .expiryDate)
        if let date = Self.dateFormatter.date(from: expiryDateString) {
            expiryDate = date
        } else {
            throw DecodingError.dataCorrupted(DecodingError.Context(
                codingPath: decoder.codingPath,
                debugDescription: "Invalid date format for expiry_date: \(expiryDateString)"
            ))
        }

        // Decode purchase_date (date format, optional)
        if let purchaseDateString = try container.decodeIfPresent(String.self, forKey: .purchaseDate) {
            if let date = Self.dateFormatter.date(from: purchaseDateString) {
                purchaseDate = date
            } else {
                purchaseDate = nil
            }
        } else {
            purchaseDate = nil
        }

        // Decode created_at (timestamp format)
        let createdAtString = try container.decode(String.self, forKey: .createdAt)
        if let date = Self.iso8601Formatter.date(from: createdAtString) {
            createdAt = date
        } else if let date = Self.timestampFormatter.date(from: createdAtString) {
            createdAt = date
        } else {
            createdAt = Date()
        }

        // Decode updated_at (timestamp format)
        let updatedAtString = try container.decode(String.self, forKey: .updatedAt)
        if let date = Self.iso8601Formatter.date(from: updatedAtString) {
            updatedAt = date
        } else if let date = Self.timestampFormatter.date(from: updatedAtString) {
            updatedAt = date
        } else {
            updatedAt = Date()
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(householdId, forKey: .householdId)
        try container.encode(name, forKey: .name)
        try container.encodeIfPresent(category, forKey: .category)
        try container.encodeIfPresent(storageLocation, forKey: .storageLocation)
        try container.encode(originalQuantity, forKey: .originalQuantity)
        try container.encode(currentQuantity, forKey: .currentQuantity)
        try container.encodeIfPresent(unit, forKey: .unit)
        try container.encodeIfPresent(notes, forKey: .notes)
        try container.encodeIfPresent(caloriesPer100g, forKey: .caloriesPer100g)
        try container.encode(addedBy, forKey: .addedBy)
        try container.encodeIfPresent(shoppingListItemId, forKey: .shoppingListItemId)

        // Encode dates in correct formats
        try container.encode(Self.dateFormatter.string(from: expiryDate), forKey: .expiryDate)
        if let purchaseDate = purchaseDate {
            try container.encode(Self.dateFormatter.string(from: purchaseDate), forKey: .purchaseDate)
        }
        try container.encode(Self.iso8601Formatter.string(from: createdAt), forKey: .createdAt)
        try container.encode(Self.iso8601Formatter.string(from: updatedAt), forKey: .updatedAt)
    }

    // Computed properties for UI
    var daysUntilExpiry: Int {
        Calendar.current.dateComponents([.day], from: Date(), to: expiryDate).day ?? 0
    }

    var expiryStatus: ExpiryStatus {
        // Tamamen tüketilmiş ürünler için özel durum
        if currentQuantity <= 0 {
            return .consumed
        }

        let days = daysUntilExpiry
        if days < 0 {
            return .expired
        } else if days <= 2 {
            return .expiringSoon
        } else if days <= 5 {
            return .expiringThisWeek
        } else {
            return .fresh
        }
    }

    var quantityWithUnit: String {
        if let unit = unit, let productUnit = ProductUnit(rawValue: unit) {
            return "\(currentQuantity) \(productUnit.shortName)"
        } else {
            return "\(currentQuantity) adet"
        }
    }

    var originalQuantityWithUnit: String {
        if let unit = unit, let productUnit = ProductUnit(rawValue: unit) {
            return "\(originalQuantity) \(productUnit.shortName)"
        } else {
            return "\(originalQuantity) adet"
        }
    }
}

enum ConsumptionStatus: String, CaseIterable {
    case active = "active"
    case partiallyConsumed = "partially_consumed"
    case fullyConsumed = "fully_consumed"
    case wasted = "wasted"
    case partiallyWasted = "partially_wasted"

    var displayName: String {
        switch self {
        case .active:
            return "Aktif"
        case .partiallyConsumed:
            return "Kısmen Tüketildi"
        case .fullyConsumed:
            return "Tamamen Tüketildi"
        case .wasted:
            return "İsraf Edildi"
        case .partiallyWasted:
            return "Kısmen İsraf"
        }
    }

    var icon: String {
        switch self {
        case .active:
            return "🟢"
        case .partiallyConsumed:
            return "🟡"
        case .fullyConsumed:
            return "✅"
        case .wasted:
            return "🗑️"
        case .partiallyWasted:
            return "⚠️"
        }
    }

    var color: String {
        switch self {
        case .active:
            return "green"
        case .partiallyConsumed:
            return "orange"
        case .fullyConsumed:
            return "blue"
        case .wasted:
            return "red"
        case .partiallyWasted:
            return "yellow"
        }
    }
}

enum ExpiryStatus: CaseIterable {
    case expired
    case expiringSoon
    case expiringThisWeek
    case fresh
    case consumed

    var displayName: String {
        switch self {
        case .expired:
            return "Süresi Geçmiş"
        case .expiringSoon:
            return "Yakında Bitecek"
        case .expiringThisWeek:
            return "Bu Hafta Bitecek"
        case .fresh:
            return "Taze"
        case .consumed:
            return "Tüketildi"
        }
    }

    var colorName: String {
        switch self {
        case .expired:
            return "red"
        case .expiringSoon:
            return "orange"
        case .expiringThisWeek:
            return "yellow"
        case .fresh:
            return "green"
        case .consumed:
            return "blue"
        }
    }
}

enum ProductUnit: String, CaseIterable {
    case piece = "piece"
    case kg = "kg"
    case gram = "gram"
    case liter = "liter"
    case ml = "ml"
    case pack = "pack"
    case bottle = "bottle"
    case can = "can"
    case box = "box"
    case bag = "bag"

    var displayName: String {
        switch self {
        case .piece:
            return "Adet"
        case .kg:
            return "Kg"
        case .gram:
            return "Gram"
        case .liter:
            return "Litre"
        case .ml:
            return "ML"
        case .pack:
            return "Paket"
        case .bottle:
            return "Şişe"
        case .can:
            return "Kutu"
        case .box:
            return "Kutu"
        case .bag:
            return "Torba"
        }
    }

    var shortName: String {
        switch self {
        case .piece:
            return "adet"
        case .kg:
            return "kg"
        case .gram:
            return "gr"
        case .liter:
            return "lt"
        case .ml:
            return "ml"
        case .pack:
            return "paket"
        case .bottle:
            return "şişe"
        case .can:
            return "kutu"
        case .box:
            return "kutu"
        case .bag:
            return "torba"
        }
    }

    static func fromString(_ string: String) -> ProductUnit {
        switch string {
        case "kg": return .kg
        case "gram": return .gram
        case "liter": return .liter
        case "ml": return .ml
        case "pack": return .pack
        case "box": return .box
        case "bottle": return .bottle
        case "can": return .can
        case "bag": return .bag
        default: return .piece
        }
    }
}

enum ProductCategory: String, CaseIterable {
    case dairy = "dairy"
    case meat = "meat"
    case vegetables = "vegetables"
    case fruits = "fruits"
    case grains = "grains"
    case beverages = "beverages"
    case snacks = "snacks"
    case frozen = "frozen"
    case canned = "canned"
    case other = "other"

    var displayName: String {
        switch self {
        case .dairy:
            return "Süt Ürünleri"
        case .meat:
            return "Et & Tavuk"
        case .vegetables:
            return "Sebzeler"
        case .fruits:
            return "Meyveler"
        case .grains:
            return "Tahıllar"
        case .beverages:
            return "İçecekler"
        case .snacks:
            return "Atıştırmalık"
        case .frozen:
            return "Donmuş"
        case .canned:
            return "Konserve"
        case .other:
            return "Diğer"
        }
    }

    var icon: String {
        switch self {
        case .dairy:
            return "🥛"
        case .meat:
            return "🥩"
        case .vegetables:
            return "🥬"
        case .fruits:
            return "🍎"
        case .grains:
            return "🌾"
        case .beverages:
            return "🥤"
        case .snacks:
            return "🍿"
        case .frozen:
            return "🧊"
        case .canned:
            return "🥫"
        case .other:
            return "📦"
        }
    }

    static func fromString(_ string: String) -> ProductCategory {
        switch string {
        case "dairy": return .dairy
        case "meat": return .meat
        case "vegetables": return .vegetables
        case "fruits": return .fruits
        case "grains": return .grains
        case "beverages": return .beverages
        case "snacks": return .snacks
        case "frozen": return .frozen
        case "canned": return .canned
        default: return .other
        }
    }
}

enum StorageLocation: String, CaseIterable {
    case refrigerator = "refrigerator"
    case freezer = "freezer"
    case pantry = "pantry"
    case cupboard = "cupboard"
    case countertop = "countertop"
    case cellar = "cellar"
    case other = "other"

    var displayName: String {
        switch self {
        case .refrigerator:
            return "Buzdolabı"
        case .freezer:
            return "Dondurucu"
        case .pantry:
            return "Kiler"
        case .cupboard:
            return "Dolap"
        case .countertop:
            return "Tezgah"
        case .cellar:
            return "Mahzen"
        case .other:
            return "Diğer"
        }
    }

    var icon: String {
        switch self {
        case .refrigerator:
            return "❄️"
        case .freezer:
            return "🧊"
        case .pantry:
            return "🏠"
        case .cupboard:
            return "🗄️"
        case .countertop:
            return "🍽️"
        case .cellar:
            return "🏚️"
        case .other:
            return "📍"
        }
    }
}

// MARK: - Kalori Hesaplamaları
extension Product {
    /// Mevcut miktardaki toplam kalori
    var totalCalories: Int? {
        guard let caloriesPer100g = caloriesPer100g else { return nil }

        // Birim türüne göre kalori hesaplama
        switch unit?.lowercased() {
        case "kg", "kilogram":
            return Int(Double(currentQuantity) * 1000.0 * Double(caloriesPer100g) / 100.0)
        case "g", "gram":
            return Int(Double(currentQuantity) * Double(caloriesPer100g) / 100.0)
        case "adet", "piece", "pieces":
            // Adet için ortalama 100g varsayalım (bu ürün türüne göre değişebilir)
            return Int(Double(currentQuantity) * 100.0 * Double(caloriesPer100g) / 100.0)
        case "l", "lt", "litre", "liter":
            // Sıvılar için 1L = 1000g varsayalım
            return Int(Double(currentQuantity) * 1000.0 * Double(caloriesPer100g) / 100.0)
        case "ml", "mililitre", "milliliter":
            return Int(Double(currentQuantity) * Double(caloriesPer100g) / 100.0)
        default:
            // Bilinmeyen birim için 100g varsayalım
            return Int(Double(currentQuantity) * 100.0 * Double(caloriesPer100g) / 100.0)
        }
    }

    /// Orijinal miktardaki toplam kalori
    var originalTotalCalories: Int? {
        guard let caloriesPer100g = caloriesPer100g else { return nil }

        switch unit?.lowercased() {
        case "kg", "kilogram":
            return Int(Double(originalQuantity) * 1000.0 * Double(caloriesPer100g) / 100.0)
        case "g", "gram":
            return Int(Double(originalQuantity) * Double(caloriesPer100g) / 100.0)
        case "adet", "piece", "pieces":
            return Int(Double(originalQuantity) * 100.0 * Double(caloriesPer100g) / 100.0)
        case "l", "lt", "litre", "liter":
            return Int(Double(originalQuantity) * 1000.0 * Double(caloriesPer100g) / 100.0)
        case "ml", "mililitre", "milliliter":
            return Int(Double(originalQuantity) * Double(caloriesPer100g) / 100.0)
        default:
            return Int(Double(originalQuantity) * 100.0 * Double(caloriesPer100g) / 100.0)
        }
    }

    /// Tüketilen kalori miktarı
    var consumedCalories: Int? {
        guard let originalTotal = originalTotalCalories,
              let currentTotal = totalCalories else { return nil }
        return originalTotal - currentTotal
    }
}
