//
//  Consumption.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 28.05.2025.
//

import Foundation

struct Consumption: Codable, Identifiable, Equatable {
    let id: UUID
    let productId: UUID
    let userId: UUID
    let householdId: UUID
    let consumedQuantity: Int
    let wastedQuantity: Int
    let consumptionType: String
    let notes: String?
    let consumedAt: Date
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case productId = "product_id"
        case userId = "user_id"
        case householdId = "household_id"
        case consumedQuantity = "consumed_quantity"
        case wastedQuantity = "wasted_quantity"
        case consumptionType = "consumption_type"
        case notes
        case consumedAt = "consumed_at"
        case createdAt = "created_at"
    }

    // Custom date formatters
    private static let iso8601Formatter: ISO8601DateFormatter = {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter
    }()

    private static let timestampFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSSSSS+00"
        formatter.timeZone = TimeZone(abbreviation: "UTC")
        return formatter
    }()

    init(id: UUID, productId: UUID, userId: UUID, householdId: UUID, consumedQuantity: Int, wastedQuantity: Int, consumptionType: String, notes: String?, consumedAt: Date, createdAt: Date) {
        self.id = id
        self.productId = productId
        self.userId = userId
        self.householdId = householdId
        self.consumedQuantity = consumedQuantity
        self.wastedQuantity = wastedQuantity
        self.consumptionType = consumptionType
        self.notes = notes
        self.consumedAt = consumedAt
        self.createdAt = createdAt
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(UUID.self, forKey: .id)
        productId = try container.decode(UUID.self, forKey: .productId)
        userId = try container.decode(UUID.self, forKey: .userId)
        householdId = try container.decode(UUID.self, forKey: .householdId)
        consumedQuantity = try container.decode(Int.self, forKey: .consumedQuantity)
        wastedQuantity = try container.decode(Int.self, forKey: .wastedQuantity)
        consumptionType = try container.decode(String.self, forKey: .consumptionType)
        notes = try container.decodeIfPresent(String.self, forKey: .notes)

        // Decode consumed_at (timestamp format)
        let consumedAtString = try container.decode(String.self, forKey: .consumedAt)
        if let date = Self.iso8601Formatter.date(from: consumedAtString) {
            consumedAt = date
        } else if let date = Self.timestampFormatter.date(from: consumedAtString) {
            consumedAt = date
        } else {
            consumedAt = Date()
        }

        // Decode created_at (timestamp format)
        let createdAtString = try container.decode(String.self, forKey: .createdAt)
        if let date = Self.iso8601Formatter.date(from: createdAtString) {
            createdAt = date
        } else if let date = Self.timestampFormatter.date(from: createdAtString) {
            createdAt = date
        } else {
            createdAt = Date()
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(productId, forKey: .productId)
        try container.encode(userId, forKey: .userId)
        try container.encode(householdId, forKey: .householdId)
        try container.encode(consumedQuantity, forKey: .consumedQuantity)
        try container.encode(wastedQuantity, forKey: .wastedQuantity)
        try container.encode(consumptionType, forKey: .consumptionType)
        try container.encodeIfPresent(notes, forKey: .notes)

        // Encode dates in ISO8601 format
        try container.encode(Self.iso8601Formatter.string(from: consumedAt), forKey: .consumedAt)
        try container.encode(Self.iso8601Formatter.string(from: createdAt), forKey: .createdAt)
    }
}

enum ConsumptionType: String, CaseIterable {
    case consumed = "consumed"
    case wasted = "wasted"
    case mixed = "mixed"

    var displayName: String {
        switch self {
        case .consumed:
            return "Tüketildi"
        case .wasted:
            return "İsraf Edildi"
        case .mixed:
            return "Karışık"
        }
    }

    var icon: String {
        switch self {
        case .consumed:
            return "✅"
        case .wasted:
            return "🗑️"
        case .mixed:
            return "⚠️"
        }
    }

    var color: String {
        switch self {
        case .consumed:
            return "green"
        case .wasted:
            return "red"
        case .mixed:
            return "orange"
        }
    }
}

// MARK: - Consumption Extensions
extension Consumption {
    var totalQuantity: Int {
        return consumedQuantity + wastedQuantity
    }

    var currentConsumptionType: ConsumptionType {
        return ConsumptionType(rawValue: consumptionType) ?? .mixed
    }

    var efficiencyPercentage: Double {
        let total = totalQuantity
        if total == 0 { return 0.0 }
        return Double(consumedQuantity) / Double(total) * 100.0
    }

    var wastePercentage: Double {
        let total = totalQuantity
        if total == 0 { return 0.0 }
        return Double(wastedQuantity) / Double(total) * 100.0
    }

    var quantityDescription: String {
        if consumedQuantity > 0 && wastedQuantity > 0 {
            return "\(consumedQuantity) tüketildi, \(wastedQuantity) israf"
        } else if consumedQuantity > 0 {
            return "\(consumedQuantity) tüketildi"
        } else if wastedQuantity > 0 {
            return "\(wastedQuantity) israf edildi"
        } else {
            return "Miktar belirtilmedi"
        }
    }
}

// MARK: - Product Extensions for Consumption
extension Product {
    // Calculate total consumed quantity from consumptions
    func totalConsumedQuantity(from consumptions: [Consumption]) -> Int {
        return consumptions
            .filter { $0.productId == self.id }
            .reduce(0) { $0 + $1.consumedQuantity }
    }

    // Calculate total wasted quantity from consumptions
    func totalWastedQuantity(from consumptions: [Consumption]) -> Int {
        return consumptions
            .filter { $0.productId == self.id }
            .reduce(0) { $0 + $1.wastedQuantity }
    }

    // Calculate remaining quantity (now just returns currentQuantity)
    func remainingQuantity(from consumptions: [Consumption]) -> Int {
        return currentQuantity
    }

    // Get consumption status based on current quantity
    func consumptionStatus(from consumptions: [Consumption]) -> ConsumptionStatus {
        let totalConsumed = totalConsumedQuantity(from: consumptions)
        let totalWasted = totalWastedQuantity(from: consumptions)
        let totalUsed = totalConsumed + totalWasted

        if totalUsed == 0 {
            return .active
        } else if currentQuantity == 0 {
            if totalWasted > 0 && totalConsumed > 0 {
                return .partiallyWasted
            } else if totalWasted >= originalQuantity {
                return .wasted
            } else {
                return .fullyConsumed
            }
        } else {
            return .partiallyConsumed
        }
    }

    // Get consumptions for this product
    func getConsumptions(from consumptions: [Consumption]) -> [Consumption] {
        return consumptions
            .filter { $0.productId == self.id }
            .sorted { $0.consumedAt > $1.consumedAt }
    }
}
