import Foundation

struct FavoriteRecipe: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let recipeName: String
    let recipeData: Data // JSON encoded Recipe object
    let createdAt: Date
    let updatedAt: Date

    init(id: UUID = UUID(), userId: UUID, recipeName: String, recipeData: Data, createdAt: Date = Date(), updatedAt: Date = Date()) {
        self.id = id
        self.userId = userId
        self.recipeName = recipeName
        self.recipeData = recipeData
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // CodingKeys for snake_case to camelCase conversion
    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case recipeName = "recipe_name"
        case recipeData = "recipe_data"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// Helper extension to convert Recipe to/from FavoriteRecipe
extension FavoriteRecipe {
    init(from recipe: Recipe, userId: UUID) throws {
        let encoder = JSONEncoder()
        let recipeData = try encoder.encode(recipe)

        self.init(
            userId: userId,
            recipeName: recipe.name,
            recipeData: recipeData
        )
    }

    func toRecipe() throws -> Recipe {
        let decoder = JSONDecoder()
        return try decoder.decode(Recipe.self, from: recipeData)
    }
}
