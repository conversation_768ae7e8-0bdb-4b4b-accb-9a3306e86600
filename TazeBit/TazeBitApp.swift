//
//  TazeBitApp.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI
import UserNotifications

@main
struct TazeBitApp: App {
    @StateObject private var notificationManager = NotificationManager.shared
    @StateObject private var trackingManager = TrackingManager.shared
    private let notificationDelegate = NotificationDelegate()

    init() {
        // Bildirim delegate'ini ayarla
        UNUserNotificationCenter.current().delegate = notificationDelegate
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(notificationManager)
                .environmentObject(trackingManager)
                .environment(\.locale, Locale(identifier: "tr_TR"))
                .onAppear {
                    // Uygulama açıldığında bildirim durumunu kontrol et
                    notificationManager.checkAuthorizationStatus()
                    // Tracking durumunu güncelle
                    trackingManager.updateTrackingStatus()
                }
        }
    }
}
