//
//  CustomTextField.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct CustomTextField: View {
    let title: String
    @Binding var text: String
    let icon: String
    var isSecure: Bool = false
    var keyboardType: UIKeyboardType = .default
    var autocapitalization: UITextAutocapitalizationType = .sentences

    @FocusState private var isFocused: Bool
    @State private var isPasswordVisible: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .foregroundColor(.white.opacity(0.7))
                    .frame(width: 20)

                if isSecure && !isPasswordVisible {
                    SecureField(title, text: $text)
                        .focused($isFocused)
                        .textFieldStyle(PlainTextFieldStyle())
                        .foregroundColor(.white)
                        .autocapitalization(.none)
                } else {
                    TextField(title, text: $text)
                        .focused($isFocused)
                        .textFieldStyle(PlainTextFieldStyle())
                        .foregroundColor(.white)
                        .keyboardType(keyboardType)
                        .autocapitalization(isSecure ? .none : autocapitalization)
                }

                // Password visibility toggle button
                if isSecure {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isPasswordVisible.toggle()
                        }
                    }) {
                        Image(systemName: isPasswordVisible ? "eye.slash.fill" : "eye.fill")
                            .foregroundColor(.white.opacity(0.7))
                            .frame(width: 20, height: 20)
                            .contentTransition(.symbolEffect(.replace))
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(isFocused ? 0.25 : 0.15))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(isFocused ? 0.6 : 0.3), lineWidth: 1)
                    )
            )
            .animation(.easeInOut(duration: 0.2), value: isFocused)
        }
    }
}

struct CustomButton: View {
    let title: String
    let action: () -> Void
    var isLoading: Bool = false
    var style: ButtonStyle = .primary

    enum ButtonStyle {
        case primary
        case secondary
        case outline
    }

    var body: some View {
        Button(action: action) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: textColor))
                        .scaleEffect(0.8)
                }

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(backgroundColor)
            .foregroundColor(textColor)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: style == .outline ? 2 : 0)
            )
        }
        .disabled(isLoading)
        .opacity(isLoading ? 0.7 : 1.0)
    }

    private var backgroundColor: Color {
        switch style {
        case .primary:
            return Color.white.opacity(0.9)
        case .secondary:
            return Color.white.opacity(0.2)
        case .outline:
            return Color.clear
        }
    }

    private var textColor: Color {
        switch style {
        case .primary:
            return Color.blue
        case .secondary, .outline:
            return Color.white
        }
    }

    private var borderColor: Color {
        switch style {
        case .outline:
            return Color.white.opacity(0.6)
        default:
            return Color.clear
        }
    }
}

struct CustomTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(16)
            .background(Color.cardBackground)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.borderColor, lineWidth: 1)
            )
    }
}

#Preview {
    ZStack {
        GradientBackground.tazeBitPrimary

        VStack(spacing: 20) {
            CustomTextField(
                title: "Email",
                text: .constant(""),
                icon: "envelope",
                keyboardType: .emailAddress,
                autocapitalization: .none
            )

            CustomTextField(
                title: "Şifre",
                text: .constant("test123"),
                icon: "lock",
                isSecure: true
            )

            CustomButton(title: "Giriş Yap", action: {})
            CustomButton(title: "Kayıt Ol", action: {}, style: .outline)
        }
        .padding()
    }
}
