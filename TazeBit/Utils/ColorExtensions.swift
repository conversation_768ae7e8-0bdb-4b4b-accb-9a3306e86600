//
//  ColorExtensions.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

extension Color {
    // MARK: - Gradient Colors (using auto-generated asset colors)
    static let gradientColors: [Color] = [
        Color("GradientStart"),
        Color("GradientEnd")
    ]

    // MARK: - Status Colors
    static let statusExpired = Color.red
    static let statusExpiringSoon = Color.orange
    static let statusExpiringWeek = Color.yellow
    static let statusFresh = Color.green

    // MARK: - UI Colors
    static let appBackground = Color(.systemGroupedBackground)
    static let cardBackground = Color(.systemBackground)
    static let cardShadow = Color.black.opacity(0.1)
    static let textPrimary = Color.primary
    static let textSecondary = Color.secondary
    static let borderColor = Color(.systemGray4)

    // MARK: - Additional Colors for Charts
    static let brown = Color(red: 0.6, green: 0.4, blue: 0.2)
    static let cyan = Color.cyan
    static let purple = Color.purple
    static let indigo = Color.indigo
    static let gray = Color.gray

    // MARK: - Helper Methods
    static func statusColor(for status: ExpiryStatus) -> Color {
        switch status {
        case .expired:
            return .statusExpired
        case .expiringSoon:
            return .statusExpiringSoon
        case .expiringThisWeek:
            return .statusExpiringWeek
        case .fresh:
            return .statusFresh
        case .consumed:
            return .blue
        }
    }

    // MARK: - Custom Initializers
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Gradient Extensions
extension LinearGradient {
    static let tazeBitGradient = LinearGradient(
        colors: Color.gradientColors,
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    static let tazeBitVerticalGradient = LinearGradient(
        colors: Color.gradientColors,
        startPoint: .top,
        endPoint: .bottom
    )

    static let tazeBitHorizontalGradient = LinearGradient(
        colors: Color.gradientColors,
        startPoint: .leading,
        endPoint: .trailing
    )
}
