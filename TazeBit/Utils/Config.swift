//
//  Config.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 29.05.2025.
//

import Foundation

struct Config {
    // MARK: - OpenAI Configuration
    static let openAIAPIKey = "********************************************************************************************************************************************************************" // TODO: Replace with actual API key
    static let openAIBaseURL = "https://api.openai.com/v1"
    static let openAIModel = "gpt-4" // or "gpt-4" for better results

    // MARK: - Recipe AI Configuration
    static let maxRecipeSuggestions = 5
    static let maxIngredientsPerRecipe = 10
    static let recipeLanguage = "Turkish"

    // MARK: - Expiry Date Thresholds
    static let nearExpiryDays = 3 // Products expiring in 3 days
    static let urgentExpiryDays = 1 // Products expiring in 1 day

    // MARK: - AI Prompt Templates
    static let recipePromptTemplate = """
    Sen bir Türk mutfağı uzmanısın. Aşağıdaki malzemelerle yapılabilecek lezzetli yemek tarifleri öner.

    Mevcut malzemeler: {ingredients}

    Lütfen şu formatta farklı zorluk seviyelerinde 5 tarif öner (kolay, orta, zor karışık):

    **Tarif Adı:** [Yemek adı]
    **Zorluk:** [Kolay/Orta/Zor]
    **Süre:** [Hazırlık + Pişirme süresi]
    **Malzemeler:**
    - [Malzeme listesi]
    **Tarif:**
    1. [Adım 1]
    2. [Adım 2]
    ...

    **İpucu:** [Özel ipucu]

    ---

    Sadece mevcut malzemeleri kullan ve eksik malzeme varsa belirt.
    """

    static let quickRecipePromptTemplate = """
    Şu malzemelerle hızlı bir yemek tarifi öner: {ingredients}

    Sadece tarif adı, malzemeler ve kısa adımları ver. Maksimum 30 dakikada hazırlanabilsin.
    """
}

// MARK: - Environment Configuration
extension Config {
    static var isDebug: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }

    static var shouldLogAPIRequests: Bool {
        return isDebug
    }
}
