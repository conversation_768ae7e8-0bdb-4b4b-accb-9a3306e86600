//
//  GradientBackground.swift
//  TazeBit
//
//  Created by <PERSON><PERSON><PERSON> on 26.05.2025.
//

import SwiftUI

struct GradientBackground: View {
    let colors: [Color]
    let startPoint: UnitPoint
    let endPoint: UnitPoint

    init(
        colors: [Color] = [Color.blue.opacity(0.8), Color.purple.opacity(0.6), Color.pink.opacity(0.4)],
        startPoint: UnitPoint = .topLeading,
        endPoint: UnitPoint = .bottomTrailing
    ) {
        self.colors = colors
        self.startPoint = startPoint
        self.endPoint = endPoint
    }

    var body: some View {
        LinearGradient(
            colors: colors,
            startPoint: startPoint,
            endPoint: endPoint
        )
        .ignoresSafeArea()
    }
}

struct AnimatedGradientBackground: View {
    @State private var animateGradient = false

    let colors: [Color]

    init(colors: [Color] = [Color.blue.opacity(0.8), Color.purple.opacity(0.6), Color.pink.opacity(0.4)]) {
        self.colors = colors
    }

    var body: some View {
        LinearGradient(
            colors: colors,
            startPoint: animateGradient ? .topLeading : .bottomLeading,
            endPoint: animateGradient ? .bottomTrailing : .topTrailing
        )
        .ignoresSafeArea()
        .onAppear {
            withAnimation(.easeInOut(duration: 3).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
        }
    }
}

// Predefined gradient themes
extension GradientBackground {
    static let tazeBitPrimary = GradientBackground(
        colors: Color.gradientColors
    )

    static let sunset = GradientBackground(
        colors: [
            Color.orange.opacity(0.8),
            Color.pink.opacity(0.6),
            Color.purple.opacity(0.4)
        ]
    )

    static let ocean = GradientBackground(
        colors: [
            Color.blue.opacity(0.8),
            Color.cyan.opacity(0.6),
            Color.teal.opacity(0.4)
        ]
    )

    static let forest = GradientBackground(
        colors: [
            Color.green.opacity(0.8),
            Color.mint.opacity(0.6),
            Color.cyan.opacity(0.4)
        ]
    )
}
