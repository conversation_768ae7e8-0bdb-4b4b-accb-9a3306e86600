// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		3E06944C2DE4F9C60003031F /* Auth in Frameworks */ = {isa = PBXBuildFile; productRef = 3E06944B2DE4F9C60003031F /* Auth */; };
		3E06944E2DE4F9C60003031F /* Functions in Frameworks */ = {isa = PBXBuildFile; productRef = 3E06944D2DE4F9C60003031F /* Functions */; };
		3E0694502DE4F9C60003031F /* PostgREST in Frameworks */ = {isa = PBXBuildFile; productRef = 3E06944F2DE4F9C60003031F /* PostgREST */; };
		3E0694522DE4F9C60003031F /* Realtime in Frameworks */ = {isa = PBXBuildFile; productRef = 3E0694512DE4F9C60003031F /* Realtime */; };
		3E0694542DE4F9C60003031F /* Storage in Frameworks */ = {isa = PBXBuildFile; productRef = 3E0694532DE4F9C60003031F /* Storage */; };
		3E0694562DE4F9C60003031F /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = 3E0694552DE4F9C60003031F /* Supabase */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		3E0694192DE4F3F60003031F /* TazeBit.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TazeBit.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		3E06941B2DE4F3F60003031F /* TazeBit */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TazeBit;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		3E0694162DE4F3F60003031F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3E0694502DE4F9C60003031F /* PostgREST in Frameworks */,
				3E06944E2DE4F9C60003031F /* Functions in Frameworks */,
				3E0694562DE4F9C60003031F /* Supabase in Frameworks */,
				3E06944C2DE4F9C60003031F /* Auth in Frameworks */,
				3E0694542DE4F9C60003031F /* Storage in Frameworks */,
				3E0694522DE4F9C60003031F /* Realtime in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3E0694102DE4F3F60003031F = {
			isa = PBXGroup;
			children = (
				3E06941B2DE4F3F60003031F /* TazeBit */,
				3E06944A2DE4F9C60003031F /* Frameworks */,
				3E06941A2DE4F3F60003031F /* Products */,
			);
			sourceTree = "<group>";
		};
		3E06941A2DE4F3F60003031F /* Products */ = {
			isa = PBXGroup;
			children = (
				3E0694192DE4F3F60003031F /* TazeBit.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		3E06944A2DE4F9C60003031F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3E0694182DE4F3F60003031F /* TazeBit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3E0694242DE4F3F60003031F /* Build configuration list for PBXNativeTarget "TazeBit" */;
			buildPhases = (
				3E0694152DE4F3F60003031F /* Sources */,
				3E0694162DE4F3F60003031F /* Frameworks */,
				3E0694172DE4F3F60003031F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				3E06941B2DE4F3F60003031F /* TazeBit */,
			);
			name = TazeBit;
			packageProductDependencies = (
				3E06944B2DE4F9C60003031F /* Auth */,
				3E06944D2DE4F9C60003031F /* Functions */,
				3E06944F2DE4F9C60003031F /* PostgREST */,
				3E0694512DE4F9C60003031F /* Realtime */,
				3E0694532DE4F9C60003031F /* Storage */,
				3E0694552DE4F9C60003031F /* Supabase */,
			);
			productName = TazeBit;
			productReference = 3E0694192DE4F3F60003031F /* TazeBit.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3E0694112DE4F3F60003031F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					3E0694182DE4F3F60003031F = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 3E0694142DE4F3F60003031F /* Build configuration list for PBXProject "TazeBit" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 3E0694102DE4F3F60003031F;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				3E0694492DE4F9B70003031F /* XCRemoteSwiftPackageReference "supabase-swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 3E06941A2DE4F3F60003031F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				3E0694182DE4F3F60003031F /* TazeBit */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3E0694172DE4F3F60003031F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		3E0694152DE4F3F60003031F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		3E0694222DE4F3F60003031F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = M8JAQ7ZZPB;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		3E0694232DE4F3F60003031F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = M8JAQ7ZZPB;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		3E0694252DE4F3F60003031F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = M8JAQ7ZZPB;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "TazeBit uygulaması buzdolabı ve mutfak fotoğraflarını çekerek ürünleri otomatik olarak tespit etmek için kamera erişimine ihtiyaç duyar.";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "TazeBit, size daha iyi hizmet verebilmek ve kişiselleştirilmiş deneyim sunabilmek için bazı verilerinizi takip etmek istiyor.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dijikit.TazeBit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		3E0694262DE4F3F60003031F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = M8JAQ7ZZPB;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "TazeBit uygulaması buzdolabı ve mutfak fotoğraflarını çekerek ürünleri otomatik olarak tespit etmek için kamera erişimine ihtiyaç duyar.";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "TazeBit, size daha iyi hizmet verebilmek ve kişiselleştirilmiş deneyim sunabilmek için bazı verilerinizi takip etmek istiyor.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dijikit.TazeBit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3E0694142DE4F3F60003031F /* Build configuration list for PBXProject "TazeBit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3E0694222DE4F3F60003031F /* Debug */,
				3E0694232DE4F3F60003031F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3E0694242DE4F3F60003031F /* Build configuration list for PBXNativeTarget "TazeBit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3E0694252DE4F3F60003031F /* Debug */,
				3E0694262DE4F3F60003031F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		3E0694492DE4F9B70003031F /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		3E06944B2DE4F9C60003031F /* Auth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3E0694492DE4F9B70003031F /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Auth;
		};
		3E06944D2DE4F9C60003031F /* Functions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3E0694492DE4F9B70003031F /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Functions;
		};
		3E06944F2DE4F9C60003031F /* PostgREST */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3E0694492DE4F9B70003031F /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = PostgREST;
		};
		3E0694512DE4F9C60003031F /* Realtime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3E0694492DE4F9B70003031F /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Realtime;
		};
		3E0694532DE4F9C60003031F /* Storage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3E0694492DE4F9B70003031F /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Storage;
		};
		3E0694552DE4F9C60003031F /* Supabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3E0694492DE4F9B70003031F /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Supabase;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 3E0694112DE4F3F60003031F /* Project object */;
}
